import CommonList from '@/components/CommonList'
import SearchLayout from '@/components/Search/Layout'
import { Button, DatePicker, message, Select, Space } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { useAccess, useModel } from 'umi'
import AddFillInspectionModal from './AddFillInspectionModal'
import { getFillColums, getFillMetas } from './getColumns'
import SemesterSelect from '@/components/SemesterSelect'
import { PlusOutlined, UploadOutlined } from '@ant-design/icons'
import { envjudge } from '@/utils'
import {
  create,
  getAfternoonList,
  remove,
  update,
} from '@/services/edu-platform-web/noonInspection'
import dayjs from 'dayjs'

const FillInspection = () => {
  const childRef = useRef()
  const { isAdministration, isLoginAdmin } = useAccess()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  const [time, setTime] = useState<string>()
  const [loading, setLoading] = useState(false)
  const [period, setPeriod] = useState<string>()
  const [scrollHeight, setScrollHeight] = useState(0)
  const [termCode, setTermCode] = useState<string>()

  // 选择学年学期
  const [nowTermCode, setNowTermCode] = useState<string>()
  const [modalProps, setModalProps] = useState<{
    visible: boolean
    data?: any
    type: 'look' | 'edit' | 'add' | undefined
  }>({
    visible: false,
    type: undefined,
    data: undefined,
  })

  /** 删除晨午检记录 */
  const deleteTable = async (id: string) => {
    if (!id) {
      return message.warning('获取ID失败，请联系管理员或稍后再试')
    }
    const res = await remove(id)
    if (res?.errCode) {
      return message.warning('删除失败，请联系管理员或稍后再试' + ' ' + (res?.msg ?? res?.message))
    }
    message.success('删除成功')
    ;(childRef?.current as any)?.reload?.()
  }

  const handleEvent = (type: string, data: any) => {
    switch (type) {
      case 'deleteTable':
        deleteTable(data?.id)
        break
      case 'lookTable':
        setModalProps({
          visible: true,
          data,
          type: 'look',
        })
        break
      case 'editTable':
        setModalProps({
          visible: true,
          data,
          type: 'edit',
        })
        break
    }
  }
  /** 新增编辑晨午检 */
  const onSubmit = async (data: any) => {
    if (modalProps?.type === 'edit') {
      setLoading(true)
      const res = await update(modalProps?.data?.id, {
        ...data,
        check_time: undefined,
      })
      if (res?.errCode) {
        setLoading(false)
        return message.warning(
          '修改失败，请联系管理员或稍后再试' + ' ' + (res?.msg ?? res?.message),
        )
      }
      setLoading(false)
      message.success('修改成功')
    }
    if (modalProps?.type === 'add') {
      setLoading(true)
      const res = await create(data)
      if (res?.errCode) {
        setLoading(false)
        return message.warning(
          '填报失败，请联系管理员或稍后再试' + ' ' + (res?.msg ?? res?.message),
        )
      }
      setLoading(false)
      message.success('填报成功')
    }
    setModalProps({
      visible: false,
      type: 'add',
      data: undefined,
    })
    ;(childRef?.current as any)?.reload?.()
  }

  const handleDownload = async () => {
    try {
      const baseUrl = '/edu_api/download/morning_check'
      const params = new URLSearchParams()
      params.append('enterpriseCode', schoolInfo?.code || '')
      params.append('enterpriseName', schoolInfo?.name || '')
      params.append('semesterCode', termCode || '')
      if (time) {
        params.append('check_time', time)
      }
      if (period) {
        params.append('period', period)
      }
      const url = `${baseUrl}?${params.toString()}`
      const a = document.createElement('a')
      a.href = url
      a.download = '晨午检填报记录.xlsx'
      document.body.appendChild(a)
      a.click()
      a.remove()
      message.success('导出成功')
    } catch (error) {
      message.error('导出失败，请联系管理员或稍后再试！' + error)
    }
  }

  useEffect(() => {
    const calculateHeight = () => {
      const headerHeight = document.querySelector('.ant-page-header')?.clientHeight || 64
      const searchHeight = document.querySelector('.ant-pro-table-search')?.clientHeight || 56
      const padding = 24
      setScrollHeight(window.innerHeight - headerHeight - searchHeight - padding - 170)
    }
    calculateHeight()
    window.addEventListener('resize', calculateHeight)
    return () => window.removeEventListener('resize', calculateHeight)
  }, [])

  return (
    <div>
      <CommonList
        ref={childRef}
        columns={getFillColums({ handleEvent })}
        metas={getFillMetas({ handleEvent })}
        scroll={{
          y: scrollHeight,
        }}
        params={{
          schoolInfo,
          termCode,
          time,
          period,
        }}
        request={async (params) => {
          const { pageSize, current: page } = params
          if (!termCode) {
            return {
              data: [],
              success: false,
              total: 0,
            }
          }
          const res = await getAfternoonList({
            enterpriseCode: schoolInfo?.code || '',
            semesterCode: termCode || undefined,
            check_time: time || undefined,
            period,
            teacherCode:
              isAdministration(ENV_CWJTB, ENV_SP) && !isLoginAdmin
                ? currentUser?.userCode
                : undefined,
            offset: Number((page || 1) - 1) * Number(pageSize),
            limit: Number(pageSize),
          })
          if (res?.errCode) {
            message.warning(
              '获取晨午检列表失败，请联系管理员或稍后再试' + ' ' + (res?.msg ?? res?.message),
            )
            return {
              success: false,
              data: [],
              total: 0,
            }
          }
          res?.list?.forEach((item: any) => {
            item.key = item.id
            item.forbid = isAdministration(ENV_CWJGL, ENV_GL)
              ? false
              : dayjs(item.check_time).isBefore(dayjs(), 'day')
          })
          console.log(res?.list)

          return {
            success: true,
            data: res?.list || [],
            total: res?.total,
          }
        }}
        headerTitle={
          <SearchLayout>
            <SemesterSelect
              setNowTermCode={setNowTermCode}
              onChange={(val) => {
                setTermCode(val)
              }}
            />
            <div>
              <label htmlFor="status">填报日期：</label>
              <DatePicker
                style={{ width: '180px' }}
                inputReadOnly
                onChange={(_, dateString) => {
                  setTime(dateString === '' ? undefined : dateString)
                }}
              />
            </div>
            <div>
              <label htmlFor="period">填报时段：</label>
              <Select
                allowClear
                placeholder="请选择时段"
                style={{ width: 180 }}
                onChange={(value: string) => {
                  setPeriod(value)
                }}
                options={[
                  { value: '上午', label: '上午' },
                  { value: '下午', label: '下午' },
                ]}
              />
            </div>
          </SearchLayout>
        }
        toolBarRender={
          isMobile
            ? undefined
            : () => {
                return (
                  <Space>
                    {isAdministration(ENV_CWJTB, ENV_SP) && (
                      <Button
                        type={'primary'}
                        onClick={() =>
                          setModalProps({
                            visible: true,
                            type: 'add',
                            data: undefined,
                          })
                        }
                      >
                        <PlusOutlined />
                        {'晨午检填报'}
                      </Button>
                    )}
                    <Button type={'primary'} onClick={handleDownload}>
                      <UploadOutlined />
                      导出
                    </Button>
                  </Space>
                )
              }
        }
      />
      {env_screen.includes('mobile') && (
        <div
          style={{
            position: 'fixed',
            insetInlineEnd: 0,
            insetBlockEnd: 0,
            width: '100vw',
            background: '#fff',
            padding: '8px 24px',
            borderTop: '1px solid #ececec',
          }}
        >
          <Button
            type="primary"
            style={{
              width: '100%',
              borderRadius: '24px',
            }}
            onClick={() =>
              setModalProps({
                visible: true,
                type: 'add',
                data: undefined,
              })
            }
          >
            <PlusOutlined />
            晨午检填报
          </Button>
        </div>
      )}
      <AddFillInspectionModal
        loadings={loading}
        termCode={nowTermCode}
        modalProps={modalProps}
        setModalProps={setModalProps}
        onSubmit={onSubmit}
      />
    </div>
  )
}

export default FillInspection
