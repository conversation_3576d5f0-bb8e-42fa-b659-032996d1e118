/*
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-08 17:49:33
 * @LastEditTime: 2023-12-29 09:53:35
 * @LastEditors: SissleLynn
 */

import React, { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import { Input, Tabs, Select, Button, Space, Table, message, Tooltip } from 'antd'
import CommonList from '@/components/CommonList'
import SearchLayout from '@/components/Search/Layout'
import styles from './index.less'
import { getColums, getMetas } from './getColums'
import { teacherTrainIndex } from '@/services/edu-platform-web/teacherTrain'
import { envjudge } from '@/utils'
import { generatePDF } from '../utils'

const { Option } = Select
const { Search } = Input
const Management = () => {
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  const childRef = useRef()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const [activeGroup, setActiveGroup] = useState<any>('audit')
  const [activeStatus, setActiveStatus] = useState<any>('已同意,已拒绝,已结束')
  const [groupData, setGroupData] = useState<any>()
  const [title, setTitle] = useState<string>()
  const [loading, setLoading] = useState<boolean>(false)
  const [selectedRows, setSelectedRows] = useState<any>([])

  useEffect(() => {
    setGroupData([
      {
        name: '待审核记录',
        value: 'audit',
      },
      { name: '已审核记录', value: 'check' },
    ])
  }, [schoolInfo?.id])

  const exportPdf = async () => {
    setLoading(true)
    if (!selectedRows || selectedRows.length === 0) {
      setLoading(false)
      return message.warning('请选择要导出的记录')
    }
    const { status, data: pdfBytes, msg } = await generatePDF(selectedRows)
    if (!status) {
      setLoading(false)
      return message.warning(`导出失败 ${msg}`)
    }
    if (!pdfBytes) {
      return message.warning('导出失败 PDF生成内容为空！')
    }
    const blob = new Blob([pdfBytes], { type: 'application/pdf' })
    const fileName = `培训记录.pdf`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    link.click()
    URL.revokeObjectURL(link.href)
    message.success('导出成功')
    setLoading(false)
  }

  const teacherTrainDetail = sessionStorage.getItem('teacherTrainDetail')

  useEffect(() => {
    if (teacherTrainDetail === 'true') {
      setActiveGroup('check')
    }
  }, [teacherTrainDetail])
  return (
    <div className={styles.stasticCheckon}>
      <Tabs
        activeKey={activeGroup}
        destroyInactiveTabPane
        onChange={(activeKey: string) => {
          sessionStorage.removeItem('teacherTrainDetail')
          setActiveStatus('已同意,已拒绝,已结束')
          setTitle(undefined)
          setActiveGroup(activeKey)
        }}
      >
        {groupData?.map((item: any) => {
          return (
            <Tabs.TabPane tab={item.name} key={item.value}>
              <CommonList
                ref={childRef}
                params={{
                  activeGroup,
                  activeStatus,
                  title,
                }}
                rowSelection={activeGroup === 'check' ? {} : false}
                tableAlertRender={({ selectedRowKeys, selectedRows: value, onCleanSelected }) => {
                  setSelectedRows(value)
                  return (
                    <Space size={24}>
                      <span>
                        已选 {selectedRowKeys.length} 项
                        <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                          取消选择
                        </a>
                      </span>
                    </Space>
                  )
                }}
                tableAlertOptionRender={() => {
                  return (
                    <Tooltip title="批量导出耗时较久，请耐心等待">
                      <Button type="primary" loading={loading} onClick={exportPdf}>
                        {loading ? '导出中' : '导出'}
                      </Button>
                    </Tooltip>
                  )
                }}
                request={async (
                  // 第一个参数 params 查询表单和 params 参数的结合
                  // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
                  params: any,
                  sort,
                  filter,
                ) => {
                  // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
                  // 如果需要转化参数可以在这里进行修改
                  if (activeGroup) {
                    const { pageSize, current: page } = params
                    const { errCode, list, count, total } = (await teacherTrainIndex({
                      enterpriseCode: schoolInfo?.code || '',
                      realName: title,
                      status: activeGroup === 'audit' ? '待审批' : activeStatus,
                      offset: Number((page || 1) - 1) * Number(pageSize),
                      limit: Number(pageSize),
                    })) as any
                    return {
                      success: errCode ? false : true,
                      data: list || [],
                      total: count || total,
                    }
                  } else {
                    return {
                      success: true,
                      data: [],
                      total: 0,
                    }
                  }
                }}
                columns={getColums({
                  type: activeGroup,
                  refreshTable: () => {
                    ;(childRef?.current as any)?.reload?.()
                  },
                  currentUser,
                })}
                metas={getMetas({
                  type: activeGroup,
                  refreshTable: () => {
                    ;(childRef?.current as any)?.reload?.()
                  },
                  currentUser,
                })}
                headerTitle={
                  <>
                    <SearchLayout>
                      {activeGroup === 'check' && (
                        <div>
                          <label htmlFor="status">状态：</label>
                          <Select
                            style={{ width: 160 }}
                            defaultValue="已同意,已拒绝,已结束"
                            placeholder="请选择"
                            onChange={(value) => {
                              setActiveStatus(value)
                            }}
                          >
                            <Option value="已同意,已拒绝,已结束" key="all">
                              全部
                            </Option>
                            <Option value={'已同意'} key={'0,1'}>
                              已同意
                            </Option>
                            <Option value={'已拒绝'} key={'2'}>
                              已拒绝
                            </Option>
                            <Option value={'已结束'} key={'3'}>
                              已结束
                            </Option>
                          </Select>
                        </div>
                      )}
                      <div>
                        <label htmlFor="title">教师姓名：</label>
                        <Search
                          allowClear
                          defaultValue={title}
                          onSearch={(val) => {
                            setTitle(val === '' ? undefined : val)
                          }}
                        />
                      </div>
                    </SearchLayout>
                  </>
                }
              />
            </Tabs.TabPane>
          )
        })}
      </Tabs>
    </div>
  )
}

export default Management
