@import '~antd/es/style/themes/default.less';

.topCon {
  margin-bottom: 16px;
  padding: 24px 16px 8px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 4px;
}

.middleCon {
  margin-top: 10px;
  margin-bottom: 16px;
  padding: 16px 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 4px;

  .flowCon {
    margin-top: 10px;
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 4px;
    :global {
      .@{ant-prefix}-descriptions-item-content {
        .@{ant-prefix}-image {
          width: 160px !important;
        }
      }
    }
  }
}

.bottomCon {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 4px;
}

.title {
  margin-bottom: 10px;
  font-weight: 600;
  font-size: large;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.desc {
  margin-bottom: 10px;
  color: gray;
  font-size: small;
}
