/*
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-04 16:35:03
 * @LastEditTime: 2024-03-01 16:24:08
 * @LastEditors: Sissle<PERSON>ynn
 */

import { useImperativeHandle, type ReactNode, useRef, forwardRef } from 'react'
import type { ActionType, ProColumns } from '@ant-design/pro-components'
import { ProList, ProTable } from '@ant-design/pro-components'
import styles from './index.less'
import { envjudge, getTableWidth } from '@/utils'
import { paginationConfig } from '@/constant'
import type { TableRowSelection } from 'antd/lib/table/interface'

const CommonList = forwardRef(
  (
    {
      metas,
      toolBarRender,
      headerTitle,
      columns,
      params,
      request,
      rowSelection,
      tableAlertRender,
      tableAlertOptionRender,
      scroll = {
        x: getTableWidth(columns),
      },
    }: {
      tableAlertRender?: any
      tableAlertOptionRender?: any
      rowSelection?:
        | false
        | (TableRowSelection<any> & {
            alwaysShowAlert?: boolean
          })
        | undefined
      metas?: any
      toolBarRender?: any
      headerTitle?: ReactNode
      columns: ProColumns<any>[]
      params: any
      request: (
        params: any,
        sort: any,
        filter: any,
      ) => Promise<{
        success: boolean
        data: any
        total: any
      }>
      scroll?: {
        x?: number
        y?: number
      }
    },
    ref: React.Ref<any>,
  ) => {
    const env_screen = envjudge()
    const isMobile = env_screen?.includes('mobile')
    const actionRef = useRef<ActionType>()
    useImperativeHandle(ref, () => ({
      // 暴露给父组件的方法
      reload: () => {
        actionRef.current?.reload()
      },
    }))
    return (
      <div className={styles.commonList}>
        {isMobile ? (
          <ProList<any>
            actionRef={actionRef}
            className={styles.mobileList}
            headerTitle={headerTitle}
            toolBarRender={toolBarRender}
            params={params}
            request={request}
            pagination={{
              defaultPageSize: 5,
              showSizeChanger: false,
              size: 'small',
              simple: true,
            }}
            metas={metas}
            rowKey={(record) => record.id + '_' + record?.status}
            showActions="hover"
          />
        ) : (
          <>
            <ProTable<any>
              actionRef={actionRef}
              columns={columns}
              search={false}
              params={params}
              scroll={scroll}
              rowSelection={rowSelection}
              tableAlertRender={tableAlertRender}
              tableAlertOptionRender={tableAlertOptionRender}
              options={{
                setting: false,
                fullScreen: false,
                density: false,
                reload: false,
              }}
              request={request}
              headerTitle={headerTitle}
              toolBarRender={toolBarRender}
              pagination={paginationConfig}
              rowKey={(record) => record.id + '_' + record?.status}
              dateFormatter="string"
            />
          </>
        )}
      </div>
    )
  },
)
export default CommonList
