/*
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>ynn
 * @Date: 2023-12-08 17:49:33
 * @LastEditTime: 2024-03-06 17:22:44
 * @LastEditors: SissleLynn
 */

import React, { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import { Input, Tabs, Select, Modal, Table, Space, Button, message, Tooltip } from 'antd'
import CommonList from '@/components/CommonList'
import SearchLayout from '@/components/Search/Layout'
import styles from './index.less'
import { getColums, getMetas } from './getColums'
import { homeVisitIndex } from '@/services/edu-platform-web/home_visit'
import { generatePDF, StatusEnum } from '../../utils'
import { getQueryObj } from '@/utils'

const { Option } = Select
const { Search } = Input

/** 待审批  */
export const isAudited = `${StatusEnum['待审批']}`
/** 已审核 */
export const Audited = `${StatusEnum['已同意']},${StatusEnum['已拒绝']},${StatusEnum['已结束']}`
const Management = () => {
  const childRef = useRef()
  const { audited } = getQueryObj()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const [activeGroup, setActiveGroup] = useState<any>(isAudited)
  const [groupData, setGroupData] = useState<any>()
  const [title, setTitle] = useState<string>()
  const [previewImage, setPreviewImage] = useState<any>()
  const [selectStaus, setSelectStaus] = useState<string>()
  const [loading, setLoading] = useState<boolean>(false)
  const [data, setData] = useState<any>()
  useEffect(() => {
    setGroupData([
      {
        name: '待审核记录',
        value: isAudited,
      },
      {
        name: '已审核记录',
        value: Audited,
      },
    ])
  }, [schoolInfo?.id])

  useEffect(() => {
    if (audited) {
      setActiveGroup(Audited)
    }
  }, [audited])

  const exportPdf = async () => {
    if (!data) {
      return message.warning('请等待数据加载完成后再导出')
    }
    setLoading(true)
    const { status, data: pdfBytes, msg } = await generatePDF(data)
    if (!status) {
      setLoading(false)
      return message.warning(`导出失败 ${msg}`)
    }
    if (!pdfBytes) {
      return message.warning('导出失败 PDF生成内容为空！')
    }
    const blob = new Blob([pdfBytes], { type: 'application/pdf' })
    const fileName = `${data?.studentName || '家访'}记录.pdf`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    link.click()
    URL.revokeObjectURL(link.href)
    message.success('导出成功')
    setLoading(false)
  }
  return (
    <div className={styles.stasticCheckon}>
      <Tabs
        defaultActiveKey={isAudited}
        activeKey={activeGroup}
        destroyInactiveTabPane
        onChange={(activeKey: string) => {
          if (audited) {
            /** 清除参数，防止tabs切换导致返回页面错误 */
            const url = new URL(window.location.href)
            url.search = ''
            window.history.replaceState({}, document.title, url.toString())
          }
          setTitle(undefined)
          setSelectStaus(undefined)
          setActiveGroup(activeKey)
        }}
      >
        {groupData?.map((item: any) => {
          return (
            <Tabs.TabPane tab={item.name} key={item.value}>
              <CommonList
                ref={childRef}
                params={{
                  activeGroup,
                  title,
                  selectStaus,
                }}
                rowSelection={activeGroup === Audited ? {} : false}
                tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
                  setData(selectedRows)
                  return (
                    <span>
                      已选 {selectedRowKeys.length} 项
                      <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                        取消选择
                      </a>
                    </span>
                  )
                }}
                tableAlertOptionRender={() => {
                  return (
                    <Tooltip title="批量导出耗时较长，请耐心等候。">
                      <Button type="primary" onClick={exportPdf} loading={loading}>
                        {loading ? '批量导出中...' : '批量导出'}
                      </Button>
                    </Tooltip>
                  )
                }}
                request={async (
                  // 第一个参数 params 查询表单和 params 参数的结合
                  // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
                  params: any,
                  sort,
                  filter,
                ) => {
                  // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
                  // 如果需要转化参数可以在这里进行修改
                  if (activeGroup) {
                    const { pageSize, current: page } = params
                    const { errCode, list, total } = (await homeVisitIndex({
                      enterpriseCode: schoolInfo?.code || '',
                      teacherName: title,
                      status: selectStaus || activeGroup,
                      offset: Number((page || 1) - 1) * Number(pageSize),
                      limit: Number(pageSize),
                    })) as any
                    return {
                      success: errCode ? false : true,
                      data: list || [],
                      total,
                    }
                  } else {
                    return {
                      success: true,
                      data: [],
                      total: 0,
                    }
                  }
                }}
                columns={getColums({
                  section_code: schoolInfo?.section_code,
                  type: activeGroup,
                  setPreviewImage,
                  refreshTable: () => {
                    ;(childRef?.current as any)?.reload?.()
                  },
                  currentUser,
                })}
                metas={getMetas({
                  section_code: schoolInfo?.section_code,
                  type: activeGroup,
                  setPreviewImage,
                  refreshTable: () => {
                    ;(childRef?.current as any)?.reload?.()
                  },
                  currentUser,
                })}
                headerTitle={
                  <SearchLayout>
                    {activeGroup === Audited && (
                      <div>
                        <label htmlFor="status">状态：</label>
                        <Select
                          style={{ width: 160 }}
                          defaultValue={Audited}
                          placeholder="请选择"
                          onChange={(value) => {
                            setSelectStaus(value)
                          }}
                        >
                          <Option value={Audited} key={'2,3,4'}>
                            全部
                          </Option>
                          <Option value={`${StatusEnum['已同意']}`} key={'2'}>
                            已同意
                          </Option>
                          <Option value={`${StatusEnum['已拒绝']}`} key={'3'}>
                            已拒绝
                          </Option>
                          <Option value={`${StatusEnum['已结束']}`} key={'4'}>
                            已结束
                          </Option>
                        </Select>
                      </div>
                    )}
                    <div>
                      <label htmlFor="title">教师姓名：</label>
                      <Search
                        allowClear
                        defaultValue={title}
                        onSearch={(val) => {
                          setTitle(val === '' ? undefined : val)
                        }}
                      />
                    </div>
                  </SearchLayout>
                }
              />
            </Tabs.TabPane>
          )
        })}
      </Tabs>
      <Modal
        open={previewImage?.show}
        title={previewImage?.title}
        footer={null}
        onCancel={() => setPreviewImage(undefined)}
      >
        <img alt="example" style={{ width: '100%' }} src={previewImage?.imgUrl} />
      </Modal>
    </div>
  )
}

export default Management
