/*
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-08 17:49:33
 * @LastEditTime: 2024-03-13 09:39:20
 * @LastEditors: SissleLynn
 */

import React, { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import { Input, DatePicker, Tabs, message, Select, Button, Modal, Empty } from 'antd'
import { getStudentColums, getStudentMetas, getTeacherColums, getTeacherMetas } from './getColums'
import CommonList from '@/components/CommonList'
import SearchLayout from '@/components/Search/Layout'
import SemesterSelect from '@/components/SemesterSelect'
import styles from './index.less'
import {
  controllerStudentAttendanceIndex,
  getUnnamedClass,
} from '@/services/edu-platform-web/attendanceStudent'
import { controllerTeacherAttendanceIndex } from '@/services/edu-platform-web/attendance'
import moment from 'moment'
import { controllerWorkRestTimeNowEffective } from '@/services/edu-platform-web/workRestTime'
import ExportRecord from '@/components/ExportRecord'
import { envjudge } from '@/utils'
import {
  controllerRepeatGetClasses,
  controllerRepeatGetGrades,
} from '@/services/edu-platform-web/ssoRepeat'

const { RangePicker } = DatePicker
const { Search } = Input
const Statistics = () => {
  const childRef = useRef()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo } = initialState || {}
  const [dateRange, setDateRange] = useState<any>()
  const [title, setTitle] = useState<string>()
  const [activedKey, setActivedKey] = useState<string>('1')
  const [termCode, setTermCode] = useState<string>()
  const [sectionData, setSectionData] = useState<API.workRestTime[]>()
  /** 选中的节次 */
  const [SelectedSection, setSelectedSection] = useState<number>()
  /** 当前选择的年级 */
  const [gradeValue, setGradeValue] = useState<string>()
  /** 当前选择的班级 */
  const [classValue, setClassValue] = useState<string>()
  /** 班级列表 */
  const [classList, setClassList] = useState<
    {
      label: string
      value: string
    }[]
  >([])
  /** 年级列表 */
  const [gradeList, setGradeList] = useState<
    {
      label: string
      value: string
    }[]
  >([])
  const [modalOpen, setModalOpen] = useState<{
    isOpen: boolean
    data: any
  }>({
    isOpen: false,
    data: undefined,
  })

  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  // 获取节次数据
  const getSection = async () => {
    const res = (await controllerWorkRestTimeNowEffective({
      enterpriseCode: schoolInfo?.code || '',
      sectionCode: schoolInfo?.section_code || '',
      // sectionCode: Keys || SelectedGrade?.key?.substring(0, 2),
      typeCode: '01',
      semesterCode: termCode!,
    })) as ResType<any>
    if (res?.errCode) {
      message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
    } else {
      setSectionData(res?.list)
    }
  }
  useEffect(() => {
    if (termCode) {
      getSection()
    }
  }, [termCode])

  /** 获取班级列表 */
  const getClassList = async (grade: string) => {
    if (!grade) return
    const res = await controllerRepeatGetClasses({
      grade,
      enterpriseId: schoolInfo?.id || '',
    })
    if (res?.errCode) {
      message.warning('班级列表查询失败，请联系管理员' + res?.msg)
      setClassList([])
    }
    const list = res?.list.map((t: { name: string; code: string }) => ({
      label: t.name,
      value: t.code,
    }))
    setClassList(list ?? [])
  }

  /** 获取年级列表 */
  const getGradeList = async () => {
    const res = await controllerRepeatGetGrades({
      section: [schoolInfo?.section_name || ''],
    })
    if (res?.errCode) {
      message.warning('年级列表查询失败，请联系管理员' + res?.msg)
      return []
    }
    const list = res?.list.map((t: { name: string; code: string }) => ({
      label: t.name,
      value: t.code,
    }))
    setGradeList(list ?? [])
  }

  const unnamedClass = async () => {
    if (!SelectedSection) {
      return message.warning('请选择节次')
    }
    const res = await getUnnamedClass({
      enterpriseCode: schoolInfo?.code || '',
      semesterCode: termCode!,
      startDate: dateRange ? moment(dateRange[0]).format('YYYY-MM-DD') : undefined,
      endDate: dateRange ? moment(dateRange[1]).format('YYYY-MM-DD') : undefined,
      orderIndex: SelectedSection,
      gradeCode: gradeValue,
    })
    if (res?.errCode) {
      return message.error('数据获取失败，请联系管理员或稍后再试 ' + res?.msg)
    }
    setModalOpen({
      isOpen: true,
      data: res,
    })
  }

  useEffect(() => {
    getGradeList()
  }, [])

  return (
    <div className={styles.stasticCheckon}>
      <Tabs
        activeKey={activedKey}
        destroyInactiveTabPane
        onChange={(activeKey: string) => {
          setDateRange(undefined)
          setActivedKey(activeKey)
        }}
        tabBarExtraContent={
          !isMobile && {
            right: (
              <ExportRecord
                type={activedKey === '1' ? '教师考勤' : '学生点名'}
                title={`导出${activedKey === '1' ? '教师考勤' : '学生点名'}记录`}
                params={{
                  semesterCode: termCode,
                }}
              />
            ),
          }
        }
      >
        <Tabs.TabPane tab={'教师考勤'} key="1">
          <CommonList
            ref={childRef}
            params={{
              schoolInfo,
              dateRange,
              title,
            }}
            request={async (
              // 第一个参数 params 查询表单和 params 参数的结合
              // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
              params: any,
              sort,
              filter,
            ) => {
              // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
              // 如果需要转化参数可以在这里进行修改
              const { pageSize, current: page } = params
              const { errCode, list, count, total } = (await controllerTeacherAttendanceIndex({
                enterpriseCode: schoolInfo?.code || '',
                teacherName: title,
                startDate: dateRange ? moment(dateRange[0]).format('YYYY-MM-DD') : undefined,
                endDate: dateRange ? moment(dateRange[1]).format('YYYY-MM-DD') : undefined,
                offset: Number((page || 1) - 1) * Number(pageSize),
                limit: Number(pageSize),
              })) as any
              if (errCode) {
                message.error('考勤数据获取失败')
                return {
                  success: true,
                  data: [],
                  total: 0,
                }
              } else {
                return {
                  success: true,
                  data: list || [],
                  total: count || total,
                }
              }
            }}
            columns={getTeacherColums()}
            metas={getTeacherMetas()}
            headerTitle={
              <SearchLayout>
                {/* <SemesterSelect
                  onChange={(val) => {
                    setTermCode(val)
                  }}
                /> */}
                <div>
                  <label htmlFor="status">考勤时间：</label>
                  <RangePicker
                    allowClear
                    onChange={(val) => setDateRange(val)}
                    style={{
                      width: 300,
                    }}
                  />
                </div>
                <div>
                  <label htmlFor="title">教师姓名：</label>
                  <Search
                    allowClear
                    onSearch={(val) => {
                      if (val === '') {
                        setTitle(undefined)
                      } else {
                        setTitle(val)
                      }
                    }}
                  />
                </div>
              </SearchLayout>
            }
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={'学生点名'} key="2">
          <div className={styles.header}>
            <SearchLayout>
              <SemesterSelect
                onChange={(val) => {
                  setTermCode(val)
                }}
              />
              <div>
                <label htmlFor="status">上课日期：</label>
                <RangePicker
                  allowClear
                  onChange={(val) => setDateRange(val)}
                  style={{
                    width: 300,
                  }}
                />
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <label>节次筛选：</label>
                <Select
                  allowClear
                  style={{
                    minWidth: '160px',
                  }}
                  onChange={(val) => {
                    setSelectedSection(val)
                  }}
                  options={[
                    {
                      label: '第七节',
                      value: 7,
                    },
                    {
                      label: '第八节',
                      value: 8,
                    },
                  ]}
                />
              </div>
              <div>
                <label htmlFor="status">年级：</label>
                <Select
                  style={{ width: 140 }}
                  placeholder="请选择年级"
                  value={gradeValue}
                  allowClear
                  onClear={() => {
                    setGradeValue(undefined)
                    setClassValue(undefined)
                    setClassList([])
                  }}
                  onChange={(val) => {
                    setGradeValue(val)
                    getClassList(val)
                    setClassValue(undefined)
                  }}
                  options={gradeList}
                />
              </div>
              <div>
                <label htmlFor="status">班级：</label>
                <Select
                  allowClear
                  style={{ width: 140 }}
                  placeholder="请选择班级"
                  value={classValue}
                  onChange={(val: string) => {
                    setClassValue(val)
                  }}
                  options={classList}
                />
              </div>

              <Button type="primary" onClick={unnamedClass}>
                未点名班级
              </Button>
            </SearchLayout>
          </div>
          <CommonList
            ref={childRef}
            params={{
              schoolInfo,
              dateRange,
              termCode,
              orderIndex: SelectedSection,
              gradeCode: gradeValue,
              classCode: classValue,
            }}
            request={async (
              // 第一个参数 params 查询表单和 params 参数的结合
              // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
              params: any,
              sort,
              filter,
            ) => {
              // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
              // 如果需要转化参数可以在这里进行修改
              if (termCode) {
                const { pageSize, current: page, orderIndex, gradeCode, classCode } = params
                const { errCode, list, count, total } = (await controllerStudentAttendanceIndex({
                  enterpriseCode: schoolInfo?.code || '',
                  semesterCode: termCode,
                  startDate: dateRange ? moment(dateRange[0]).format('YYYY-MM-DD') : undefined,
                  endDate: dateRange ? moment(dateRange[1]).format('YYYY-MM-DD') : undefined,
                  orderIndex: orderIndex || undefined,
                  gradeCode: gradeCode || undefined,
                  classCode: classCode || undefined,
                  offset: Number((page || 1) - 1) * Number(pageSize),
                  limit: Number(pageSize),
                })) as any

                if (errCode) {
                  return {
                    success: false,
                    data: [],
                    total: 0,
                  }
                } else {
                  return {
                    success: true,
                    data: list || [],
                    total: count || total,
                  }
                }
              } else {
                return {
                  success: true,
                  data: [],
                  total: 0,
                }
              }
            }}
            columns={getStudentColums(sectionData, schoolInfo?.section_code)}
            metas={getStudentMetas(sectionData, schoolInfo?.section_code)}
          />
        </Tabs.TabPane>
      </Tabs>
      <Modal
        footer={null}
        open={modalOpen.isOpen}
        bodyStyle={{
          maxHeight: '500px',
          overflowY: 'scroll',
        }}
        onCancel={() => {
          setModalOpen({
            isOpen: false,
            data: undefined,
          })
        }}
        width={800}
        title={'未点名班级'}
      >
        {modalOpen.data ? (
          Object.entries(modalOpen.data).map(([date, classes]) => (
            <div key={date} style={{ marginBottom: 16 }}>
              <h4 style={{ marginBottom: 8 }}>{date}</h4>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                {String(classes)
                  .split('、')
                  .map((className) => (
                    <div
                      key={className}
                      style={{
                        padding: '4px 12px',
                        background: '#f0f0f0',
                        borderRadius: 4,
                      }}
                    >
                      {className}
                    </div>
                  ))}
              </div>
            </div>
          ))
        ) : (
          <Empty description="暂无数据" />
        )}
      </Modal>
    </div>
  )
}

export default Statistics
