import React, { useEffect, useRef, useState } from 'react'
import styles from './index.less'
import { Button, Card, Form, Popconfirm, Segmented, Spin, message } from 'antd'
import type { EditableFormInstance, FormInstance, ProColumns } from '@ant-design/pro-components'
import { DrawerForm, EditableProTable, ProForm, ProFormText } from '@ant-design/pro-components'
import { useModel } from 'umi'
import { ExportOutlined } from '@ant-design/icons'
import {
  getGradeInfo,
  getSchoolQualityScore,
  getSchoolQualityTable,
  getStudyQualityTable,
} from '@/services/score-analysis/statistics'
import { controllerExaminationShow as getDetailFrom } from '@/services/score-analysis/examination'
import PaperQuality from './PaperQuality'
import StudyQuality from './StudyQuality'
import { submitFormRealTime } from './util'

const SchoolAnalyse = ({
  form,
  examId,
  setEditStatus,
  type,
}: {
  form: FormInstance<any>
  examId: any
  setEditStatus?: (status?: string) => void
  type?: string
}) => {
  const { initialState } = useModel('@@initialState')
  const { currentUser } = initialState || {}
  const [drawForm] = Form.useForm()
  const editorFormRef = useRef<EditableFormInstance<any>>()
  const [subjectActived, setSubjectActived] = useState<string>()
  const [subjectNameActived, setSubjectNameActived] = useState<string>()
  const [statisticData, setStatisticData] = useState<any>()
  const [statisticTable, setStatisticTable] = useState<any[]>()
  const [statisticStudyTable, setStatisticStudyTable] = useState<any[]>()
  const [gradeData, setGradeData] = useState<any[]>([])
  const [subjectSource, setSubjectSource] = useState<any>()
  const [downLoading, setDownLoading] = useState<boolean>(false)
  const [innerLoading, setInnerLoading] = useState<boolean>(false)
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])
  const [curType, setCurType] = useState<string | undefined>(type)
  const [tableData, setTableData] = useState<any[]>([])
  const [drawProps, setDrawProps] = useState<{
    open: boolean
    title: string
    type: string
    infoData?: any
  }>()
  const resetForm = async () => {
    const res = (await getDetailFrom({ id: examId })) as ResType<any>
    if (res?.errCode) {
      message.error(res.message || '获取数据失败，请联系管理员或稍后再试')
    } else {
      const gradeArr = res?.examGrads.sort(
        (a: SCORE_API.ExamGradeInfo, b: SCORE_API.ExamGradeInfo) =>
          parseInt(a?.gradeCode + '') - parseInt(b.gradeCode + ''),
      )
      const newGradeData: any = [].map.call(gradeArr, (v: any, index: number) => {
        return {
          gradeName: v.gradeName,
          gradeCode: v.gradeCode,
          id: v.id,
          index: index,
        }
      })
      // const newData: any = gradeArr?.[0]?.subjects
      const newGrade: any = gradeArr?.flatMap((v: { subjects: any }) => v.subjects)
      const newData = Array.from(new Set(newGrade?.map((v: any) => v.subjectName)))
      const subData = newData?.map((v: any) => {
        return {
          label: v,
          value: v,
        }
      })
      // const subData = newData?.map((v: any) => {
      //   return {
      //     label: v.subjectName,
      //     value: v.subjectName,
      //     code: v.subjectCode,
      //   }
      // })
      const curSubData = subData?.find((v: any, i: number) => {
        if (subjectActived) {
          return v.value === subjectActived
        } else {
          return i === 0
        }
      })
      setGradeData(newGradeData)
      setSubjectActived(curSubData?.code)
      setSubjectNameActived(curSubData?.label)
      setSubjectSource(subData)
      form.setFieldsValue({
        subjectCode: curSubData?.code,
        subjectName: curSubData?.label,
      })
    }
  }
  const getGradeInfoContent = async (gradeName: string) => {
    const result = await getGradeInfo({
      examinationId: examId,
      type: '年级',
      subjectName: subjectNameActived,
      gradeName,
      status: '已发布',
    })
    if (result?.errCode) {
      message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
    } else {
      if (result?.list?.length) {
        const { measure, opinion } = result.list[0]
        const newData = [...tableData]
        const curGrade = newData?.find((v) => v.gradeName === gradeName)
        curGrade.measure = measure
        curGrade.opinion = opinion
        setTableData(newData)
        form.setFieldValue('schoolTable', newData)
        setTimeout(async () => {
          await submitFormRealTime(form, currentUser, examId)
        }, 0)
      } else {
        message.warning('未查询到本年级分析数据，请确认当前年级试卷质量分析报告是否已提交！')
      }
    }
  }
  const columns: ProColumns<API.course>[] = [
    {
      title: '年级',
      key: 'gradeName',
      dataIndex: 'gradeName',
      valueType: 'text',
      width: 60,
      align: 'center',
      editable: false,
    },
    {
      title: '对试题的具体意见或建议',
      dataIndex: 'opinion',
      key: 'opinion',
      valueType: 'textarea',
      width: 300,
      align: 'left',
      formItemProps: () => {
        return {
          rules: [{ type: 'string', max: 255, message: '内容不能超过255个字符' }],
        }
      },
    },
    {
      title: '对命题的整体评价',
      dataIndex: 'measure',
      key: 'measure',
      valueType: 'textarea',
      width: 300,
      align: 'left',
      formItemProps: () => {
        return {
          rules: [{ type: 'string', max: 255, message: '内容不能超过255个字符' }],
        }
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      align: 'center',
      hideInTable: curType === 'check',
      render: (text: any, record: any, _: any, action: any) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id)
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="delete"
          // icon={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
          title="同步年级数据之后，当前年级的现有数据会被重置为年级填写的内容，确定要同步吗?"
          onConfirm={async () => {
            await getGradeInfoContent(record.gradeName)
          }}
          okText="确定"
          cancelText="取消"
        >
          <a key="async">同步年级分析</a>
        </Popconfirm>,
      ],
    },
  ]
  const getFillStatus = async () => {
    const res = await getSchoolQualityTable({
      examinationId: examId,
      subjectName: subjectNameActived!,
    })
    if (res?.errCode) {
      message.warning(res?.message || res?.msg || '数据获取失败，请联系管理员或稍后再试！')
    } else {
      const arr = res?.sort(
        (a: { gradeCode: string }, b: { gradeCode: string }) =>
          parseInt(a.gradeCode + '') - parseInt(b.gradeCode + ''),
      )
      setStatisticTable(arr)
    }
    const res1 = await getStudyQualityTable({
      examinationId: examId,
      subjectName: subjectNameActived!,
    })
    if (res1?.errCode) {
      message.warning(res1?.message || res1?.msg || '数据获取失败，请联系管理员或稍后再试！')
    } else {
      const arr1 = res1?.sort(
        (a: { gradeCode: string }, b: { gradeCode: string }) =>
          parseInt(a.gradeCode + '') - parseInt(b.gradeCode + ''),
      )
      setStatisticStudyTable(arr1)
    }
  }
  useEffect(() => {
    resetForm()
  }, [])
  useEffect(() => {
    if (examId && subjectNameActived) {
      setDownLoading(true)
      getFillStatus()
      setTableData([])
      setEditableRowKeys([])
      form.setFieldsValue({
        id: '',
        schoolTable: [],
        opinion: '',
        measure: '',
      })
      ;(async () => {
        const res = await getSchoolQualityScore({
          examinationId: examId,
          subjectName: subjectNameActived,
        })
        if (res?.errCode) {
          message.warning(res?.message || res?.msg || '数据获取失败，请联系管理员或稍后再试！')
          setDownLoading(false)
        } else {
          const { reportStatistic, ...info } = res
          if (reportStatistic) {
            const { reportStatisticsDetails, status, subjectCode, subjectName, ...rest } =
              reportStatistic
            const data = reportStatisticsDetails
              ?.filter((v: any) => v.content)
              .map((item: any) => {
                return item.content
              })
            const realData = gradeData?.map((val) => {
              const isExist = data?.find((v: any) => v.gradeName === val.gradeName)
              if (isExist) {
                return {
                  ...isExist,
                  ...val,
                }
              } else {
                return val
              }
            })
            setSubjectNameActived(subjectName)
            if (status === '已发布') {
              setCurType('check')
              setEditStatus?.('check')
            } else {
              setEditStatus?.('edit')
              setCurType('edit')
            }
            setTableData(realData || [])
            form.setFieldsValue({
              ...rest,
              subjectCode,
              schoolTable: realData,
              type: '学校',
            })
          } else {
            setTableData(gradeData)
            form.setFieldsValue({
              type: '学校',
              edit_teacherCode: currentUser?.userCode,
              edit_teacherName: currentUser?.realName,
              examinationId: examId,
              schoolTable: gradeData,
            })
            setEditStatus?.('edit')
            setCurType('edit')
          }
          setStatisticData({
            ...info,
          })
          setDownLoading(false)
        }
      })()
    }
  }, [currentUser, subjectNameActived, examId, form, setEditStatus, type])

  return (
    <Spin tip="数据获取中，请稍候..." spinning={downLoading}>
      <ProFormText name="type" hidden />
      <ProFormText name="schoolTable" hidden />
      <div className={styles.topBar}>
        {curType === 'check' && (
          <a
            className={styles.exportBtn}
            href={`/api_score/download/statistic_by_examination?examinationId=${examId}&subjectName=${subjectNameActived}`}
          >
            <ExportOutlined />
            导出
          </a>
        )}
        <ProForm.Item label="学科：" name="subjectName">
          {subjectSource?.length ? (
            <Segmented
              className={styles.gradeSegmented}
              value={subjectNameActived}
              options={subjectSource}
              onChange={(value: any) => {
                setSubjectNameActived(value)
                const curData = subjectSource?.find((v: any) => v.value === value)
                if (curData) {
                  setSubjectActived(curData?.code)
                  form.setFieldValue('subjectCode', curData?.code)
                }
              }}
            />
          ) : (
            '暂无学科'
          )}
        </ProForm.Item>
      </div>
      <div className={styles.middleBar}>
        <table
          className={styles.statisticTable}
          style={{
            marginBottom: 16,
          }}
        >
          <tbody>
            <tr>
              <td>年级</td>
              {statisticTable?.map((v) => {
                return <td key={v?.gradeCode}>{v?.gradeName}</td>
              })}
            </tr>
            <tr>
              <td>试卷质量分析</td>
              {statisticTable?.map((v) => {
                return (
                  <td key={v?.code}>
                    {v?.isWrite ? (
                      <a
                        onClick={() => {
                          setInnerLoading(true)
                          setDrawProps({
                            title: v.gradeName + '【' + subjectNameActived + '】' + '试卷质量分析',
                            type: 'quality',
                            open: true,
                            infoData: {
                              gradeCodeActived: v?.gradeCode,
                              gradeNameActived: v?.gradeName,
                              subjectActived: v?.subjectCode,
                            },
                          })
                        }}
                      >
                        已填写
                      </a>
                    ) : (
                      <span
                        style={{
                          color: '#f50',
                        }}
                      >
                        未填写
                      </span>
                    )}
                  </td>
                )
              })}
            </tr>
            <tr>
              <td>学业质量分析</td>
              {statisticStudyTable?.map((v) => {
                return (
                  <td key={v?.code}>
                    {v?.isWrite ? (
                      <a
                        onClick={() => {
                          setInnerLoading(true)
                          setDrawProps({
                            title: v?.gradeName + '【' + subjectNameActived + '】' + '学业质量分析',
                            type: 'study',
                            open: true,
                            infoData: {
                              gradeCodeActived: v?.gradeCode,
                              gradeNameActived: v?.gradeName,
                              subjectActived: v?.subjectCode,
                            },
                          })
                        }}
                      >
                        已填写
                      </a>
                    ) : (
                      <span
                        style={{
                          color: '#f50',
                        }}
                      >
                        未填写
                      </span>
                    )}
                  </td>
                )
              })}
            </tr>
          </tbody>
        </table>
        <table className={styles.statisticTable}>
          <thead>
            <th>年级</th>
            <th>应考人数</th>
            <th>实考人数</th>
            <th>参试率（%）</th>
            <th>满分</th>
            <th>率（%）</th>
            <th>优秀</th>
            <th>率（%）</th>
            <th>良好</th>
            <th>率（%）</th>
            <th>合格</th>
            <th>率（%）</th>
            <th>不合格</th>
            <th>率（%）</th>
            <th>总分</th>
            <th>平均分</th>
            <th>优秀率（%）</th>
            <th>合格率（%）</th>
          </thead>
          <tbody>
            {statisticData?.item
              ?.sort(
                (a: { gradeCode: string }, b: { gradeCode: string }) =>
                  parseInt(a.gradeCode + '') - parseInt(b.gradeCode + ''),
              )
              .map((item: any) => {
                return (
                  <tr key={item?.gradeName}>
                    <td>{item?.gradeName}</td>
                    <td>{item?.yyrs}</td>
                    <td>{item?.skrs}</td>
                    <td>{item?.csl}</td>
                    <td>{item?.mf}</td>
                    <td>{item?.mfl}</td>
                    <td>{item?.yx}</td>
                    <td>{item?.yxl}</td>
                    <td>{item?.lh}</td>
                    <td>{item?.lhl}</td>
                    <td>{item?.hg}</td>
                    <td>{item?.hgl}</td>
                    <td>{item?.bhg}</td>
                    <td>{item?.bhgl}</td>
                    <td
                      style={{
                        background: 'var(--primary-color-1)',
                      }}
                    >
                      {item?.zf}
                    </td>
                    <td
                      style={{
                        background: 'var(--primary-color-1)',
                      }}
                    >
                      {item?.pjf}
                    </td>
                    <td
                      style={{
                        background: 'var(--primary-color-1)',
                      }}
                    >
                      {item?.zyxl}
                    </td>
                    <td
                      style={{
                        background: 'var(--primary-color-1)',
                      }}
                    >
                      {item?.zjgl}
                    </td>
                  </tr>
                )
              })}
          </tbody>
          <tbody>
            <tr
              style={{
                background: 'var(--primary-color-1)',
              }}
            >
              <td>合计</td>
              <td>{statisticData?.school_yyrs}</td>
              <td>{statisticData?.school_skrs}</td>
              <td>{statisticData?.school_csl}</td>
              <td>{statisticData?.school_mf}</td>
              <td>{statisticData?.school_mfl}</td>
              <td>{statisticData?.school_yx}</td>
              <td>{statisticData?.school_yxl}</td>
              <td>{statisticData?.school_lh}</td>
              <td>{statisticData?.school_lhl}</td>
              <td>{statisticData?.school_hg}</td>
              <td>{statisticData?.school_hgl}</td>
              <td>{statisticData?.school_bhg}</td>
              <td>{statisticData?.school_bhgl}</td>
              <td>{statisticData?.school_zf}</td>
              <td>{statisticData?.school_pjf}</td>
              <td>{statisticData?.school_zyxl}</td>
              <td>{statisticData?.school_zjgl}</td>
            </tr>
            <tr>
              <td
                colSpan={18}
                style={{
                  color: '#888',
                }}
              >
                备注：1、优秀：85分~100分（不含100分）；良好：70分及以上，85分以下；合格：60分及以上，70分以下；不合格：60分以下。
                2、优秀率=85分及以上人数/实考人数。 3、合格率=60分及以上人数/实考人数
              </td>
            </tr>
          </tbody>
        </table>
        <Card title="教学质量考察分析表" bordered={false} style={{ width: '100%' }}>
          <EditableProTable
            editableFormRef={editorFormRef}
            rowKey={(record) => record.id}
            recordCreatorProps={false}
            columns={columns}
            value={tableData}
            onChange={setTableData}
            editable={{
              type: 'single',
              editableKeys,
              actionRender: (row, config, dom) => [dom.save, dom.cancel],
              onChange: setEditableRowKeys,
              onSave: async (key: any, record: any) => {
                const newData = [...tableData]
                const isExist = newData?.findIndex((v) => v.id === key)
                if (isExist === -1) {
                  newData.push(record)
                } else {
                  newData[isExist] = record
                }
                form.setFieldValue('schoolTable', newData)
                setTimeout(async () => {
                  const result = await submitFormRealTime(form, currentUser, examId)
                  if (!result) {
                    setEditableRowKeys([key])
                  }
                }, 0)
              },
            }}
          />
        </Card>
      </div>
      <DrawerForm
        title={drawProps?.title || `年级质量分析`}
        width="90vw"
        className={styles.applyModal}
        form={drawForm}
        visible={drawProps?.open || false}
        drawerProps={{
          destroyOnClose: true,
        }}
        onVisibleChange={(visible: boolean) => {
          if (!visible) {
            setDrawProps(undefined)
          }
        }}
        submitter={{
          render: () => {
            return (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                }}
              >
                <Button
                  type="primary"
                  onClick={() => {
                    setDrawProps(undefined)
                  }}
                >
                  关闭
                </Button>
              </div>
            )
          },
        }}
      >
        <Spin tip="数据获取中，请稍候..." spinning={innerLoading}>
          {drawProps?.type === 'quality' && (
            <PaperQuality
              form={drawForm}
              examId={examId}
              setEditStatus={setEditStatus}
              setDownLoading={setInnerLoading}
              infoData={drawProps?.infoData}
            />
          )}
          {drawProps?.type === 'study' && (
            <StudyQuality
              form={drawForm}
              examId={examId}
              setEditStatus={setEditStatus}
              setDownLoading={setInnerLoading}
              infoData={drawProps?.infoData}
            />
          )}
        </Spin>
      </DrawerForm>
    </Spin>
  )
}

export default SchoolAnalyse
