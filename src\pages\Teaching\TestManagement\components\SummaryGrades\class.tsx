import React, { useEffect, useState } from 'react'
import styles from './index.less'
import { Button, Segmented, Spin, message } from 'antd'
import { ExportOutlined } from '@ant-design/icons'
import { ProForm } from '@ant-design/pro-components'
import { getNjName } from '@/utils'
import { useModel } from 'umi'
import NoData from '@/components/NoData'
import CommonTable from './commonTable'
import { getClassCollect } from '@/services/edu-platform-web/summary_by_examination'

const ClassAnalyse = ({
  examinationId,
  grade,
}: {
  examinationId: any
  setEditStatus?: (status?: string) => void
  grade?: any
}) => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const [gradeActived, setGradeActived] = useState<string>()
  const [gradeCodeActived, setGradeCodeActived] = useState<string>()
  const [subjectActived, setSubjectActived] = useState<string>()
  const [classActived, setClassActived] = useState<any>()
  const [gradeData, setGradeData] = useState<any[]>([])
  const [classData, setClassData] = useState<any[]>([])
  const [subjectSource, setSubjectSource] = useState<any>()
  const [downLoading, setDownLoading] = useState<boolean>(false)
  const [isEditor, setIsEditor] = useState<boolean>(false)
  const [tableData, setTableData] = useState<any[]>([])
  const getClsData = (curSub: any, index?: number) => {
    const clsData = curSub?.classData?.map((v: any) => {
      return {
        label: v.className,
        value: v.classCode,
      }
    })

    const curClsData = clsData?.find((v: any, i: number) => {
      if (index !== undefined) {
        return i === index
      } else if (classActived) {
        return v.value === classActived
      } else {
        return i === 0
      }
    })
    setClassActived(curClsData?.value)
    setClassData(clsData)
  }
  const getSubData = (curGrade: any, index?: number) => {
    const subData = curGrade?.subjectData?.map((v: any) => {
      return {
        label: v.subjectName,
        value: v.subjectCode,
        classData: v.teachers?.[0]?.classes,
      }
    })
    const curSubData = subData?.find((v: any, i: number) => {
      if (index !== undefined) {
        return i === index
      } else if (subjectActived) {
        return v.value === subjectActived
      } else {
        return i === 0
      }
    })
    getClsData(curSubData, index)
    setSubjectActived(curSubData?.value)
    setSubjectSource(subData)
  }
  const resetForm = () => {
    if (grade?.length) {
      const nowData = grade.filter((v: any) => v.subjects?.length > 0)
      const newData: any = [].map.call(nowData, (v: any) => {
        return {
          label: `${getNjName(v.gradeName, schoolInfo?.section_code)}`,
          value: v.id,
          code: v.gradeCode,
          subjectData: v.subjects,
        }
      })
      const curGradeInfo = newData?.find((v: any, i: number) => {
        if (gradeActived) {
          return v.value === gradeActived
        } else {
          return i === 0
        }
      })

      getSubData(curGradeInfo)
      setGradeActived(curGradeInfo?.value)
      setGradeCodeActived(curGradeInfo?.code)
      setGradeData(newData)
    }
  }

  useEffect(() => {
    resetForm()
  }, [])

  const handleDownload = async () => {
    try {
      const baseUrl = '/api_score/download/summary_by_class'
      const params = new URLSearchParams()
      params.append('examinationId', examinationId || '')
      params.append('subjectCode', subjectActived || '')
      params.append('gradeCode', gradeCodeActived || '')
      params.append('classCode', classActived || '')
      const url = `${baseUrl}?${params.toString()}`
      const a = document.createElement('a')
      a.href = url
      a.download = '班级成绩汇总.xlsx'
      document.body.appendChild(a)
      a.click()
      a.remove()
      message.success('导出成功')
    } catch (error) {
      message.error('导出失败，请联系管理员或稍后再试！' + error)
    }
  }

  useEffect(() => {
    if (examinationId && gradeCodeActived && classActived && subjectActived) {
      setTableData([])
      setIsEditor(true)
      setDownLoading(true)
      ;(async () => {
        const res = await getClassCollect({
          examinationId: examinationId,
          gradeCode: gradeCodeActived,
          classCode: classActived,
          subjectCode: subjectActived,
        })
        if (res?.errCode) {
          message.warning(res?.message || res?.msg || '数据获取失败，请联系管理员或稍后再试！')
          setDownLoading(false)
        } else {
          const { reportStatistic, ...info } = res
          if (reportStatistic) {
            const { subjectCode, classCode, gradeId } = reportStatistic

            const classes = [
              {
                className: res?.className,
                yyrs: res?.yyrs,
                skrs: res?.ksrs,
                yx: res?.yxrs,
                yxl: res?.yxl,
                hg: res?.rs_4,
                hgl: res?.ratio_4,
                pjf: res?.pjf,
                lh: res?.rs_3,
                lhl: res?.ratio_3,
                bhg: res?.rs_5,
                bhgl: res?.ratio_5,
                qkrs: res?.qkrs,
              },
            ]

            setClassActived(classCode)
            setGradeActived(gradeId)
            setSubjectActived(subjectCode)

            setTableData(classes || [])
          } else {
            setTableData([])
          }
          setDownLoading(false)
        }
      })()
    }
  }, [gradeCodeActived, currentUser, classActived, subjectActived, examinationId])

  return (
    <Spin tip="数据获取中，请稍候..." spinning={downLoading}>
      {isEditor ? (
        <>
          <header className={styles.schoolHeader}>
            <div>
              <ProForm.Item label="年级：" name="gradeId">
                {gradeData?.length ? (
                  <Segmented
                    className={styles.gradeSegmented}
                    value={gradeActived}
                    options={gradeData}
                    onChange={(value) => {
                      setGradeActived(value as string)
                      const curData = gradeData?.find((v: any) => v.value === value)
                      if (curData) {
                        setGradeCodeActived(curData?.code)
                        getSubData(curData, 0)
                      }
                    }}
                  />
                ) : (
                  '暂无年级'
                )}
              </ProForm.Item>
              <ProForm.Item label="学科：" name="subjectCode">
                {subjectSource?.length ? (
                  <Segmented
                    className={styles.gradeSegmented}
                    value={subjectActived}
                    options={subjectSource}
                    onChange={(value) => {
                      setSubjectActived(value as string)
                      const curData = subjectSource?.find((v: any) => v.value === value)
                      if (curData) {
                        getClsData(curData)
                      }
                    }}
                  />
                ) : (
                  '暂无学科'
                )}
              </ProForm.Item>
              <ProForm.Item label="班级：" name="classCode">
                {classData?.length ? (
                  <Segmented
                    className={styles.gradeSegmented}
                    value={classActived}
                    options={classData}
                    onChange={(value) => {
                      setClassActived(value as string)
                    }}
                  />
                ) : (
                  '暂无班级'
                )}
              </ProForm.Item>
            </div>
            <Button
              type="primary"
              onClick={handleDownload}
              icon={<ExportOutlined />}
              disabled={tableData.length === 0}
            >
              导出
            </Button>
          </header>
          <CommonTable type="class" data={tableData} />
        </>
      ) : (
        <div
          style={{
            marginBlockStart: '15vh',
          }}
        >
          <NoData desc="本次考试不涉及您教授的课程或班级" />
        </div>
      )}
    </Spin>
  )
}

export default ClassAnalyse
