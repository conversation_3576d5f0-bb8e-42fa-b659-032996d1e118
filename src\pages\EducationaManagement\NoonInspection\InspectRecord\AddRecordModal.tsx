import { getTeacherInfoAPI } from '@/services/edu-platform-web/class_duty'
import {
  diseaseStudentCreate,
  diseaseStudentUpdate,
} from '@/services/edu-platform-web/disease_student'
import { controllerRepeatGetStudents } from '@/services/edu-platform-web/ssoRepeat'
import { envjudge, getImageId } from '@/utils'
import {
  DrawerForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
  ProFormUploadDragger,
} from '@ant-design/pro-components'
import { Form, Result, Upload, message } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { useModel } from 'umi'
import { controllerUploadReplace } from '@/services/edu-platform-web/upload'

type screenType = {
  label: string
  value: string
  detail?: string
  code?: string
  gender?: string
}[]
const AddRecordModal = ({
  termCode,
  modalProps,
  diseases,
  setModalProps,
  refreshTable,
}: {
  termCode?: string
  diseases?: any
  modalProps: {
    visible: boolean
    data?: any
  }
  setModalProps: React.Dispatch<
    React.SetStateAction<{
      visible: boolean
      data?: any
    }>
  >
  refreshTable: () => void
}) => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  const [form] = Form.useForm()
  /** 年级班级列表 */
  const [gradeList, setGradeList] = useState<any[]>([])
  /** 当前选中的年级班级 */
  const [selectGrade, setSelectGrade] = useState<any>()
  /** 学生列表 */
  const [studentData, setStudentData] = useState<screenType>([])
  const onFinish = async (values: any) => {
    const { id, attachment, ...rest } = values
    let result
    if (id) {
      result = await diseaseStudentUpdate(id, {
        ...rest,
        attachment: attachment
          ?.map((item: { response: { data: { url: any } } }) => item.response?.data?.url || 'null')
          .join(','),
      })
    } else {
      result = await diseaseStudentCreate({
        ...values,
        attachment: attachment
          ?.map((item: { response: { data: { url: any } } }) => item.response?.data?.url || 'null')
          .join(','),
        record_date: new Date(),
      })
    }
    if (result?.errCode) {
      message.warning(
        result.msg || result.message || result.error || '提交失败，请联系管理员或稍后再试！',
      )
      return false
    } else {
      message.success('提交成功')
      form?.resetFields()
      refreshTable?.()
      return true
    }
  }
  /**
   * 通过年级班级信息获取学生信息
   * @param classCode
   * @param grade
   */
  const getStudents = async (classCode: string, grade: string) => {
    const result = await controllerRepeatGetStudents({
      enterpriseId: schoolInfo?.id || '',
      classCode,
      grade,
      semesterCode: termCode,
    })
    if (result?.errCode) {
      console.warn(result?.msg || '班级获取失败，请联系管理员或稍后再试！')
    } else {
      const newData = (result?.list || []).map((item: any) => {
        let year: string | undefined = undefined
        const yearPart = item?.IDNumber?.substring(6, 10)
        if (yearPart && /^\d{4}$/.test(yearPart)) {
          const yearNumber = parseInt(yearPart, 10)
          if (yearNumber >= 1900 && yearNumber <= 2100) {
            year = yearPart
          }
        } else {
          year = undefined
        }
        return {
          label: item.name,
          value: item.name,
          detail: item.name,
          code: item.work_code,
          gender: item.gender,
          year,
          parentName: item.parents?.[0]?.name,
          parentPhone: item.parents?.[0]?.mobile,
        }
      })
      setStudentData(newData)
    }
  }
  /** 获取教师班级信息 */
  const getTeacherInfo = useCallback(async () => {
    const res = await getTeacherInfoAPI('getClassesByMemberId', {
      query: { memberId: currentUser?.memberId },
    })
    if (res?.code) {
      message.warning('获取教师任课班级信息失败，请联系管理员或稍后再试')
      return []
    }
    const newData = res?.map(
      (item: {
        grade_name: string
        name: string
        grade_code: string
        code: string
        id: string
      }) => ({
        label: item.grade_name + item.name,
        value: `${item.grade_code},${item.code}`,
        grade_name: item.grade_name,
        grade_code: item.grade_code,
        class_name: item.name,
        class_code: item.code,
        id: item.id,
      }),
    )
    setGradeList(newData)

    if (newData.length === 0) {
      setSelectGrade({})
    } else if (newData.length >= 1) {
      setSelectGrade(newData[0])
    }
  }, [])
  useEffect(() => {
    if (currentUser?.memberId) {
      getTeacherInfo()
    }
  }, [getTeacherInfo, currentUser])
  return (
    <DrawerForm<any>
      title="学生因病追踪登记"
      form={form}
      open={modalProps?.visible}
      autoFocusFirstInput
      layout="horizontal"
      labelCol={{
        flex: '6.5em',
      }}
      wrapperCol={{
        flex: isMobile ? 'auto' : undefined,
      }}
      onOpenChange={(open) => {
        if (open) {
          if (modalProps.data) {
            const { disease_symptoms, attachment, ...rest } = modalProps.data
            let attachmentImg
            if (attachment) {
              attachmentImg = (attachment.split(',') || [])?.map((item: any, index: number) => {
                return {
                  uid: index,
                  name: '附件照片' + index + 1,
                  status: 'done',
                  url: item,
                  response: {
                    data: {
                      id: getImageId(item),
                      url: item,
                    },
                  },
                }
              })
            }
            form.setFieldsValue({
              ...rest,
              symptomIdArr: disease_symptoms?.map((v: any) => v.id),
              attachment: attachmentImg,
            })
            getStudents(modalProps.data?.classCode, modalProps.data?.gradeCode)
          } else {
            setStudentData([])
            if (gradeList.length > 0) {
              getStudents(gradeList[0].class_code, gradeList[0].grade_code)
              form.setFieldsValue({
                gradeName: gradeList[0]?.grade_name,
                gradeCode: gradeList[0]?.grade_code,
                className: gradeList[0]?.class_name,
                classCode: gradeList[0]?.class_code,
              })
            }
          }
        } else {
          form?.resetFields()
        }
        setModalProps({ ...modalProps, visible: open })
      }}
      drawerProps={{
        width: isMobile ? '100%' : 800,
        destroyOnClose: false,
      }}
      submitter={{
        // 配置按钮文本
        searchConfig: {
          resetText: '取消',
          submitText: '提交',
        },
        submitButtonProps: {
          style: {
            display: gradeList.length > 0 ? 'block' : 'none',
          },
        },
      }}
      initialValues={{
        teacherCode: currentUser?.userCode,
        teacherName: currentUser?.realName,
        enterpriseCode: schoolInfo?.code,
        enterpriseName: schoolInfo?.name,
        semesterCode: termCode,
        reason: '学生因病缺勤',
      }}
      grid
      onFinish={onFinish}
    >
      <ProFormText name="id" hidden />
      <ProFormText name="teacherCode" hidden />
      <ProFormText name="semesterCode" hidden />
      <ProFormText name="enterpriseCode" hidden />
      <ProFormText name="enterpriseName" hidden />
      <ProFormText name="gradeName" hidden />
      <ProFormText name="gradeCode" hidden />
      <ProFormText name="className" hidden />
      <ProFormText name="classCode" hidden />
      {gradeList.length > 0 ? (
        <>
          <ProFormSelect
            colProps={{ md: 8, sm: 24 }}
            name="gradeClass"
            label="年级班级"
            readonly={gradeList.length <= 1}
            placeholder="请选择"
            options={gradeList}
            fieldProps={{
              onSelect: (_, option) => {
                setSelectGrade(option)
                setStudentData([])
                form.setFieldsValue({
                  gradeName: option?.grade_name,
                  gradeCode: option?.grade_code,
                  className: option?.class_name,
                  classCode: option?.class_code,
                  studentName: undefined,
                  gender: undefined,
                  studentCode: undefined,
                })
                getStudents(option?.class_code, option.grade_code)
              },
              value: selectGrade?.value,
            }}
          />
          <ProFormSelect
            showSearch
            colProps={{ md: 8, sm: 24 }}
            name="studentName"
            label="学生姓名"
            rules={[{ required: true, message: '请选择学生' }]}
            fieldProps={{
              options: studentData,
              filterOption: (inputValue, option) => (option?.detail as any)?.includes(inputValue),
              getPopupContainer: (triggerNode) => triggerNode.parentElement,
              onChange: (value, option: any) => {
                form.setFieldsValue({
                  gender: option?.gender,
                  studentCode: option?.code,
                  parentName: option?.parentName,
                  parentPhone: option?.parentPhone,
                  age:
                    option?.year !== undefined ? dayjs().year() - Number(option?.year) : undefined,
                })
              },
            }}
          />
          <ProFormText colProps={{ md: 8, sm: 24 }} name="studentCode" label="学校学号" readonly />
          <ProFormRadio.Group
            colProps={{ md: 8, sm: 24 }}
            name="gender"
            label="性别"
            options={[
              {
                label: '男',
                value: '男',
              },
              {
                label: '女',
                value: '女',
              },
            ]}
          />
          <ProFormDigit
            label="年龄"
            colProps={{ md: 8, sm: 24 }}
            name="age"
            min={1}
            fieldProps={{ precision: 0 }}
          />
          <ProFormText colProps={{ md: 8, sm: 24 }} name="parentName" label="家长姓名" />
          <ProFormText
            colProps={{ md: 8, sm: 24 }}
            name="parentPhone"
            label="联系方式"
            rules={[
              {
                pattern: /^1[3456789]\d{9}$/,
                message: '请输入正确的手机号码',
              },
            ]}
          />
          <ProFormDigit
            label="缺勤天数"
            colProps={{ md: 8, sm: 24 }}
            name="absenceNumber"
            min={0}
            fieldProps={{ precision: 1 }}
          />
          <ProFormDatePicker
            fieldProps={{
              inputReadOnly: true,
              disabledDate: (current) => {
                // Can not select days before today and today
                return current && current > dayjs().endOf('day')
              },
            }}
            colProps={{ md: 8, sm: 24 }}
            name="disease_date"
            label="发病日期"
            rules={[{ required: true, message: '请选择发病日期' }]}
          />
          {modalProps?.data?.disease_type === '非传染病' || !modalProps?.data ? (
            <ProFormCheckbox.Group
              colProps={{ span: 24 }}
              name="symptomIdArr"
              label="主要症状"
              layout="horizontal"
              options={diseases}
              rules={[{ required: true, message: '请选择主要症状' }]}
            />
          ) : (
            <ProFormText
              readonly
              label="主要症状"
              fieldProps={{
                value: modalProps?.data?.contagion,
              }}
            />
          )}

          <ProFormSwitch
            name="is_diagnose"
            colProps={{ md: 8, sm: 24 }}
            label="是否就诊"
            unCheckedChildren="否"
            checkedChildren="是"
          />
          <ProFormSwitch
            name="is_resident"
            colProps={{ md: 8, sm: 24 }}
            label="是否内宿"
            unCheckedChildren="否"
            checkedChildren="是"
          />
          <ProFormText colProps={{ md: 8, sm: 24 }} name="teacherName" label="登记人" readonly />
          <ProFormSelect
            label="排查原因"
            name="reason"
            options={['学生因病缺勤', '传染病早期症状', '疑似传染病病人']}
          />
          <ProFormTextArea
            label="排查结果"
            name="check_result"
            placeholder={'请填写'}
            tooltip="排查结果最多不可超过255个字符"
            // rules={[{ required: true, message: '请填写排查结果' }]}
            fieldProps={{
              maxLength: 255,
              showCount: true,
            }}
          />
          <ProFormUploadButton
            name="attachment"
            listType="picture-card"
            accept="image/*"
            action={`/edu_api/upload/uploadSingle?subPath=noonInspection/${schoolInfo?.code}`}
            getValueFromEvent={(e: any) => {
              if (Array.isArray(e)) {
                return e
              }
              return e?.fileList
            }}
            max={2}
            fieldProps={{
              maxCount: 2,
              onRemove: async (file) => {
                if (file?.response?.data?.id) {
                  const res = await controllerUploadReplace({ id: file.response.data.id })
                  if (res?.errCode) {
                    message.error('删除失败，请联系管理员或稍后再试')
                    return false
                  }
                }
                return true
              },
              beforeUpload: (file) => {
                const isLt10M = file.size / 1024 / 1024 < 10
                if (!isLt10M) {
                  message.warning('图片大小不应超过10M!')
                }
                return isLt10M || Upload.LIST_IGNORE
              },
            }}
            label={'附件上传'}
          />
        </>
      ) : (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <Result status="warning" title="请联系当前学生的班主任为其登记信息" />
        </div>
      )}
    </DrawerForm>
  )
}

export default AddRecordModal
