import fontkit from '@pdf-lib/fontkit'
import { PDFDocument, rgb } from 'pdf-lib'
import dayjs from 'dayjs'
import {
  fetchWithTimeout,
  splitTextIntoLines,
  fetchWithBetterErrorHandling,
  getImageOrientation,
  loadImageByExtension,
  calculateImageSize,
  drawImageWithOrientation,
} from '../SchoolAdministration/TrainingManagement/utils'

export enum StatusEnum {
  '草稿' = 0,
  '待审批' = 1,
  '已同意' = 2,
  '已拒绝' = 3,
  '已结束' = 4,
}

function formatTrainingData(item: any) {
  const leftColumn = [
    { label: '教师1', value: item.teacher1_realName || '无' },
    { label: '教师2', value: item.teacher2_realName || '无' },
    { label: '学生', value: item.studentName || '无' },
    { label: '年级班级', value: `${item.gradeName || '无'} ${item.className || '无'}` },
  ]
  const rightColumn = [
    { label: '审批状态', value: StatusEnum[item.status as keyof typeof StatusEnum] || '无' },
    { label: '审批人', value: item.sp_teacherName || '无' },
    {
      label: '审批时间',
      value: item.sp_time ? dayjs(item.sp_time).format('YYYY-MM-DD HH:mm') : '无',
    },
    {
      label: '家访时间',
      value:
        item.start_time && item.end_time
          ? `${dayjs(item.start_time).format('MM-DD HH:mm')} - ${dayjs(item.end_time).format(
              'MM-DD HH:mm',
            )}`
          : '无',
    },
  ]

  const visitRecord = item.visitRecord || '无' // 单独提取家访记录

  return {
    title: `${item.teacher1_realName || ''} 和 ${item.teacher2_realName || ''}（${
      item.studentName || ''
    }）家访记录`,
    visitRecord,
    images: item.visitImg ? item.visitImg.split(',') : [],
    leftColumn,
    rightColumn,
  }
}

export async function generatePDF(trainingData: any[]) {
  try {
    const pdfDoc = await PDFDocument.create()
    pdfDoc.registerFontkit(fontkit)

    // 加载中文字体
    const fontUrl =
      'https://ysp-uploader-1301720845.cos.ap-nanjing.myqcloud.com/ziti/NotoSansSC-Regular.otf'
    const fontBytes = await fetchWithTimeout(fontUrl, { timeout: 10000 })

    const font = await pdfDoc.embedFont(fontBytes)

    // 页面配置常量
    const PAGE_WIDTH = 600
    const PAGE_HEIGHT = 800
    const MARGIN = 50
    const CONTENT_WIDTH = PAGE_WIDTH - MARGIN * 2
    const IMAGE_MAX_WIDTH = CONTENT_WIDTH
    const TEXT_LINE_HEIGHT = 15
    const TITLE_FONT_SIZE = 18
    const BODY_FONT_SIZE = 12
    const MAX_HEIGHT = 200

    for (const item of trainingData) {
      const formattedData = formatTrainingData(item)
      let page = pdfDoc.addPage([PAGE_WIDTH, PAGE_HEIGHT])
      let yPosition = PAGE_HEIGHT - MARGIN

      // 绘制标题
      page.drawText(formattedData.title, {
        x: MARGIN,
        y: yPosition,
        size: TITLE_FONT_SIZE,
        font,
        color: rgb(0.1, 0.1, 0.1),
      })
      yPosition -= 40

      // 绘制左右栏
      const columnWidth = (CONTENT_WIDTH - 20) / 2 // 20是列间距
      const maxLines = Math.max(formattedData.leftColumn.length, formattedData.rightColumn.length)

      for (let i = 0; i < maxLines; i++) {
        // 绘制左栏当前行
        if (i < formattedData.leftColumn.length) {
          const { label, value } = formattedData.leftColumn[i]
          const text = `${label}：${value}`
          const lines = splitTextIntoLines(text, font, BODY_FONT_SIZE, columnWidth)

          for (const line of lines) {
            page.drawText(line, {
              x: MARGIN,
              y: yPosition,
              size: BODY_FONT_SIZE,
              font,
              color: rgb(0.3, 0.3, 0.3),
            })
            yPosition -= TEXT_LINE_HEIGHT
          }
        }

        // 绘制右栏当前行
        if (i < formattedData.rightColumn.length) {
          const { label, value } = formattedData.rightColumn[i]
          const text = `${label}：${value}`
          const lines = splitTextIntoLines(text, font, BODY_FONT_SIZE, columnWidth)

          for (const line of lines) {
            page.drawText(line, {
              x: MARGIN + columnWidth + 20,
              y: yPosition,
              size: BODY_FONT_SIZE,
              font,
              color: rgb(0.3, 0.3, 0.3),
            })
            yPosition -= TEXT_LINE_HEIGHT
          }
        }
      }

      // 单独绘制家访记录
      yPosition -= 10 // 增加间距
      const recordLines = splitTextIntoLines(
        `家访记录：${formattedData.visitRecord}`,
        font,
        BODY_FONT_SIZE,
        CONTENT_WIDTH,
      )
      for (const line of recordLines) {
        page.drawText(line, {
          x: MARGIN,
          y: yPosition,
          size: BODY_FONT_SIZE,
          font,
          color: rgb(0.3, 0.3, 0.3),
        })
        yPosition -= TEXT_LINE_HEIGHT
      }

      // 处理图片
      if (
        formattedData.images &&
        Array.isArray(formattedData.images) &&
        formattedData.images.length > 0
      ) {
        yPosition -= 10 // 图片与内容的间距

        // 添加图片标题
        page.drawText('家访照片：', {
          x: MARGIN,
          y: yPosition,
          size: BODY_FONT_SIZE,
          font,
          color: rgb(0.1, 0.1, 0.1),
        })
        yPosition -= TEXT_LINE_HEIGHT

        let currentX = MARGIN
        let currentRowHeight = 0

        for (const imageUrl of formattedData.images) {
          try {
            const imageBytes = await fetchWithBetterErrorHandling(imageUrl)
            const orientation = await getImageOrientation(imageBytes)
            const image = await loadImageByExtension(pdfDoc, imageBytes, imageUrl)

            const { scaledWidth, scaledHeight } = calculateImageSize(
              image,
              IMAGE_MAX_WIDTH / 2,
              MAX_HEIGHT,
              orientation,
            )

            // 检查是否需要换行
            if (currentX + scaledWidth > PAGE_WIDTH - MARGIN) {
              yPosition -= currentRowHeight + 10
              currentX = MARGIN
              currentRowHeight = 0
            }

            // 检查是否需要新页面
            if (yPosition - scaledHeight < MARGIN) {
              page = pdfDoc.addPage([PAGE_WIDTH, PAGE_HEIGHT]) // 创建新页面
              yPosition = PAGE_HEIGHT - MARGIN // 重置 yPosition 到新页面顶部
              currentX = MARGIN
              currentRowHeight = 0
            }

            // 绘制图片
            await drawImageWithOrientation(
              page,
              image,
              orientation,
              currentX,
              yPosition - scaledHeight,
              scaledWidth,
              scaledHeight,
              PAGE_WIDTH,
            )

            // 更新位置
            currentX += scaledWidth + 10
            currentRowHeight = Math.max(currentRowHeight, scaledHeight)
          } catch (error) {
            console.error(`图片加载失败: ${imageUrl}`, error)
          }
        }

        yPosition -= currentRowHeight + 20
      }
    }

    // 生成并下载PDF文件
    const pdfBytes = await pdfDoc.save()
    return {
      status: true,
      data: pdfBytes,
      msg: 'PDF生成成功',
    }
  } catch (error) {
    return {
      status: false,
      msg: error,
    }
  }
}
