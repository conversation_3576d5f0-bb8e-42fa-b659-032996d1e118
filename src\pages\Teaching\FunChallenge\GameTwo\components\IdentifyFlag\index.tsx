import { Drawer } from 'antd'
import React, { useEffect, useState } from 'react'
import Labyrinth from '../Labyrinth'
import nationalFlag from '../../assets/images/nationalFlag.png'
import redFlag from '../../assets/images/redFlag.png'
import partyFlag from '../../assets/images/partyFlag.png'
import nextSide from '../../assets/images/nextSide.png'
import nextStage from '../../assets/images/nextStage.png'
import styles from './index.less'
import Common from '../common'
import Audio from '../Audio'
import three02 from '../Audio/three02.mp3'
import three03 from '../Audio/three03.mp3'
import three04 from '../Audio/three04.mp3'
import three05 from '../Audio/three05.mp3'
import AnswerAncientPoetry from '../AnswerAncientPoetry'
import { secondGradePoetryList } from '../data'
import ThroughForest from '../ThroughForest'

const IdentifyFlag: React.FC<{ currentStudent: any }> = ({ currentStudent }) => {
  const [nextOpen, setNextOpen] = useState<boolean>(false)
  const [identifyFlagType, setIdentifyFlagType] = useState<string>('国旗')
  const [mp3Url, setMp3Url] = useState<string>(three02)
  const [Btn, setBtn] = useState<boolean>(true)
  const [commonText, setCommonText] = useState<{
    /** 是否结束 */
    status: boolean
    message: string
  }>({
    status: false,
    message: '',
  })

  useEffect(() => {
    if (currentStudent && currentStudent.classes && currentStudent.classes[0]) {
      setCommonText({ status: true, message: '请说出这面旗帜的名称！' })
    }
  }, [currentStudent])

  const IdentifyFlagType = () => {
    switch (identifyFlagType) {
      case '国旗':
        return (
          <>
            <img className={styles.flag} src={nationalFlag} alt="" srcSet="" />
            <p className={styles.next}>
              {Btn && (
                <img
                  src={nextSide}
                  style={{
                    position: 'absolute',
                    left: '55%',
                    bottom: '-13vh',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    backgroundSize: '100% 100%',
                    transform: 'translate(-50%, -50%)',
                    height: '60px',
                    width: '180px',
                  }}
                  onClick={() => {
                    setBtn(false)
                    setMp3Url(three03)
                    setTimeout(() => {
                      setIdentifyFlagType('党旗')
                      setMp3Url(three02)
                      setBtn(true)
                    }, 2000)
                  }}
                />
              )}
            </p>
          </>
        )
      case '党旗':
        return (
          <>
            <img className={styles.flag} src={partyFlag} alt="" srcSet="" />
            <p className={styles.next}>
              {Btn && (
                <img
                  src={nextSide}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    bottom: '-13vh',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    backgroundSize: '100% 100%',
                    transform: 'translate(-50%, -50%)',
                    height: '60px',
                    width: '180px',
                  }}
                  onClick={() => {
                    setBtn(false)
                    setMp3Url(three04)
                    setTimeout(() => {
                      setIdentifyFlagType('队旗')
                      setMp3Url(three02)
                      setBtn(true)
                    }, 2000)
                  }}
                />
              )}
            </p>
          </>
        )
      case '队旗':
        return (
          <>
            <img className={styles.flag} src={redFlag} alt="" srcSet="" />
            <p className={styles.next}>
              {Btn && (
                <img
                  src={nextStage}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    bottom: '-13vh',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    backgroundSize: '100% 100%',
                    transform: 'translate(-50%, -50%)',
                    height: '60px',
                    width: '180px',
                  }}
                  onClick={() => {
                    setBtn(false)
                    setMp3Url(three05)
                    setTimeout(() => {
                      setNextOpen(true)
                    }, 2000)
                  }}
                />
              )}
            </p>
          </>
        )
      default:
        return (
          <>
            <img className={styles.flag} src={nationalFlag} alt="" srcSet="" />
            <p className={styles.next}>
              {Btn && (
                <img
                  src={nextSide}
                  style={{
                    position: 'fixed',
                    left: '50%',
                    bottom: '18vh',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    backgroundSize: '100% 100%',
                    transform: 'translate(-50%, -50%)',
                    height: '60px',
                    width: '180px',
                  }}
                  onClick={() => {
                    setBtn(false)
                    setMp3Url(three03)
                    setTimeout(() => {
                      setIdentifyFlagType('党旗')
                      setMp3Url(three02)
                      setBtn(true)
                    }, 2000)
                  }}
                />
              )}
            </p>
          </>
        )
    }
  }

  return (
    <>
      <Audio src={mp3Url} />

      <div className={styles.videoBox}>
        <div className={styles.titleBox}>
          <h1 className={styles.title}>欢迎来到长安八景“太白积雪”</h1>
          <div className={styles.described}>——夏之光</div>
        </div>
        <span className={styles.IdentifyFlagBox}>
          <IdentifyFlagType />
        </span>
      </div>
      <Common
        currentHello={commonText.message}
        callback={() => {
          if (commonText.status) return
        }}
      />
      <Drawer
        width={'100vw'}
        open={nextOpen}
        onClose={() => setNextOpen(false)}
        headerStyle={{ display: 'none' }}
        bodyStyle={{ padding: 0 }}
      >
        <ThroughForest currentStudent={currentStudent} />
        {/* <Labyrinth currentStudent={currentStudent} /> */}
      </Drawer>
    </>
  )
}
export default IdentifyFlag
