{"name": "edu-platform-web", "version": "1.21.2", "private": true, "scripts": {"start": "umi dev", "build": "umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "dependencies": {"@ant-design/charts": "^1.4.0", "@ant-design/pro-components": "^2.7.0", "@ant-design/pro-layout": "^6.38.4", "@pdf-lib/fontkit": "^1.1.1", "@umijs/plugin-qiankun": "^2.37.2", "@wangeditor/editor": "^5.1.0", "@wangeditor/editor-for-react": "^1.0.3", "antd": "^4.20.6", "antd-img-crop": "^4.2.3", "compression-webpack-plugin": "6.1.1", "cos-js-sdk-v5": "^1.8.4", "exif-js": "^2.3.0", "exifr": "^7.1.3", "intro.js": "^6.0.0", "pdf-lib": "^1.17.1", "rc-resize-observer": "^1.4.0", "rc-tween-one": "^3.0.6", "react": "^18.1.0", "react-dom": "^18.1.0", "react-zoom-pan-pinch": "^3.7.0", "sortablejs": "^1.15.0", "swiper": "^8.2.6", "umi": "^3.5.24"}, "devDependencies": {"@types/react": "^18.0.9", "@types/react-dom": "^18.0.5", "@types/sortablejs": "^1.13.0", "@umijs/fabric": "^2.10.2", "@umijs/preset-react": "^1.8.31", "@umijs/test": "^3.5.24", "btoa": "^1.2.1", "file-loader": "^6.2.0", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "typescript": "^4.1.2", "yorkie": "^2.0.0"}}