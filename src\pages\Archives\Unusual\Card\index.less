.card {
  display: flex;
  width: 260px;
  height: 130px;
  margin: 0 16px;
  padding: 24px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--card-shadow);
  cursor: pointer;
  transition: box-shadow 0.2s;
  .left {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 72px;
    height: 72px;
    margin-right: 16px;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    img {
      width: 55%;
    }
  }
  .right {
    .title {
      margin-bottom: 16px;
      color: var(--card-title);
      font-size: var(--font-size);
      line-height: 24px;
    }
    .descriptions {
      color: var(--font-color);
      font-size: calc(var(--font-size) - 2px);
      line-height: 20px;
    }
  }
  &:hover {
    box-shadow: 0 5px 3px var(--primary-color-2);
  }
}
