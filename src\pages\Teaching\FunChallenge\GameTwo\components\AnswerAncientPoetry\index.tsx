import React, { useEffect, useState } from 'react'
import { firstGradePoetryList, secondGradePoetryList } from '../data'
import type { RadioChangeEvent } from 'antd'
import { Radio } from 'antd'
import { createLoyou as SubmitAPI } from '@/services/edu-platform-web/ancient_poetry_answers'
import EndBg01 from '../../assets/images/endBg01.png'
import EndBg02 from '../../assets/images/endBg02.png'
import EndBtn from '../../assets/images/EndBtn.png'
import gs01 from '../Audio/gs01.mp3'
import gscg from '../Audio/gscg.mp3'
import gssb from '../Audio/gssb.mp3'
import styles from './index.less'
import Audio from '../Audio'
import Common from '../common'
import moment from 'moment'
import { useLocation, useModel } from 'umi'
import { forthright01, school } from '../../assets/images'

const AnswerAncientPoetry: React.FC<{
  currentStudent: any
  currentPoetry: string
  isSecondGrade?: boolean
}> = ({ currentStudent, currentPoetry, isSecondGrade = false }) => {
  const { pathname } = useLocation()
  const { content, author, correct, mistake } = (
    isSecondGrade ? secondGradePoetryList : firstGradePoetryList
  ).get(currentPoetry) as any
  const { initialState } = useModel('@@initialState')
  const { currentUser } = initialState || {}
  const [value, setValue] = useState('')
  const [type, setType] = useState<string>('start')
  const [radioDisabled, setRadioDisabled] = useState(false)
  const [EndSatet, setEndSatet] = useState<boolean>()
  const [audioSrc, setAudioSrcFun] = useState<string>(gs01)
  const [commonText, setCommonText] = useState<{
    /** 是否结束 */
    status: boolean
    message: string
  }>({
    status: false,
    message: '',
  })

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value)
    if (e.target.value === correct) {
      setRadioDisabled(true)
      setAudioSrcFun(gscg)
      setEndSatet(true)
      setCommonText({ status: true, message: '恭喜你，回答正确！' })
      setTimeout(() => {
        setType('end')
      }, 3000)
    } else {
      setRadioDisabled(true)
      setAudioSrcFun(gssb)
      setEndSatet(false)
      setCommonText({ status: true, message: '回答错误！再接再厉哦。' })
      setTimeout(() => {
        setType('end')
      }, 3500)
    }
  }
  useEffect(() => {
    setCommonText({ status: false, message: '请选择正确的答案' })
  }, [currentStudent.classes])

  const Show: any = () => {
    switch (type) {
      case 'start':
        return (
          <>
            <Audio src={audioSrc} />
            <Common
              currentHello={commonText.message}
              callback={() => {
                if (commonText.status) return
              }}
            />
            <div className={styles.titleBox}>
              <h1 className={styles.title}>
                欢迎来到长安八景“{isSecondGrade ? '碧峰林海' : '太白积雪'}”
              </h1>
              <div className={styles.described}>——夏之{isSecondGrade ? '趣' : '光'}</div>
            </div>
            <div className={styles.ancientPoetry}>
              <h1 className={styles.title}>{currentPoetry}</h1>
              <div className={styles.authorName}>{author}</div>
              <h1>{content.split('。').shift()}</h1>
              <h1>{content.split('。')[1]}</h1>
              <div className={styles.options}>
                <Radio.Group onChange={onChange} value={value}>
                  <Radio value={correct} disabled={radioDisabled}>
                    {correct}
                  </Radio>
                  <Radio value={mistake} disabled={radioDisabled}>
                    {mistake}
                  </Radio>
                </Radio.Group>
              </div>
            </div>
          </>
        )
      case 'end':
        return (
          <div className={styles.endBox}>
            <img className={styles.endImg} src={EndSatet ? EndBg01 : EndBg02} alt="" srcSet="" />
            {pathname !== '/entertainment/gameTwo' && (
              <img
                className={styles.endBtn}
                src={EndBtn}
                alt=""
                onClick={async () => {
                  sessionStorage.setItem(
                    'gradeCode',
                    `${currentStudent.classes[0]?.grade_code};${currentStudent?.classes[0]?.code}`,
                  )
                  await SubmitAPI({
                    gameName: '乐游长安',
                    className: currentStudent?.classes[0]?.name,
                    gradeName: currentStudent?.classes[0]?.grade_name,
                    schoolCode: currentUser?.enterprise?.code,
                    schoolName: currentUser?.enterprise?.name,
                    studentCode: currentStudent?.code,
                    studentName: currentStudent?.name,
                    answerTime: moment(new Date()).format('YYYY-MM-DD'),
                  })
                  window.location.reload()
                }}
              />
            )}
          </div>
        )
      default:
        return (
          <>
            <Audio src={audioSrc} />
            <Common
              currentHello={commonText.message}
              callback={() => {
                if (commonText.status) return
              }}
            />
            <div className={styles.ancientPoetry}>
              <h1 className={styles.title}>{currentPoetry}</h1>
              <div className={styles.authorName}>{author}</div>
              <h1>{content.split('。').shift()}</h1>
              <h1>{content.split('。')[1]}</h1>
              <div className={styles.options}>
                <Radio.Group onChange={onChange} value={value}>
                  <Radio value={correct} disabled={radioDisabled}>
                    {correct}
                  </Radio>
                  <Radio value={mistake} disabled={radioDisabled}>
                    {mistake}
                  </Radio>
                </Radio.Group>
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <div
      className={styles.ancientPoetryBox}
      style={{
        backgroundImage: `url(${isSecondGrade ? forthright01 : school})`,
      }}
    >
      <Show />
    </div>
  )
}
export default AnswerAncientPoetry
