/**
 * @description: 转入申请
 */
import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, message } from 'antd'
import React, { useRef, useState } from 'react'
import styles from './index.less'
import { envjudge } from '@/utils'
import { getColumns, getMetas, UnusualStatus } from './columns'
import AddInfo from './AddInfo'
import type { AddApplicationType } from '@/services/edu-platform-web/archives_unusual'
import {
  addApplication,
  queryList,
  deleteApplication,
  putApplication,
  revokeApplication,
  resubmitApplication,
  submitApplication,
} from '@/services/edu-platform-web/archives_unusual'
import CommonList from '@/components/CommonList'
import type { ActionType } from '@ant-design/pro-components'
import { useModel } from 'umi'

/** 异动管理数据格式兼容 */
export const unusualFormatData = (list: any) => {
  if (!list) return []
  return list.map(
    (item: { student_transfer_in_apply: any; status: string; rejectReason: string }) => {
      const { student_transfer_in_apply, status, rejectReason } = item
      const newStatus =
        status === UnusualStatus['草稿'] && rejectReason && rejectReason?.length > 0
          ? UnusualStatus['已拒绝']
          : status
      return {
        ...item,
        ...student_transfer_in_apply,
        status: newStatus,
      }
    },
  )
}

const TransferApplication: React.FC = () => {
  const { initialState } = useModel('@@initialState')
  const { currentUser } = initialState || {}
  const env_screen = envjudge()
  const isMobile = env_screen.includes('mobile')
  const childRef = useRef<ActionType>()
  const [open, setOpen] = useState(false)
  const [addInfoData, setAddInfoData] = useState<any>()
  const [form] = Form.useForm<{ name: string; company: string }>()

  const onSubmit = async (values: AddApplicationType) => {
    const res = await addApplication({
      ...values,
      creator: values?.from,
      creatorName: values?.fromName,
    })
    if (res?.errCode) {
      return message.warning(
        `新增转入申请失败，请联系管理员或稍后再试  ${res?.msg ?? res?.message}`,
      )
    }
    message.success('新增转入申请成功')
    setOpen(false)
    childRef.current?.reload()
  }

  /** 刪除转入申请 */
  const removalApplication = async (record: any) => {
    const res = await deleteApplication(record?.applyId)
    if (res?.errCode) {
      return message.warning(`删除失败，请联系管理员或稍后再试  ${res?.msg ?? res?.message}`)
    }
    message.success('删除成功')
    childRef.current?.reload()
  }

  /** 提交转入申请 */
  const submit = async (record: any) => {
    const res = await submitApplication(record?.id)
    if (res?.errCode) {
      return message.warning(`提交失败，请联系管理员或稍后再试  ${res?.msg ?? res?.message}`)
    }
    message.success('提交成功')
    childRef.current?.reload()
  }
  /** 重新提交转入申请 */
  const reSubmit = async (record: any) => {
    const res = await resubmitApplication(record?.id)
    if (res?.errCode) {
      return message.warning(`重新提交失败，请联系管理员或稍后再试  ${res?.msg ?? res?.message}`)
    }
    message.success('重新提交成功')
    childRef.current?.reload()
  }

  /** 编辑转入申请 */
  const editApplication = async (values: any) => {
    const res = await putApplication(values?.id, values)
    if (res?.errCode) {
      return message.warning(`编辑失败，请联系管理员或稍后再试  ${res?.msg ?? res?.message}`)
    }
    message.success('编辑成功')
    setOpen(false)
    childRef.current?.reload()
  }

  /** 撤回转入申请 */
  const revocationApplication = async (record: any) => {
    const res = await revokeApplication(record?.id)
    if (res?.errCode) {
      return message.warning(
        `${
          record?.status === UnusualStatus['已拒绝'] ? '重新申请' : '撤回'
        }失败，请联系管理员或稍后再试  ${res?.msg ?? res?.message}`,
      )
    }
    message.success(`${record?.status === UnusualStatus['已拒绝'] ? '重新申请' : '撤回'}成功`)
    childRef.current?.reload()
  }

  /** 处理 转入申请事件 */
  const handEvents = (type: string, record: any) => {
    switch (type) {
      case 'edit':
        setOpen(true)
        setAddInfoData(record)
        break
      case 'delete':
        removalApplication(record)
        break
      case 'submit':
        submit(record)
        break
      case 'replead':
        reSubmit(record)
        break
      case 'revocation':
        revocationApplication(record)
        break
    }
  }

  return (
    <div className={styles.transferApplication}>
      <CommonList
        ref={childRef}
        params={{}}
        request={async (params: any, sort, filter) => {
          const { pageSize, current: page } = params
          const { errCode, list, count, total } = await queryList({
            creator: currentUser?.username,
            offset: Number((page || 1) - 1) * Number(pageSize),
            limit: Number(pageSize),
            chooseSchool: currentUser?.enterpriseCode || '',
            isHandled: false,
          })
          if (errCode) {
            message.error('转入申请列表获取失败')
            return {
              success: true,
              data: [],
              total: 0,
            }
          } else {
            const newlist = unusualFormatData(list)
            console.log(newlist)

            return {
              success: true,
              data: newlist || [],
              total: count || total,
            }
          }
        }}
        columns={getColumns({
          handEvents,
        })}
        metas={getMetas({
          handEvents,
        })}
        toolBarRender={
          env_screen.includes('mobile')
            ? false
            : () => [
                <Button
                  key="button"
                  icon={<PlusOutlined />}
                  type="primary"
                  onClick={() => {
                    setOpen(true)
                    setAddInfoData(undefined)
                  }}
                >
                  新增
                </Button>,
              ]
        }
      />
      {env_screen.includes('mobile') && (
        <div className={styles.footer}>
          <Button
            icon={<PlusOutlined />}
            className={styles.addBtn}
            onClick={() => {
              setOpen(true)
              setAddInfoData(undefined)
            }}
          >
            新增
          </Button>
        </div>
      )}
      <AddInfo
        width={isMobile ? '100vw' : '40vw'}
        form={form}
        onFinish={!!addInfoData ? editApplication : onSubmit}
        open={open}
        data={addInfoData}
        onClose={() => setOpen(false)}
      />
    </div>
  )
}
export default TransferApplication
