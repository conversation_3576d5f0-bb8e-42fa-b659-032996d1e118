import { Button, message, Segmented, Spin } from 'antd'
import { ExportOutlined } from '@ant-design/icons'
import React, { useEffect } from 'react'
import styles from './index.less'
import CommonTable from './commonTable'
import { controllerExaminationShow as getDetailFrom } from '@/services/score-analysis/examination'
import { getGradeCollect } from '@/services/edu-platform-web/summary_by_examination'
import { getNjName } from '@/utils'
import { useModel } from 'umi'

interface GradeSummaryGradesProps {
  examinationId: string
}

const GradeSummaryGrades: React.FC<GradeSummaryGradesProps> = ({ examinationId }) => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo } = initialState || {}
  const [dataSource, setDataSource] = React.useState<any[]>([])
  const [selectSubject, setSelectSubject] = React.useState<string>()
  const [subjectOptions, setSubjectOptions] = React.useState<any[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)
  const [gradeOptions, setGradeOptions] = React.useState<any[]>([])
  const [selectGrade, setSelectGrade] = React.useState<any>(null)

  /** 获取数据 */
  const getData = async (subjectCode?: string, gradeCode?: string) => {
    setLoading(true)
    const res = await getGradeCollect({
      examinationId,
      subjectCode,
      gradeCode,
    })
    if (res?.errCode) {
      setLoading(false)
      return message.warning(`获取数据失败，请联系管理员或稍后再试！${res?.msg}`)
    }
    setLoading(false)

    const newData = [
      ...res?.grade,
      {
        className: '合计',
        teacherName: '-',
        ykrs: res?.grade_ykrs,
        skrs: res?.grade_skrs,
        zrs: res?.grade_zrs,
        ratio_1: res?.grade_ratio_1,
        yxl: res?.grade_yxl,
        rs_4: res?.grade_rs_4,
        ratio_4: res?.grade_ratio_4,
        rs_3: res?.grade_rs_3,
        ratio_3: res?.grade_ratio_3,
        rs_5: res?.grade_rs_5,
        ratio_5: res?.grade_ratio_5,
        bhg: res?.grade_bhg,
        bhgl: res?.grade_bhgl,
        qkrs: res?.grade_qkrs,
        lh: res?.grade_lh,
        lhl: res?.grade_lhl,
        pjf: res?.grade_pjf,
      },
    ]
    setDataSource(newData || [])
  }

  const resetForm = async () => {
    const res = (await getDetailFrom({ id: examinationId })) as ResType<any>
    if (res?.errCode) {
      message.error(res.message || '获取数据失败，请联系管理员或稍后再试')
    } else {
      const gradeArr = res?.examGrads.sort(
        (a: SCORE_API.ExamGradeInfo, b: SCORE_API.ExamGradeInfo) =>
          parseInt(a?.gradeCode + '') - parseInt(b.gradeCode + ''),
      )

      const newGradeData: any = [].map.call(gradeArr, (v: any) => {
        return {
          label: `${getNjName(v.gradeName, schoolInfo?.section_code)}`,
          value: v.gradeCode,
          code: v.gradeCode,
          subjectData: v.subjects,
        }
      })

      // 初始化选中第一个年级
      const initialGrade = newGradeData?.[0]
      setSelectGrade(initialGrade?.value || null)
      const selectedGrade = newGradeData.find((grade: any) => grade.value === initialGrade?.value)
      const newData: any = selectedGrade?.subjectData

      const subData = newData?.map((v: any) => {
        return {
          label: v.subjectName,
          value: v.subjectCode,
        }
      })
      setSubjectOptions(subData)
      setGradeOptions(newGradeData)
      setSelectSubject(subData?.[0]?.value || null)
      getData(subData?.[0]?.value || null, initialGrade?.value || null)
    }
  }

  // 当年级选择变化时，更新学科选项
  const handleGradeChange = (value: string) => {
    setSelectGrade(value)
    const selectedGrade = gradeOptions.find((grade) => grade.value === value)
    if (selectedGrade) {
      const newData = selectedGrade.subjectData
      const subData = newData?.map((v: any) => {
        return {
          label: v.subjectName,
          value: v.subjectCode,
        }
      })
      setSubjectOptions(subData)
      const firstSubjectCode = subData?.[0]?.value || null
      setSelectSubject(firstSubjectCode)
      getData(firstSubjectCode, value)
    }
  }

  const handleDownload = async () => {
    try {
      const baseUrl = '/api_score/download/summary_by_grade'
      const params = new URLSearchParams()
      params.append('examinationId', examinationId || '')
      params.append('subjectCode', selectSubject || '')
      params.append('gradeCode', selectGrade || '')
      const url = `${baseUrl}?${params.toString()}`
      const a = document.createElement('a')
      a.href = url
      a.download = '成绩汇总.xlsx'
      document.body.appendChild(a)
      a.click()
      a.remove()
      message.success('导出成功')
    } catch (error) {
      message.error('导出失败，请联系管理员或稍后再试！' + error)
    }
  }

  useEffect(() => {
    resetForm()
  }, [])
  return (
    <>
      <header className={styles.schoolHeader}>
        <div>
          <div
            style={{
              marginBottom: 10,
            }}
          >
            年级：
            <Segmented
              value={selectGrade}
              options={gradeOptions}
              onChange={(value) => {
                setSelectGrade(String(value))
                handleGradeChange(String(value))
              }}
            />
          </div>
          <div>
            学科：
            <Segmented
              value={selectSubject}
              options={subjectOptions}
              onChange={(value) => {
                setSelectSubject(String(value))
                getData(String(value), selectGrade)
              }}
            />
          </div>
        </div>

        <Button
          type="primary"
          onClick={handleDownload}
          icon={<ExportOutlined />}
          disabled={dataSource.length === 0}
        >
          导出
        </Button>
      </header>
      <Spin spinning={loading} tip="数据获取中，请稍候...">
        <CommonTable type="grade" data={dataSource} />
      </Spin>
    </>
  )
}
export default GradeSummaryGrades
