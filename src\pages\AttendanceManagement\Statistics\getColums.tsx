import { getNjName } from '@/utils'
import type { ProColumns } from '@ant-design/pro-components'
import { Button, Col, Divider, Row, Space, Tag } from 'antd'
import moment from 'moment'
import styles from './index.less'

const getTeacherColums = (): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
  },
  {
    title: '教师姓名',
    dataIndex: 'realName',
    key: 'realName',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '打卡班次',
    dataIndex: 'clockInTime',
    key: 'clockInTime',
    align: 'center',
    valueType: 'dateTime',
    width: 150,
    render: (_, record: any) => {
      const { check_time, type } = record?.attendance_config_period_time || {}
      return <div>{type ? `${type}-（${check_time?.substring(0, 5)}）` : record.past_period}</div>
    },
  },
  {
    title: '打卡状态',
    dataIndex: 'type',
    key: 'type',
    align: 'center',
    width: 70,
    render: (_, record: any) =>
      record.type === 1 ? (
        <Tag color="#87d068">正常</Tag>
      ) : record.type === 2 ? (
        <Tag color="#f50">请假</Tag>
      ) : record.type === 3 ? (
        <Tag color="#2db7f5">外出</Tag>
      ) : (
        <Tag>缺卡</Tag>
      ),
  },
  {
    title: '打卡时间',
    dataIndex: 'clockInTime',
    key: 'clockInTime',
    align: 'center',
    valueType: 'dateTime',
    width: 150,
  },
  {
    title: '打卡地点',
    dataIndex: 'describe',
    key: 'describe',
    align: 'center',
    width: 150,
    hideInTable: true,
  },
]

const getTeacherMetas = (): any => {
  return {
    title: {
      dataIndex: 'realName',
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <Button
            type="text"
            style={{
              padding: 0,
              maxWidth: 'calc(100vw - 100px)',
              textAlign: 'left',
              color: '#333',
              fontWeight: 'bold',
            }}
            className="ant-table-cell-ellipsis"
          >
            {entity?.realName}
            <span
              style={{
                paddingInline: 4,
              }}
            />
            {entity.type === 1 ? (
              <Tag color="#87d068">正常</Tag>
            ) : entity.type === 2 ? (
              <Tag color="#f50">请假</Tag>
            ) : entity.type === 3 ? (
              <Tag color="#2db7f5">外出</Tag>
            ) : (
              <Tag>缺卡</Tag>
            )}
          </Button>
        )
      },
    },
    subTitle: {
      render: (dom: React.ReactNode, entity: any) => {
        const { check_time, type } = entity?.attendance_config_period_time || {}
        return <div>{type ? `${type}-（${check_time?.substring(0, 5)}）` : entity.past_period}</div>
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <Space>{moment(entity?.createdAt).format('YYYY-MM-DD HH:mm')}</Space>
          </div>
        )
      },
    },
  }
}

const getStudentColums = (
  sectionData?: API.workRestTime[],
  sectionCode?: sectionCode,
): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
  },
  {
    title: '点名教师',
    dataIndex: 'bzr_teacherName',
    key: 'bzr_teacherName',
    ellipsis: true,
    width: 100,
    align: 'center',
    render: (_, record) => {
      return record?.bzr_teacherName || record?.teacherName
    },
  },
  {
    title: '点名类型',
    dataIndex: 'courseCode',
    key: 'courseCode',
    ellipsis: true,
    width: 100,
    align: 'center',
    render: (_, record) => {
      return record?.courseName ? (
        <Tag
          color="purple"
          style={{
            color: 'purple',
          }}
        >
          任课教师点名
        </Tag>
      ) : (
        <Tag
          color="geekblue"
          style={{
            color: '#531dab',
          }}
        >
          班主任点名
        </Tag>
      )
    },
  },
  {
    title: '点名班级',
    dataIndex: 'className',
    key: 'className',
    ellipsis: true,
    width: 120,
    align: 'center',
    render: (_, record: any) => {
      return `${getNjName(record?.gradeName, sectionCode)} ${record?.className}`
    },
  },
  {
    title: '点名课程',
    dataIndex: 'courseName',
    key: 'courseName',
    ellipsis: true,
    width: 120,
    align: 'center',
    render: (_, record: any) => {
      if (record?.courseName) {
        const title = sectionData?.[record.orderIndex - 1]?.title
        return `${title ? title : ''} ${record?.courseName}`
      } else {
        return '-'
      }
    },
  },
  {
    title: '上课日期',
    dataIndex: 'clockInTime',
    key: 'clockInTime',
    ellipsis: true,
    width: 100,
    valueType: 'date',
    align: 'center',
  },
  {
    title: '点名时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    ellipsis: true,
    width: 150,
    valueType: 'dateTime',
    align: 'center',
  },
  {
    title: '点名结果',
    dataIndex: 'result',
    key: 'result',
    hideInForm: true,
    align: 'center',
    width: 220,
    render: (_, record) => {
      return (
        <Row gutter={[8, 8]} justify="center">
          <Col span={8} className={styles.col}>
            应到：{record?.yds}
          </Col>
          <Col span={8} className={styles.col}>
            实到：{record?.sds}
          </Col>
          <Col span={8} className={styles.col}>
            缺席：{record?.qqs}
          </Col>
          <Col span={8} className={styles.col}>
            已请假：{record?.earlyPermissionNumber ?? 0}
          </Col>
          <Col span={8} className={styles.col}>
            未请假：{record?.noShowLeaveNumber ?? 0}
          </Col>
          <Col span={8} className={styles.col}>
            其他：{record?.qjs}
          </Col>
        </Row>
      )
    },
  },
]

const getStudentMetas = (sectionData?: API.workRestTime[], sectionCode?: sectionCode): any => {
  return {
    title: {
      dataIndex: 'bzr_teacherName',
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <Button
            type="text"
            style={{
              padding: 0,
              maxWidth: 'calc(100vw - 100px)',
              textAlign: 'left',
              color: '#333',
              fontWeight: 'bold',
            }}
            className="ant-table-cell-ellipsis"
          >
            {entity?.bzr_teacherName || entity?.teacherName}
            <span
              style={{
                paddingInline: 4,
              }}
            />
            {entity?.courseName ? (
              <Tag
                color="purple"
                style={{
                  color: 'purple',
                }}
              >
                任课教师点名
              </Tag>
            ) : (
              <Tag
                color="geekblue"
                style={{
                  color: '#531dab',
                }}
              >
                班主任点名
              </Tag>
            )}
          </Button>
        )
      },
    },
    subTitle: {
      render: (dom: React.ReactNode, entity: any) => {
        const title = sectionData?.[entity.orderIndex - 1]?.title
        return `【${moment(entity?.clockInTime).format('YYYY-MM-DD')}】${getNjName(
          entity?.gradeName,
          sectionCode,
        )} ${entity?.className} /${title ? title : ''} ${entity?.courseName} `
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <Space>点名时间：{moment(entity?.createdAt).format('YYYY-MM-DD HH:mm')}</Space>
            <Divider
              style={{
                margin: '8px 0',
              }}
            />
            <Row gutter={[8, 8]}>
              <Col span={8} className={styles.col}>
                应到：{entity?.yds}
              </Col>
              <Col span={8} className={styles.col}>
                实到：{entity?.sds}
              </Col>
              <Col span={8} className={styles.col}>
                缺席：{entity?.qqs}
              </Col>
              <Col span={8} className={styles.col}>
                已请假：{entity?.earlyPermissionNumber ?? 0}
              </Col>
              <Col span={8} className={styles.col}>
                未请假：{entity?.noShowLeaveNumber ?? 0}
              </Col>
              <Col span={8} className={styles.col}>
                其他：{entity?.qjs}
              </Col>
            </Row>
          </div>
        )
      },
    },
  }
}
export { getTeacherColums, getTeacherMetas, getStudentColums, getStudentMetas }
