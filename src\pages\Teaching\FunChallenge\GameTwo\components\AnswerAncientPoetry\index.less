
.ancientPoetryBox{
  width: 100%;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  .ancientPoetry {
  min-width: 40vw;
  position: absolute;
  top: 55%;
  left: 50%;
  letter-spacing: 5px;
  padding: 40px 20px;
  transform: translate(-50%, -50%);
  backdrop-filter: blur(10px) brightness(90%);
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  .title {
    margin-bottom: 8px;
    text-align: center;
    color: #000000;
  }
  h1{
    text-align: center;
    color: #000000;
  }
  .authorName {
    margin: 20px 0;
    font-weight: 600;
    font-size: 1rem;
    text-align: center;
    color: #000000;
  }
  .options{
    text-align: center;
    margin-top: 8vh;
    :global{
      .@{ant-prefix}-radio-wrapper{
        font-size: 1.2rem !important;
        margin: 0 15px;
        color: #000000;
      }
    }
  }
}
}
.endBox{
  width: 50vw;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .endImg{
    width: 80%;
  }
  .endBtn{
    width: 30%;
    margin-top: 50px;
  }
}
.titleBox {
  position: absolute;
  top: 10vh;
  left: 50%;
  z-index: 10;
  width: 500px;
  letter-spacing: 5px;
  transform: translate(-50%, -50%);
  .title {
    width: 100%;
    color: rgba(72, 142, 192, 1);
    font-weight: bold;
    font-size: 1.5rem;
    text-align: center;
    text-shadow: 1px 1px rgba(65, 93, 132, 1);
  }
  .described {
    color: rgba(72, 142, 192, 1);
    font-weight: bold;
    font-size: 1rem;
    text-align: right;
    text-shadow: 1px 1px rgba(65, 93, 132, 1);
  }
}
