/*
 * @description: 审核流，自己只查看，管理员可审核
 * @author: zpl
 * @Date: 2023-11-27 11:25:53
 * @LastEditTime: 2024-03-14 16:59:52
 * @LastEditors: SissleLynn
 */
import { envjudge, getNjName, getQueryObj } from '@/utils'
import { Button, Empty, Image, message, Spin, Tag } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'
import styles from './index.less'
import { useModel, history } from 'umi'
import { ProDescriptions } from '@ant-design/pro-components'
import { homeVisitShow } from '@/services/edu-platform-web/home_visit'
import dayjs from 'dayjs'
import { generatePDF, StatusEnum } from '../../utils'

export const StatusTag = ({ status }: { status: number }) => {
  switch (status) {
    case StatusEnum['草稿']:
      return <Tag>草稿</Tag>
    case StatusEnum['待审批']:
      return <Tag color="#108ee9">待审批</Tag>
    case StatusEnum['已同意']:
      return <Tag color="#87d068">已同意</Tag>
    case StatusEnum['已拒绝']:
      return <Tag color="#f50">已拒绝</Tag>
    case StatusEnum['已结束']:
      return <Tag color="#999999">已结束</Tag>
  }
}

const Detail = () => {
  const { id, audited } = getQueryObj()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo } = initialState || {}
  const env_screen = envjudge()
  const [loading, setLoading] = useState(true)

  const [data, setData] = useState<any>()
  const [visible, setVisible] = useState({
    show: false,
    img: undefined,
  })
  const getData = async () => {
    setLoading(true)
    const result = await homeVisitShow(id!)
    if (result?.errCode) {
      setLoading(false)
      return message.warning('获取家访详情数据失败' + result?.message)
    } else {
      setLoading(false)
      setData(result)
    }
  }

  useEffect(() => {
    if (id) {
      getData()
    }
  }, [id])

  if (!id) {
    return <Empty style={{ marginTop: '35vh' }} description="非法访问" />
  }

  const exportPdf = async () => {
    if (!data) {
      return message.warning('请等待数据加载完成后再导出')
    }
    setLoading(true)
    const { status, data: pdfBytes, msg } = await generatePDF([data])
    if (!status) {
      setLoading(false)
      return message.warning(`导出失败 ${msg}`)
    }
    if (!pdfBytes) {
      return message.warning('导出失败 PDF生成内容为空！')
    }
    const blob = new Blob([pdfBytes], { type: 'application/pdf' })
    const fileName = `${data?.studentName || '家访'}记录.pdf`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    link.click()
    URL.revokeObjectURL(link.href)
    message.success('导出成功')
    setLoading(false)
  }

  return (
    <Spin spinning={loading} tip="加载中，请稍候...">
      <div style={{ height: '100%' }}>
        <div>
          {!env_screen.includes('mobile') && (
            <Button
              type="primary"
              style={{
                marginBlockEnd: 24,
              }}
              onClick={() => {
                if (audited && audited === 'true') {
                  history.push({
                    pathname: '/familyHood/visit/management',
                    query: {
                      audited: 'true',
                    },
                  })
                } else {
                  history.go(-1)
                }
              }}
            >
              <LeftOutlined />
              返回上一页
            </Button>
          )}
          <div className={styles.topCon}>
            <div className={styles.title}>
              <span>
                {data?.teacher1_realName} 和 {data?.teacher2_realName}（{data?.studentName}
                ）家访记录
              </span>
              <Button type="primary" onClick={exportPdf}>
                导出
              </Button>
            </div>
            <div className={styles.desc}>
              <StatusTag status={data?.status} />
            </div>
          </div>

          <div className={styles.middleCon}>
            <div className={styles.desc}>家访记录</div>
            <div className={styles.flowCon}>
              <ProDescriptions column={env_screen.includes('mobile') ? 1 : 2}>
                <ProDescriptions.Item label="教师1" valueType="text">
                  {data?.teacher1_realName}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="教师2" valueType="text">
                  {data?.teacher2_realName}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="学生" valueType="text">
                  {data?.studentName}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="年级班级" valueType="text">
                  {getNjName(data?.gradeName, schoolInfo?.section_code)} {data?.className}
                </ProDescriptions.Item>
                {/* <ProDescriptions.Item label="家长电话" valueType="text">
                  {data?.parentPhone}
                </ProDescriptions.Item> */}
                {/* <ProDescriptions.Item label="家庭住址" valueType="text">
                  {data?.visitAddress}
                </ProDescriptions.Item> */}
                <ProDescriptions.Item label="家访时间" valueType="text">
                  {data?.start_time && data?.end_time
                    ? `${dayjs(data?.start_time).format('YYYY-MM-DD HH:mm')} -
                                  ${dayjs(data?.end_time).format('YYYY-MM-DD HH:mm')}`
                    : '--'}
                </ProDescriptions.Item>
                <ProDescriptions.Item span={2} valueType="text" label="家访记录">
                  {data?.visitRecord}
                </ProDescriptions.Item>
              </ProDescriptions>
            </div>
            <div className={styles.desc}>家访照片</div>
            <div>
              {data?.visitImg && (
                <Image.PreviewGroup>
                  {(data?.visitImg?.split(',') || [])?.map((item: any) => {
                    return (
                      <div
                        key={item}
                        style={{
                          display: 'inline-block',
                          padding: env_screen.includes('mobile') ? '6px 0' : '16px',
                        }}
                      >
                        <Image width={200} src={item} />
                      </div>
                    )
                  })}
                </Image.PreviewGroup>
              )}
            </div>
          </div>
          <Image
            width={200}
            style={{ display: 'none' }}
            src={visible?.img}
            preview={{
              visible: visible.show,
              src: visible.img,
              onVisibleChange: (value) => {
                setVisible({
                  show: value,
                  img: undefined,
                })
              },
            }}
          />
        </div>
        {env_screen.includes('mobile') && (
          <div
            style={{
              position: 'fixed',
              insetInlineEnd: 0,
              insetBlockEnd: 0,
              width: '100vw',
              background: '#fff',
              padding: '8px 24px',
              borderTop: '1px solid #ececec',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Button
              type={'primary'}
              shape="round"
              style={{
                width: '100%',
              }}
              onClick={() => {
                if (audited && audited === 'true') {
                  history.push({
                    pathname: '/familyHood/visit/management',
                    query: {
                      audited: 'true',
                    },
                  })
                } else {
                  history.go(-1)
                }
              }}
            >
              返回上一页
            </Button>
          </div>
        )}
      </div>
    </Spin>
  )
}

export default Detail
