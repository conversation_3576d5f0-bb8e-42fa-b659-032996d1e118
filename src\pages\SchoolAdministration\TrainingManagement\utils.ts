import exifr from 'exifr'
import fontkit from '@pdf-lib/fontkit'
import { degrees, PDFDocument, rgb } from 'pdf-lib'

/**
 * 获取图片的EXIF方向信息
 */
export async function getImageOrientation(arrayBuffer: ArrayBuffer): Promise<number> {
  try {
    const exifData = await exifr.parse(arrayBuffer, ['Orientation'])
    if (!exifData?.Orientation) return 1

    if (typeof exifData.Orientation === 'string') {
      switch (exifData.Orientation.toLowerCase()) {
        case 'rotate 90 cw':
        case '90':
          return 6
        case 'rotate 180':
        case '180':
          return 3
        case 'rotate 270 cw':
        case '270':
          return 8
        default:
          return 1
      }
    }

    return Number(exifData.Orientation) || 1
  } catch (error) {
    console.warn('读取图片EXIF方向信息失败:', error)
    return 1
  }
}
/**
 * 文本自动换行处理器 - 优化中文处理
 */
export function splitTextIntoLines(
  text: string,
  font: any,
  fontSize: number,
  maxWidth: number,
): string[] {
  const lines: string[] = []
  const paragraphs = text.split('\n')

  for (const paragraph of paragraphs) {
    if (!paragraph.trim()) {
      lines.push('') // 保留空行
      continue
    }

    let currentLine = ''
    for (let i = 0; i < paragraph.length; i++) {
      const char = paragraph[i]
      const testLine = currentLine + char
      const lineWidth = font.widthOfTextAtSize(testLine, fontSize)
      if (lineWidth > maxWidth && char.trim()) {
        // 仅在非空字符时换行
        lines.push(currentLine.trim())
        currentLine = char
      } else {
        currentLine = testLine
      }
    }
    if (currentLine.trim()) {
      lines.push(currentLine.trim())
    }
  }

  return lines
}

/**
 * 格式化为短日期格式：MM/DD HH:MM
 */
export function formatShortDate(dateStr: string | null): string {
  if (!dateStr) return '无'
  try {
    const date = new Date(dateStr)
    return `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(
      2,
      '0',
    )} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  } catch (e) {
    return dateStr
  }
}

/**
 * 格式化为完整日期格式：YYYY-MM-DD HH:MM:SS
 */
export function formatFullDate(dateStr: string | null): string {
  if (!dateStr) return '无'
  try {
    const date = new Date(dateStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
      date.getDate(),
    ).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(
      date.getMinutes(),
    ).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  } catch (e) {
    return dateStr
  }
}

/**
 * 将训练数据处理成更易读的格式
 */
export function formatTrainingData(data: any) {
  // 解析图片数据
  let imageUrls = []
  try {
    const trainImgData = JSON.parse(data.train_img || '[]')
    imageUrls = trainImgData.map((item: { url: string; createdAt: string; address: string }) => {
      return {
        url: item.url.startsWith('//') ? `https:${item.url}` : item.url,
        time: item.createdAt,
        location: item.address,
      }
    })
  } catch (e) {
    console.error('解析培训图片失败:', e)
  }
  // 单独提取培训主题和起止时间
  const trainingSubject = `培训主题或内容：${data.train_theme || '无'}`
  const trainingTime = `培训起止时间：${formatShortDate(data.start_time)} 至 ${formatShortDate(
    data.end_time,
  )}`

  // 构建左右两列的基本信息格式
  const leftColumn = [
    `组织单位：${data.organization || '无'}`,
    `学时：${data.class_hour || 0}`,
    `培训方式：${data.train_type || '无'}`,
    `培训地点：${data.address || data.train_content || '无'}`,
  ].join('\n')

  const rightColumn = [
    `培训时长：${data.class_duration || '无'}`,
    `学科：${data.xk || '无'}`,
    `培训级别：${data.train_level || '无'}`,
  ].join('\n')

  // 图片信息格式
  const imageInfos = imageUrls.map((img: { time: string | null; location: any }, index: number) => {
    return {
      title: index === 0 ? '外出照片' : `照片 ${index + 1}`,
      time: `拍照时间：${formatFullDate(img.time)}`,
      location: `地图定位：${img.location || '无'}`,
    }
  })

  return {
    title: `${data.realName || ''}----的培训记录`,
    trainingSubject, // 新增字段
    trainingTime,
    leftColumn,
    rightColumn,
    imageInfos,
    imageUrls: imageUrls.map((img: { url: string }) => img.url),
  }
}

/**
 * 带超时控制的fetch请求
 */
export async function fetchWithTimeout(url: string, { timeout = 5000 } = {}) {
  const controller = new AbortController()
  const id = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, { signal: controller.signal })
    clearTimeout(id)

    if (!response.ok) throw new Error(`HTTP错误: ${response.status}`)
    return await response.arrayBuffer()
  } catch (error: any) {
    clearTimeout(id)
    throw new Error(`资源加载失败: ${url} - ${error?.message}`)
  }
}

/**
 * 改进的图片获取和错误处理函数
 */
export async function fetchWithBetterErrorHandling(url: string, { timeout = 15000 } = {}) {
  const controller = new AbortController()
  const id = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, {
      signal: controller.signal,
      // 添加跨域支持
      mode: 'cors',
    })
    clearTimeout(id)

    if (!response.ok) {
      console.error(`图片HTTP错误: ${response.status} - ${url}`)
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    return arrayBuffer
  } catch (error: any) {
    clearTimeout(id)
    // 尝试不同的URL格式（如果是相对URL）
    if (url.startsWith('//')) {
      return fetchWithBetterErrorHandling(`https:${url}`, { timeout })
    }

    // 如果是HTTP但服务器支持HTTPS，尝试HTTPS
    if (url.startsWith('http://')) {
      return fetchWithBetterErrorHandling(url.replace('http://', 'https://'), {
        timeout,
      })
    }

    throw new Error(`资源加载失败: ${url} - ${error?.message || '未知错误'}`)
  }
}

/**
 * 根据文件扩展名加载对应格式的图片
 */
export async function loadImageByExtension(
  pdfDoc: PDFDocument,
  imageBytes: ArrayBuffer,
  url: string,
) {
  const lowerUrl = url.toLowerCase()
  if (lowerUrl.endsWith('.png')) {
    return await pdfDoc.embedPng(imageBytes)
  }
  if (lowerUrl.endsWith('.jpg') || lowerUrl.endsWith('.jpeg')) {
    return await pdfDoc.embedJpg(imageBytes)
  }
  // 默认尝试作为JPG处理
  return await pdfDoc.embedJpg(imageBytes)
}

/**
 * 创建占位图片
 */
export async function createPlaceholderImage(pdfDoc: PDFDocument, width: number, height: number) {
  // 创建一个小的canvas作为占位图
  const canvas = document.createElement('canvas')
  canvas.width = width || 300
  canvas.height = height || 200
  const ctx = canvas.getContext('2d')

  if (ctx) {
    // 填充浅灰色背景
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 添加错误文本
    ctx.fillStyle = '#888'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('图片加载失败', canvas.width / 2, canvas.height / 2 - 10)
    ctx.font = '12px Arial'
    ctx.fillText('请检查网络连接或图片URL', canvas.width / 2, canvas.height / 2 + 15)

    // 转为PNG并嵌入
    const blob = await new Promise<Blob | null>((resolve) => {
      canvas.toBlob((imageBlob) => resolve(imageBlob), 'image/png')
    })
    if (blob) {
      const arrayBuffer = await blob.arrayBuffer()
      return await pdfDoc.embedPng(arrayBuffer)
    }
  }

  // 如果上面失败，返回一个1x1的透明PNG
  const transparentPixel = new Uint8Array([
    137, 80, 78, 71, 13, 10, 26, 10, 0, 0, 0, 13, 73, 72, 68, 82, 0, 0, 0, 1, 0, 0, 0, 1, 8, 6, 0,
    0, 0, 31, 21, 196, 137, 0, 0, 0, 13, 73, 68, 65, 84, 120, 1, 99, 97, 0, 0, 0, 5, 0, 1, 226, 52,
    148, 182, 0, 0, 0, 0, 73, 69, 78, 68, 174, 66, 96, 130,
  ])
  return await pdfDoc.embedPng(transparentPixel)
}

/**
 * 对图片进行EXIF方向处理
 */
export async function drawImageWithOrientation(
  page: any,
  image: any,
  orientation: number,
  x: number,
  y: number,
  width: number,
  height: number,
  pageWidth: number,
) {
  let drawY = y - height
  let rotate = 0
  const newWidth = width
  const newHeight = height

  switch (orientation) {
    case 1: // 正常方向
      break

    case 3: // 180度
      rotate = 180
      drawY = y
      break

    case 6: // 90度顺时针
      rotate = -90
      drawY = y - height / 2
      break

    case 8: // 270度顺时针
      rotate = 270
      drawY = y - height / 2
      break

    default:
      console.warn('未知的EXIF方向:', orientation)
      break
  }

  const centerX = pageWidth / 2
  const imageCenterOffsetX = newWidth / 2
  const imageCenterOffsetY = newHeight / 2

  // 居中计算（以图像中心为锚点）
  const centeredX = centerX - imageCenterOffsetX
  const centeredY = drawY + imageCenterOffsetY // 保持 Y 位置不变，仅横向居中

  // 实际绘制图片
  page.drawImage(image, {
    x: centeredX,
    y: centeredY,
    width: newWidth,
    height: newHeight,
    rotate: degrees(rotate),
  })

  return { width: newWidth, height: newHeight }
}

/**
 * 计算图片缩放尺寸（保持宽高比）
 */
export function calculateImageSize(
  image: any,
  maxWidth: number,
  maxHeight: number = Infinity,
  orientation: number = 1,
) {
  let width = image.width
  let height = image.height

  if (orientation === 6 || orientation === 8) {
    ;[width, height] = [height, width]
  }

  const scaleWidth = maxWidth / width
  const scaleHeight = maxHeight / height
  const scale = Math.min(scaleWidth, scaleHeight)

  return {
    scaledWidth: width * scale,
    scaledHeight: height * scale,
  }
}
/**
 * PDF生成服务
 */
export async function generatePDF(trainingData: any[]) {
  try {
    const pdfDoc = await PDFDocument.create()
    pdfDoc.registerFontkit(fontkit)

    // 加载中文字体
    const fontUrl =
      'https://ysp-uploader-1301720845.cos.ap-nanjing.myqcloud.com/ziti/NotoSansSC-Regular.otf'
    const fontBytes = await fetchWithTimeout(fontUrl, { timeout: 10000 })

    const font = await pdfDoc.embedFont(fontBytes)

    // 页面配置常量
    const PAGE_WIDTH = 600
    const PAGE_HEIGHT = 800
    const MARGIN = 50
    const CONTENT_WIDTH = PAGE_WIDTH - MARGIN * 2
    const IMAGE_MAX_WIDTH = CONTENT_WIDTH
    const TEXT_LINE_HEIGHT = 25 // 适当的行高
    const TITLE_FONT_SIZE = 18
    const BODY_FONT_SIZE = 12
    const MAX_HEIGHT = 200

    for (const item of trainingData) {
      // 格式化数据，使其更易读
      const formattedData = formatTrainingData(item)

      const page = pdfDoc.addPage([PAGE_WIDTH, PAGE_HEIGHT])

      // 当前绘制Y坐标位置
      let yPosition = PAGE_HEIGHT - MARGIN

      // 添加标题
      page.drawText(formattedData.title, {
        x: MARGIN,
        y: yPosition,
        size: TITLE_FONT_SIZE,
        font,
        color: rgb(0.1, 0.1, 0.1),
      })
      yPosition -= 40

      // 绘制培训主题
      page.drawText(formattedData.trainingSubject, {
        x: MARGIN,
        y: yPosition,
        size: BODY_FONT_SIZE,
        font,
        color: rgb(0.3, 0.3, 0.3),
      })
      yPosition -= TEXT_LINE_HEIGHT

      // 绘制培训起止时间
      page.drawText(formattedData.trainingTime, {
        x: MARGIN,
        y: yPosition,
        size: BODY_FONT_SIZE,
        font,
        color: rgb(0.3, 0.3, 0.3),
      })
      yPosition -= TEXT_LINE_HEIGHT

      // 计算列宽
      const columnWidth = (CONTENT_WIDTH - 20) / 2 // 20是列间距

      // 绘制左列
      const leftColumnLines = splitTextIntoLines(
        formattedData.leftColumn,
        font,
        BODY_FONT_SIZE,
        columnWidth,
      )

      // 绘制右列
      const rightColumnLines = splitTextIntoLines(
        formattedData.rightColumn,
        font,
        BODY_FONT_SIZE,
        columnWidth,
      )

      // 确定两列的最大行数
      const maxLines = Math.max(leftColumnLines.length, rightColumnLines.length)

      // 绘制两列内容
      for (let i = 0; i < maxLines; i++) {
        // 绘制左列
        if (i < leftColumnLines.length) {
          page.drawText(leftColumnLines[i], {
            x: MARGIN,
            y: yPosition - i * TEXT_LINE_HEIGHT,
            size: BODY_FONT_SIZE,
            font,
            color: rgb(0.3, 0.3, 0.3),
          })
        }

        // 绘制右列
        if (i < rightColumnLines.length) {
          page.drawText(rightColumnLines[i], {
            x: MARGIN + columnWidth + 20, // 右列起始位置
            y: yPosition - i * TEXT_LINE_HEIGHT,
            size: BODY_FONT_SIZE,
            font,
            color: rgb(0.3, 0.3, 0.3),
          })
        }
      }

      // 更新Y位置到两列内容之后
      yPosition -= maxLines * TEXT_LINE_HEIGHT + 20

      // 修改图片处理部分，只处理第一张图片
      if (formattedData.imageUrls && formattedData.imageUrls.length > 0) {
        const imageUrl = formattedData.imageUrls[0]
        const imageInfo = formattedData.imageInfos[0]

        try {
          // 加载图片资源
          const imageBytes = await fetchWithBetterErrorHandling(imageUrl)

          // 获取图片EXIF方向信息
          const orientation = await getImageOrientation(imageBytes)

          const image = await loadImageByExtension(pdfDoc, imageBytes, imageUrl)

          // 计算图片缩放尺寸
          const { scaledWidth, scaledHeight } = calculateImageSize(
            image,
            IMAGE_MAX_WIDTH,
            MAX_HEIGHT,
            orientation,
          )
          // 添加图片标题
          page.drawText(imageInfo.title, {
            x: MARGIN,
            y: yPosition,
            size: 14,
            font,
            color: rgb(0.1, 0.1, 0.1),
          })

          yPosition -= 30
          // 绘制图片信息（拍照时间和地图定位）
          page.drawText(imageInfo.time, {
            x: MARGIN,
            y: yPosition,
            size: BODY_FONT_SIZE,
            font,
            color: rgb(0.3, 0.3, 0.3),
          })

          page.drawText(imageInfo.location, {
            x: MARGIN + columnWidth + 20,
            y: yPosition,
            size: BODY_FONT_SIZE,
            font,
            color: rgb(0.3, 0.3, 0.3),
          })
          yPosition -= 30
          const fixedYPosition = PAGE_HEIGHT - MARGIN - 450
          // 绘制图片
          await drawImageWithOrientation(
            page,
            image,
            orientation,
            MARGIN, // 这个参数现在在函数内部会被覆盖
            fixedYPosition,
            scaledWidth,
            scaledHeight,
            PAGE_WIDTH, // 传入页面宽度用于居中计算
          )
        } catch (error: any) {
          try {
            // 创建占位图并显示
            const placeholderImage = await createPlaceholderImage(pdfDoc, 300, 200)

            // 绘制占位图
            page.drawImage(placeholderImage, {
              x: MARGIN,
              y: yPosition - 200,
              width: 300,
              height: 200,
            })

            // 绘制错误提示
            page.drawText(`图片加载失败: ${error.message || '未知错误'}`, {
              x: MARGIN,
              y: yPosition - 220,
              size: 12,
              font,
              color: rgb(0.8, 0.2, 0.2),
            })

            yPosition -= 240
          } catch (placeholderError) {
            // 如果占位图也失败，只显示文本错误
            page.drawText(`图片加载失败: ${error.message || '未知错误'}`, {
              x: MARGIN,
              y: yPosition - 20,
              size: 12,
              font,
              color: rgb(0.8, 0.2, 0.2),
            })
            yPosition -= 40 // 为错误信息留出空间
          }
        }
      }
    }

    // 生成并下载PDF文件
    const pdfBytes = await pdfDoc.save()
    return {
      status: true,
      data: pdfBytes,
      msg: 'PDF生成成功',
    }
  } catch (error) {
    return {
      status: false,
      msg: error,
    }
  }
}
