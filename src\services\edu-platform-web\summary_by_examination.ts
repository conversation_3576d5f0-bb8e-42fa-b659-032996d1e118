import { request } from 'umi'

/** 查询列表  POST /summary_by_examination */
export async function index(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/summary_by_examination', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    prefix: '/api_score',
    data: body,
  })
}

/** 获取年级成绩汇总 POST /summary_by_grade */
export async function getGradeCollect(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/summary_by_grade', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    prefix: '/api_score',
    data: body,
  })
}

/** 获取班级成绩汇总 POST /summary_by_class */
export async function getClassCollect(body: Omit<any, 'id'>) {
  return request<API.ResType<any>>('/summary_by_class', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    prefix: '/api_score',
    data: body,
  })
}
