import { getFileById } from '@/services/edu-platform-web/upload'
import type { ProColumns } from '@ant-design/pro-components'
import { Button, Space, Tag, message } from 'antd'
import moment from 'moment'
import { history } from 'umi'
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const getOutColums = ({
  isAdmin,
  setCurrent,
  setPreviewImage,
}: {
  isAdmin: boolean
  setPreviewImage: React.Dispatch<any>
  setCurrent: React.Dispatch<any>
  refreshTable: () => void
  currentUser?: UserInfo | null
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
  },
  {
    title: '学生姓名',
    dataIndex: 'userName',
    key: 'userName',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '年级班级',
    dataIndex: 'gradeName',
    key: 'gradeName',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (_, record: any) => {
      return record?.gradeName + record?.className
    },
  },
  {
    title: '传染病类型',
    dataIndex: 'infection_type',
    key: 'infection_type',
    align: 'center',
    ellipsis: true,
    width: 100,
    render: (_, record: any) => {
      return record?.infection_type?.jb
    },
  },
  {
    title: '请假时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    align: 'center',
    valueType: 'dateTime',
    width: 150,
    render: (_, record) => {
      return moment(record?.createdAt).format('YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '确诊日期',
    dataIndex: 'startTime',
    key: 'startTime',
    align: 'center',
    valueType: 'dateTime',
    width: 150,
    render: (_, record) => {
      return moment(record?.startTime).format('YYYY-MM-DD')
    },
  },
  {
    title: '请假说明',
    dataIndex: 'reason',
    key: 'reason',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '诊断证明及病历',
    dataIndex: 'annex',
    key: 'annex',
    align: 'center',
    width: 200,
    render: (_, record: any) => {
      return (
        <Space>
          {record?.annex?.split(',').map((id: string, index: number) => (
            <a
              key={id}
              href="#"
              onClick={async () => {
                const result = await getFileById(id)
                if (!result?.errCode) {
                  setPreviewImage({
                    imgUrl: result.url,
                    show: true,
                    title: '图片预览',
                  })
                } else {
                  message.warning('文件已过期或已损坏')
                }
              }}
            >
              附件{index + 1}
            </a>
          ))}
        </Space>
      )
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 100,
    render: (_, record: any) => (
      <>
        <Tag
          color={
            record.status === '已同意' ? '#87d068' : record.status === '已拒绝' ? '#f50' : '#108ee9'
          }
        >
          {record.status}
        </Tag>
        {record.status === '已同意' && (
          <Tag
            color={
              record.finshedStatus === '2'
                ? 'success'
                : record.finshedStatus === '1'
                ? 'processing'
                : 'error'
            }
            style={{
              marginTop: '10px',
            }}
          >
            {record.finshedStatus === '0'
              ? '待销假'
              : record.finshedStatus === '1'
              ? '销假待审'
              : record.finshedStatus === '2'
              ? '已销假'
              : record.finshedStatus === '3'
              ? '销假被拒绝'
              : ''}
          </Tag>
        )}
      </>
    ),
  },
  {
    title: '操作',
    key: 'option',
    width: 120,
    hideInTable: !isAdmin,
    render: (_: any, record: any) => (
      <>
        {record.status === '待审批' && (
          <Button
            type="link"
            style={{
              cursor: 'pointer',
            }}
            onClick={async () => {
              setCurrent(record)
            }}
          >
            审核
          </Button>
        )}
        {record.status === '已同意' && record.finshedStatus === '1' && (
          <Button
            type="link"
            style={{
              cursor: 'pointer',
            }}
            onClick={async () => {
              setCurrent(record)
            }}
          >
            审核
          </Button>
        )}
        <Button
          type="text"
          onClick={() => {
            history.push(`/educationaManagement/crbgl/auditList/${record.id}`)
          }}
        >
          查看
        </Button>
      </>
    ),
    align: 'center',
  },
]

const getOutMetas = ({
  isAdmin,
  setCurrent,
}: {
  isAdmin: boolean
  setPreviewImage: React.Dispatch<any>
  setCurrent: React.Dispatch<any>
  refreshTable: () => void
  currentUser?: UserInfo | null
}): any => {
  return {
    title: {
      dataIndex: 'teacherName',
      render: (dom: React.ReactNode, entity: any) => {
        return `${entity?.gradeName + entity?.className + entity?.userName}提交的请假申请`
      },
    },
    subTitle: {
      render: (_dom: React.ReactNode, entity: any) => {
        return (
          <Space>
            <Tag
              color={
                entity.status === '已同意'
                  ? '#87d068'
                  : entity.status === '已拒绝'
                  ? '#f50'
                  : '#108ee9'
              }
            >
              {entity.status}
            </Tag>
            {entity.status === '已同意' && (
              <Tag
                color={
                  entity.finshedStatus === '2'
                    ? 'success'
                    : entity.finshedStatus === '1'
                    ? 'processing'
                    : 'error'
                }
              >
                {entity.finshedStatus === '0'
                  ? '待销假'
                  : entity.finshedStatus === '1'
                  ? '销假待审'
                  : entity.finshedStatus === '2'
                  ? '已销假'
                  : entity.finshedStatus === '3'
                  ? '销假被拒绝'
                  : ''}
              </Tag>
            )}
          </Space>
        )
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>疾病: {entity?.infection_type?.jb}</p>
            <p style={styleP}>请假时间: {moment(entity?.createdAt).format('YYYY.MM.DD HH:mm')}</p>
            <p style={styleP}>发病日期: {moment(entity?.disease_date).format('YYYY.MM.DD')}</p>
            <p style={styleP}>确诊日期: {moment(entity?.startTime).format('YYYY.MM.DD')}</p>
            <p style={styleP}>诊断单位 : {entity?.diagnose_name}</p>
            <p style={styleP}>处理措施: {entity?.dispose}</p>
          </div>
        )
      },
    },
    actions: isAdmin && {
      render: (
        _: any,
        row: {
          status: string
          realName: string
          finshedStatus: string
          id: any
        },
      ) => {
        return (
          <Space>
            {row.status === '待审批' && (
              <Button
                type="link"
                style={{
                  cursor: 'pointer',
                }}
                onClick={async () => {
                  setCurrent(row)
                }}
              >
                审核
              </Button>
            )}

            {row.status === '已同意' && row.finshedStatus === '1' && (
              <Button
                type="link"
                style={{
                  cursor: 'pointer',
                }}
                onClick={async () => {
                  setCurrent(row)
                }}
              >
                审核
              </Button>
            )}
            <Button
              type="text"
              onClick={() => {
                history.push(`/educationaManagement/crbgl/auditList/${row.id}`)
              }}
            >
              查看
            </Button>
          </Space>
        )
      },
    },
  }
}
export { getOutColums, getOutMetas }
