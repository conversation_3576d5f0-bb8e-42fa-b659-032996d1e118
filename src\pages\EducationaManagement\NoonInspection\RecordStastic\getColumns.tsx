import type { ProColumns } from '@ant-design/pro-components'
import { Button, Popconfirm, Space, Tag, Tooltip, Typography, message } from 'antd'
import moment from 'moment'
import { history } from 'umi'

const { Paragraph } = Typography
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const getRecordColums = ({
  queryDetail,
}: {
  refreshTable: () => void
  queryDetail: (data: any) => Promise<void>
  currentUser?: UserInfo | null
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '日期',
    dataIndex: 'check_time',
    key: 'check_time',
    align: 'center',
    width: 80,
    fixed: 'left',
  },
  {
    title: '时段',
    dataIndex: 'period',
    key: 'period',
    align: 'center',
    width: 80,
    fixed: 'left',
  },
  {
    title: '年级班级',
    dataIndex: 'gradeName',
    key: 'gradeName',
    align: 'center',
    width: 80,
    render: (_, record) => {
      return (
        <Tag color="blue">
          {record.gradeName}
          {record.className}
        </Tag>
      )
    },
  },
  {
    title: '疾病症状发生人数',
    children: [
      {
        title: '发热',
        dataIndex: 'fever_count',
        key: 'fever_count',
        width: 60,
        align: 'center',
      },
      {
        title: '咳嗽',
        dataIndex: 'cough_count',
        key: 'cough_count',
        width: 60,
        align: 'center',
      },
      {
        title: '头痛',
        dataIndex: 'headache_count',
        key: 'headache_count',
        width: 60,
        align: 'center',
      },
      {
        title: '腹痛',
        dataIndex: 'abdominal_pain_count',
        key: 'abdominal_pain_count',
        width: 60,
        align: 'center',
      },
      {
        title: '腹泻',
        dataIndex: 'diarrhea_count',
        key: 'diarrhea_count',
        width: 60,
        align: 'center',
      },
      {
        title: '呕吐',
        dataIndex: 'vomiting_count',
        key: 'vomiting_count',
        width: 60,
        align: 'center',
      },
      {
        title: '出疹',
        dataIndex: 'rash_count',
        key: 'rash_count',
        width: 60,
        align: 'center',
      },
      {
        title: '黄疸',
        dataIndex: 'jaundice_count',
        key: 'jaundice_count',
        width: 60,
        align: 'center',
      },
      {
        title: '红眼',
        dataIndex: 'red_eye_count',
        key: 'red_eye_count',
        width: 60,
        align: 'center',
      },
      {
        title: '其他',
        dataIndex: 'other_symptom_count',
        key: 'other_symptom_count',
        width: 60,
        align: 'center',
      },
    ],
  },
  {
    title: '缺课人数',
    children: [
      {
        title: '迟到',
        dataIndex: 'late_count',
        key: 'late_count',
        width: 60,
        align: 'center',
      },
      {
        title: '因病',
        dataIndex: 'ill_count',
        key: 'ill_count',
        width: 60,
        align: 'center',
        render: (_, record) => {
          return (
            <Button type="text" danger={record?.has_contagion}>
              {record.ill_count}
            </Button>
          )
        },
      },
      {
        title: '因事',
        dataIndex: 'other_count',
        key: 'other_count',
        width: 60,
        align: 'center',
      },
      {
        title: '其他',
        dataIndex: 'other_reason_count',
        key: 'other_reason_count',
        width: 60,
        align: 'center',
      },
    ],
  },
  {
    title: '缺课总人数',
    dataIndex: 'total_absence_count',
    key: 'total_absence_count',
    width: 70,
    align: 'center',
    render: (_, record: any) => {
      return (
        <Button
          type="link"
          onClick={() => {
            queryDetail(record)
          }}
        >
          {record.total_absence_count}
        </Button>
      )
    },
  },
]

const getRecordMetas = ({
  queryDetail,
}: {
  refreshTable: () => void
  queryDetail: (data: any) => Promise<void>
  currentUser?: UserInfo | null
}): any => {
  return {
    title: {
      dataIndex: 'check_time',
      render: (dom: React.ReactNode, entity: any) => {
        return `${entity?.check_time}晨午检情况统计`
      },
    },
    subTitle: {
      render: (_dom: React.ReactNode, entity: any) => {
        return (
          <Space
            style={{
              marginTop: '5px',
            }}
          >
            <Tag>
              {entity.gradeName}
              {entity.className}
            </Tag>
            {entity.period && (
              <Tag color={entity.period === '下午' ? 'gold' : 'blue'}>{entity.period}</Tag>
            )}
          </Space>
        )
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>
              因病：{entity?.ill_count} 因事：{entity?.other_count} 其他：
              {entity?.other_reason_count}
            </p>
            <p style={styleP}>
              缺课总人数：
              <Button
                type="link"
                onClick={() => {
                  queryDetail(entity)
                }}
              >
                {entity.total_absence_count}
              </Button>
            </p>
          </div>
        )
      },
    },
  }
}

export { getRecordColums, getRecordMetas }
