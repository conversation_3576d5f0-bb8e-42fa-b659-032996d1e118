@import '~antd/es/style/themes/default.less';

.studentsCheckon {
  height: calc(100vh - 6rem);
  padding: 12px 24px 24px 12px;
  overflow-y: auto;
  background: var(--card-bg);

  .tabPane {
    height: calc(100vh - 12rem);
    overflow-y: auto;
  }

  div[class*='@{ant-prefix}-spin-nested-loading'],
  div[class*='@{ant-prefix}-spin-container'] {
    height: 100%;
  }
  div[class*='searchLayout'] {
    > div[class*='@{ant-prefix}-space-item'] {
      &:nth-child(1) {
        display: none;
      }
    }
  }

  .checkWorkAttendance {
    display: flex;
    justify-content: space-around;
    max-width: 900px;
    margin: 20px 10px 10px 10px;
    margin: 0 auto;
    padding: 10px 0;
    background: #fff;
    border-radius: 8px;

    .checkWorkInfo {
      text-align: center;

      .number {
        color: #333;
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
      }

      .word {
        color: #666;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }

  .studentList {
    max-width: 900px;
    margin: 0 auto;
    :global {
      .@{ant-prefix}-table-wrapper {
        .@{ant-prefix}-table {
          background: #f5f5f5;
          .@{ant-prefix}-table-thead > tr {
            > th {
              height: 28px;
              padding: 0;
              color: #666;
              font-size: 12px;
              line-height: 28px;
              background: #eee;
              &:first-child {
                border-top-left-radius: 8px;
              }
              &:last-child {
                border-top-right-radius: 8px;
              }
            }
          }

          .@{ant-prefix}-table-body {
            padding-bottom: 16px;
            tr {
              height: 40px;

              td {
                padding: 0;
                white-space: pre-wrap;
              }
            }

            tr:nth-child(even) {
              background: #fff;
            }
          }
        }
      }
    }
    .info {
      width: 80%;
      margin: 30px auto 10px auto;
      .submit {
        justify-content: flex-end;
        width: 100%;
        margin-bottom: 10px;
      }
    }
  }
  .footerButton {
    position: fixed;
    right: 50%;
    bottom: 0;
    left: calc(50% + 108px);
    z-index: 99;
    width: 100%;
    max-width: 180px;
    transform: translate(-50%, -50%);
    :global {
      .@{ant-prefix}-btn-round {
        width: 100%;
        height: 38px;
        font-size: 16px;
        border-radius: 38px;
      }
    }
  }
}
div[class*='moblieWrap'] {
  div[class*='studentsCheckon'] {
    height: calc(100vh - 4rem);
    padding-top: 0px !important;
    div[class*='footerButton'] {
      left: 0;
      max-width: 480px;
      height: 54px;
      padding: 0 14px;
      line-height: 54px;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      transform: none;
    }
  }
}

.SignIn {
  top: calc(~'50vh - 120px');
  width: 260px !important;
  height: 250px;
  text-align: center;
  :global {
    .@{ant-prefix}-modal-content {
      min-width: 260px !important;
      border-radius: 16px;
    }
  }
  img {
    width: 84px;
    height: 84px;
  }
  h3 {
    font-weight: bold;
    line-height: 50px;
  }
  button {
    width: 144px;
    height: 38px;
    border-radius: 22px;
  }
}
.segmentedControl {
  label[class*='@{ant-prefix}-segmented-item-selected'] {
    color: #fff;
    background: var(--primary-color);
  }
}
