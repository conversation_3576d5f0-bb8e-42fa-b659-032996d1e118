@import '~antd/es/style/themes/default.less';

.stasticCheckon{
  height: calc(100vh - 6rem);
  padding: 12px 24px 24px 12px;
  overflow-y: auto;
  background: var(--card-bg);
  :global{
    .@{ant-prefix}-tag-has-color{
      color: #fff;
    }
  }
}
.header{
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 24px;
  :global{
    .@{ant-prefix}-form-item{
      margin-bottom: 0 !important;
    }
  }
}
.col{
  text-align: center;
}
