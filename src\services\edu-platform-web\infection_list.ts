import { request } from 'umi'

/** 获取需要审核的学生传染病请假列表  GET /ask_for_leave_student/infection_audit_list */
export async function getAuditList(params: Record<string, any>) {
  return request<API.ResType<{ count?: number; list?: API.InfectionType[] }>>(
    '/ask_for_leave_student/infection_audit_list',
    {
      method: 'GET',
      params,
    },
  )
}

/** 获取已审核的学生传染病请假列表  GET /ask_for_leave_student/infaction_approved_list */
export async function getApprovedList(params: Record<string, any>) {
  return request<API.ResType<{ count?: number; list?: API.InfectionType[] }>>(
    '/ask_for_leave_student/infaction_approved_list',
    {
      method: 'GET',
      params,
    },
  )
}
/** 获取学生传染病请假列表  GET /ask_for_leave_student */
export async function getAllList(params: Record<string, any>) {
  return request<API.ResType<{ count?: number; list?: API.InfectionType[] }>>(
    '/ask_for_leave_student',
    {
      method: 'GET',
      params,
    },
  )
}
/** 查询单位学生请假记录详情 */
export async function getAskForLeave(id: string) {
  return request<API.ResType<API.AskForLeaveStudent>>(`/ask_for_leave_student/${id}`, {
    method: 'GET',
  })
}

/** 请假传染病审批  POST /ask_for_leave_student/infaction_qj_audit/{id} */
export async function auditLeave(
  query: { id: string },
  body: {
    isolationDays: number
    status: string
    approvalOpinion?: string
    approvalUser?: string
    approvalUserCode?: string
    approvalTime?: string
  },
  options?: { [key: string]: any },
) {
  const { id } = query
  return request<API.sectionsResponse>(`/ask_for_leave_student/infaction_qj_audit/${id}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 请假传染病审批  POST /ask_for_leave_student/infaction_xj_audit/{id} */
export async function auditFinish(
  query: { id: string },
  body: {
    finshedStatus?: string
    finshedOpinion?: string
    approvalOpinion?: string
    approvalUser?: string
    approvalUserCode?: string
    approvalTime?: string
  },
  options?: { [key: string]: any },
) {
  const { id } = query
  return request<API.sectionsResponse>(`/ask_for_leave_student/infaction_xj_audit/${id}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 批量审核传染病审核  POST /ask_for_leave_student/bulk_audit */
export async function bulkAudit(
  body: [
    {
      id: string
      /** 状态 */
      status: '已同意' | '已拒绝' | '已取消'
      /** 隔离天数 */
      isolationDays: number
      /** 审批意见 */
      approvalOpinion?: string
      /** 结束时间 */
      endTime: Date
      /** 审批人 */
      approvalUser: string
      /** 审批人code */
      approvalUserCode: string
      /** 审批时间 */
      approvalTime: Date
      /** 学校名称 */
      enterpriseName: string
    },
  ],
) {
  return request<API.sectionsResponse>(`/ask_for_leave_student/bulk_audit`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  })
}
