import React from 'react'
import { Card, Col, List, Row, Select, Spin, Switch, Table, Tag } from 'antd'
import styles from './index.less'
import { getTableColums } from './getColumns'
import { envjudge } from '@/utils'
import SwitchItem from './SwitchItem'

const MakeOut: React.FC<{
  dataSources: any
  onSwitchItemHandler: (value: any, checked: boolean) => void
  gradeValue: string | undefined
  loadings?: boolean
  onChangeDates: (value: any, data: any, fields: string) => void
  type: 'look' | 'edit' | 'add' | undefined
}> = ({ dataSources, onSwitchItemHandler, onChangeDates, gradeValue, loadings, type }) => {
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')

  return (
    <div className={styles.studentList}>
      {isMobile ? (
        <List
          loading={loadings}
          grid={{
            gutter: 8,
            xs: 1,
          }}
          dataSource={dataSources}
          renderItem={(item: {
            id: string
            studentName: string
            physical_condition: string
            isRealTo: '出勤' | '缺席' | '请假'
            handle_result: string
            absence_reason: string
          }) => {
            return (
              <List.Item className={styles.studentListItem}>
                <Card
                  title={
                    <div
                      style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      {item.studentName}

                      {item?.isRealTo === '出勤' && type !== 'look' && (
                        <Switch
                          checked={item?.physical_condition === '良好' ? true : false}
                          checkedChildren="良好"
                          unCheckedChildren="生病"
                          onChange={(v) => {
                            onChangeDates?.(v ? '良好' : '生病', item, 'physical_condition')
                          }}
                        />
                      )}

                      {type !== 'look' ? (
                        <SwitchItem
                          realTo={item.isRealTo}
                          record={item}
                          onSwitchItem={onSwitchItemHandler}
                        />
                      ) : item?.isRealTo === '出勤' ? (
                        <Tag color="#108ee9">已到校</Tag>
                      ) : (
                        <Tag color="#f50">未到校</Tag>
                      )}
                    </div>
                  }
                >
                  {item?.physical_condition !== '良好' ? (
                    <Row className={styles.studentListItemBody}>
                      {item?.isRealTo === '出勤' && type === 'look' && (
                        <Col span={type !== 'look' ? 24 : 12}>
                          身体状况： {item?.physical_condition}
                        </Col>
                      )}
                      {item?.isRealTo !== '出勤' && (
                        <Col
                          span={type !== 'look' ? 24 : 12}
                          style={{
                            marginBottom: '10px',
                          }}
                        >
                          缺勤原因：
                          {type !== 'look' ? (
                            <Select
                              getPopupContainer={(triggerNode) => triggerNode.parentElement}
                              style={{ width: 120 }}
                              value={item.absence_reason}
                              options={[
                                {
                                  value: '迟到',
                                  label: '迟到',
                                },
                                {
                                  value: '因事',
                                  label: '因事',
                                },
                                {
                                  value: '因病',
                                  label: '因病',
                                },
                                {
                                  value: '其他',
                                  label: '其他',
                                },
                              ]}
                              onSelect={(v) => {
                                onChangeDates?.(v, item, 'absence_reason')
                              }}
                            />
                          ) : (
                            item?.absence_reason
                          )}
                        </Col>
                      )}
                      {item?.handle_result && (
                        <Col span={type !== 'look' ? 24 : 12}>
                          处理结果：
                          {((item?.isRealTo !== '出勤' && item?.absence_reason === '因病') ||
                            item?.physical_condition === '生病') &&
                          type !== 'look' ? (
                            <Select
                              getPopupContainer={(triggerNode) => triggerNode.parentElement}
                              style={{ width: 120 }}
                              value={item.handle_result}
                              options={[
                                {
                                  value: '回家治疗',
                                  label: '回家治疗',
                                },
                                {
                                  value: '留校观察',
                                  label: '留校观察',
                                },
                              ]}
                              onSelect={(v) => {
                                onChangeDates?.(v, item, 'handle_result')
                              }}
                            />
                          ) : (
                            item?.handle_result
                          )}
                        </Col>
                      )}
                    </Row>
                  ) : null}
                </Card>
              </List.Item>
            )
          }}
        />
      ) : (
        <Table<any>
          dataSource={dataSources}
          columns={getTableColums({ onSwitchItemHandler, onChangeDates, type, isMobile })}
          rowKey={(record) => (gradeValue ? record.id + gradeValue : record.id)}
          loading={loadings}
          pagination={false}
          scroll={{ y: 'calc(100vh - 300px)', x: isMobile ? '200vw' : undefined }}
        />
      )}
    </div>
  )
}
export default MakeOut
