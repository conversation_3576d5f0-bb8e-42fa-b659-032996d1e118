/*
 * @Author: w<PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-17 08:48:02
 * @LastEditors: zpl
 * @LastEditTime: 2024-05-27 16:57:23
 * @FilePath: \edu_platform_web\.umirc.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import CompressionWebpackPlugin from 'compression-webpack-plugin'
import { defineConfig } from 'umi'
import routes from './.routes'
import { prefix } from './src/constant'

const prodGzipList = ['js', 'css']

export default defineConfig({
  nodeModulesTransform: {
    type: 'none',
  },
  fastRefresh: {},
  theme: {
    'ant-prefix': prefix,
  },
  define: {
    ENV_title: '智慧校园平台',
    ENV_subTitle: '智慧校园平台',
    ENV_copyRight: '2022 版权所有：智慧校园平台',
    ENV_debug: false,
    ENV_debug_env: 'local',
    ENV_JCXXGL: '11000000',
    ENV_XXJB: '11010000',
    ENV_ZZJG: '11020000',
    ENV_ZWGL: '11030000',
    ENV_XNXQ: '11050000',
    ENV_ZXFA: '11060000',
    ENV_XLGL: '11070000',
    ENV_JJR: '11080000',
    ENV_JWGL: '12000000',
    ENV_CSGL: '12010000',
    ENV_NJBJGL: '12020000',
    ENV_XKGL: '12030000',
    ENV_KCGL: '12040000',
    ENV_RKGL: '12050000',
    ENV_KBGL: '12060000',
    /** 课时管理 */
    ENV_KSGL: '12070000',
    /**智慧排课 */
    ENV_ZHPK: '12080000',
    /**课时统计 */
    ENV_KSTJ: '12090000',
    /**调代课管理 */
    ENV_TDKGL: '12100000',
    /**请假管理 */
    ENV_QJGL: '12130000',
    /**教师请销假 */
    ENV_JSQXJ: '12130100',
    /**学生请假审批 */
    ENV_XSQJSP: '12130200',
    /**教师请假审批 */
    ENV_JSQJSP: '12130300',
    /**教师销假审批 */
    ENV_JSXJSP: '12130400',
    /**学生请假查询 */
    ENV_XSQJCX: '12130500',
    /**教师请假查询 */
    ENV_JSQJCX: '12130600',
    /**请假类别设置 */
    ENV_QJLBSZ: '12130700',
    /**晨午检管理 */
    ENV_CWJGL: '12200000',
    /**晨午检填报 */
    ENV_CWJTB: '12200100',
    /**因病追踪登记 */
    ENV_XSJKDJ: '12200200',
    /**晨午检记录汇总 */
    ENV_CWJJLHZ: '12200300',
    /**传染病审核 */
    ENV_CRBSH: '12170100',
    /**传染病类别设置 */
    ENV_CRBLBSZ: '12170200',
    /**传染病登记 */
    ENV_CRBDJ: '12170300',
    /**门禁管理 */
    ENV_MJGL: '12160000',
    /**门禁学生入校管理 */
    ENV_MJXSRXGL: '12160100',
    /**门禁学生离校管理 */
    ENV_MJXSLXGL: '12160200',
    /**门禁教师离校管理 */
    ENV_MJJSLXGL: '12160300',
    /**外出管理 */
    ENV_WCGL: '12150000',
    /**考勤打卡 */
    ENV_KQDK: '12120400',
    ENV_KQTJ: '12120300',
    ENV_KQBC: '12120100',
    ENV_KQGZSZ: '12120200',
    /** 课后服务统计 */
    ENV_KHFWTJ: '12140000',
    /**第三方应用 */
    ENV_DSF: '96000000',

    ENV_JSDA: '13010000',
    ENV_XSDA: '13020000',
    /** 异动管理 */
    ENV_EDGL: '13060000',
    /** 转入管理 */
    ENV_ZRGL: '13060100',
    /** 转出管理 */
    ENV_ZCGL: '13060200',

    /** 智慧教学code */
    ENV_ZHJX: '15000000',
    ENV_XBZY: '15010000',
    /** 考试成绩管理 */
    ENV_KSCJFX: '15020000',
    /** 考试成绩录入code */
    ENV_CJLR: '15020100',
    /** 考试成绩分析code */
    ENV_CJFX: '15020200',
    /** 参数设定code */
    ENV_CSSD: '15020300',
    /** 成绩汇总 */ ENV_CJHZ: '15020500',
    /** 成绩查询code */
    ENV_CJCX: '15030000',

    ENV_KQGL: '12120000',
    ENV_TZGG: '17000000',
    ENV_DXTZGG: '18050000',

    /** 智慧校务 */
    ENV_ZHXW: '18000000',
    /** 问卷管理 */
    ENV_WJGL: '18010000',
    /** 问卷填写 */
    ENV_WJTX: '18010200',
    /** 问卷列表 */
    ENV_WJLB: '18010100',
    /** 问卷统计 */
    ENV_WJTJ: '18010300',
    /** 问卷模板 */
    ENV_WJMB: '18010400',
    /** 收集表管理 */
    ENV_SJBGL: '18020000',
    /** 收集表填写 */
    ENV_SJBTX: '18020200',
    /** 收集表列表 */
    ENV_SJBLB: '18020100',
    /** 收集表统计 */
    ENV_SJBTJ: '18020300',
    /** 收集表模板 */
    ENV_SJBMB: '18020400',
    /** 报修服务管理管理 */
    ENV_BXFW: '18030000',
    /** 报修工单 */
    ENV_BXGD: '18030100',
    /** 报修工单统计 */
    ENV_BXTJ: '18030200',
    /** 报修服务配置 */
    ENV_BXPZ: '18030300',
    /** 财务收费管理 */
    ENV_SFGL: '18040100',
    /** 财务收费项配置 */
    ENV_SFPZ: '18040200',
    /**巡课管理 */
    ENV_PATROL: '18080000',
    /**巡课管理-巡课安排 */
    ENV_PATROLAP: '18080300',
    /**教师月度考核 */
    ENV_JSYDKH: '18140000',
    /** 校本应用code */
    ENV_XBYY: '98000000',

    /** 系统管理code */
    ENV_XTGL: '99000000',
    /** 应用管理 */
    ENV_YYGL: '99010000',
    /** 账号管理 */
    ENV_ZHGL: '99020000',
    /** 角色管理 */
    ENV_JSGL: '99030000',
    /** 授权管理 */
    ENV_SQGL: '99040000',
    /** 常用工具 */
    ENV_CYGJ: '99060000',
    /** 友情链接 */
    ENV_YQLJ: '99070000',

    ENV_clientId: '00010',
    ENV_GL: '01',
    ENV_CK: '02',
    ENV_SP: '03',
    ENV_WX: '26',
    ENV_CX: '27',
    ENV_BJZL: '28',
    ENV_NJZL: '29',
    ENV_QXZL: '30',
    ENV_CJFX_FX: '31',
    ENV_NEW_SP: '32',
    ENV_BZR_SP: '33',
    ENV_JDC_SP: '34',
    ENV_XLD_SP: '35',
    ENV_TJSQ: '36',
    ENV_QXHZ: '37',
    ENV_NJHZ: '38',
    ENV_BJHZ: '39',
    ENV_zhNum: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'],
    ENV_weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    ENV_plat_enterprise_code: '01001',
    ENV_clientSecret: '05bbo95az8nC8yNtg4tl',
    test_lh6: {
      老师: {
        初中语文: 'JXU1MjFEJXU0RTJEJXU4QkVEJXU2NTg3',
        初中数学: 'JXU1MjFEJXU0RTJEJXU2NTcwJXU1QjY2',
        初中英语: 'JXU1MjFEJXU0RTJEJXU4MkYxJXU4QkVE',
      },
      学生: 'JXU1MjFEJXU0RTJEJXU1QjY2JXU3NTFG',
    },
    TENCENT_COS: {
      SecretId: 'AKIDJRL2VjjU2JL7g06xZy84lErGSREl391e',
      SecretKey: 'ShhjY9WgfR1OuKVY1KPJ0SK0SgjY9e0B',
      baseDir: 'smart_screen/',
    },
  },
  routes,
  qiankun: {
    master: {},
    slave: {},
  },
  chainWebpack: (config: any) => {
    // 以下为gzip配置内容
    config.plugin('compression-webpack-plugin').use(
      new CompressionWebpackPlugin({
        // filename: 文件名称，这里我们不设置，让它保持和未压缩的文件同一个名称
        algorithm: 'gzip', // 指定生成gzip格式
        test: new RegExp('\\.(' + prodGzipList.join('|') + ')$'), // 匹配哪些格式文件需要压缩
        threshold: 10240, //对超过10k的数据进行压缩
        minRatio: 0.6, // 压缩比例，值为0 ~ 1
      }),
    )
    // 用于处理MP3文件的loader
    config.module
      .rule('mp3')
      .test(/\.(mp3)$/)
      .use('file-loader')
      .loader('file-loader')
    // 用于处理视频文件的loader
    config.module
      .rule('video')
      .test(/\.(mp4|webm|ogg|wav|flac|aac)(\?.*)?$/)
      .use('file-loader')
      .loader('file-loader')
  },
  proxy: {
    '/api_score': {
      target: 'http://*************:3070',
      // target: 'http://*************:3070',
      changeOrigin: true,
      pathRewrite: { '^/api_score': '' },
    },
    '/edu_api': {
      // target: 'http://*************:3040',
      target: 'http://*************:3040',
      // target: 'http://*************:3040',
      // target: 'http://************:3040',
      changeOrigin: true,
      pathRewrite: { '^/edu_api': '' },
    },
    // 客户管理系统
    '/customer_api': {
      target: 'http://customer-management.test.xingjiaoyun.cn',
      changeOrigin: true,
    },
    '/question_api': {
      target: 'http://*************:3080',
      changeOrigin: true,
      pathRewrite: { '^/question_api': '' },
    },
    '/request_api': {
      target: 'https://request.xingjiaoyun.cn',
      changeOrigin: true,
      pathRewrite: { '^/request_api': '' },
    },
    '/api_screen': {
      target: 'http://*************:3040',
      changeOrigin: true,
      pathRewrite: { '^/api_screen': '' },
    },
    '/tencent': {
      target: 'https://apis.map.qq.com',
      changeOrigin: true,
      pathRewrite: { '^/tencent': '' },
    },
    '/common_api': {
      target: 'http://*************:1002',
      changeOrigin: true,
      pathRewrite: { '^/common_api': '' },
    },
    // 公文系统
    '/dispatch_center_api': {
      target: 'http://despatch.test.xingjiaoyun.cn',
      changeOrigin: true,
    },
  },
})
