import React, { useEffect } from 'react'
import { ExportOutlined } from '@ant-design/icons'
import { Button, message, Segmented, Spin } from 'antd'
import CommonTable from './commonTable'
import styles from './index.less'
import { controllerExaminationShow as getDetailFrom } from '@/services/score-analysis/examination'
import { index } from '@/services/edu-platform-web/summary_by_examination'

interface SchoolSummaryGradesProps {
  examinationId: string
}

const SchoolSummaryGrades: React.FC<SchoolSummaryGradesProps> = ({ examinationId }) => {
  const [dataSource, setDataSource] = React.useState<any[]>([])
  const [selectSubject, setSelectSubject] = React.useState<string>()
  const [subjectOptions, setSubjectOptions] = React.useState<any[]>([])
  const [loading, setLoading] = React.useState<boolean>(false)
  /** 获取数据 */
  const getData = async (subjectName: string) => {
    setLoading(true)
    const res = await index({
      examinationId,
      subjectName,
    })
    if (res?.errCode) {
      setLoading(false)
      return message.warning(`获取数据失败，请联系管理员或稍后再试！${res?.msg}`)
    }
    setLoading(false)
    const newData = [
      ...res?.item,
      {
        gradeName: '合计',
        yyrs: res?.school_yyrs,
        skrs: res?.school_skrs,
        yx: res?.school_yx,
        yxl: res?.school_yxl,
        hg: res?.school_hg,
        hgl: res?.school_hgl,
        bhg: res?.school_bhg,
        bhgl: res?.school_bhgl,
        qkrs: res?.school_qkrs,
        lh: res?.school_lh,
        lhl: res?.school_lhl,
        pjf: res?.school_pjf,
      },
    ]

    setDataSource(newData || [])
  }

  const resetForm = async () => {
    const res = (await getDetailFrom({ id: examinationId })) as ResType<any>
    if (res?.errCode) {
      message.error(res.message || '获取数据失败，请联系管理员或稍后再试')
    } else {
      const gradeArr = res?.examGrads.sort(
        (a: SCORE_API.ExamGradeInfo, b: SCORE_API.ExamGradeInfo) =>
          parseInt(a?.gradeCode + '') - parseInt(b.gradeCode + ''),
      )
      const newGrade: any = gradeArr?.flatMap((v: { subjects: any }) => v.subjects)
      const newData = Array.from(new Set(newGrade?.map((v: any) => v.subjectName)))

      const subData = newData?.map((v: any) => {
        return {
          label: v,
          value: v,
        }
      })
      setSubjectOptions(subData)
      setSelectSubject(subData?.[0]?.value || [])
      getData(subData?.[0]?.value || [])
    }
  }

  const handleDownload = async () => {
    try {
      const baseUrl = '/api_score/download/summary_by_examination'
      const params = new URLSearchParams()
      params.append('examinationId', examinationId || '')
      params.append('subjectName', selectSubject || '')
      const url = `${baseUrl}?${params.toString()}`
      const a = document.createElement('a')
      a.href = url
      a.download = '成绩汇总.xlsx'
      document.body.appendChild(a)
      a.click()
      a.remove()
      message.success('导出成功')
    } catch (error) {
      message.error('导出失败，请联系管理员或稍后再试！' + error)
    }
  }

  useEffect(() => {
    resetForm()
  }, [])

  return (
    <>
      <header className={styles.schoolHeader}>
        <div>
          学科：
          <Segmented
            value={selectSubject}
            options={subjectOptions}
            onChange={(value) => {
              setSelectSubject(String(value))
              getData(String(value))
            }}
          />
        </div>

        <Button
          type="primary"
          onClick={handleDownload}
          icon={<ExportOutlined />}
          disabled={dataSource.length === 0}
        >
          导出
        </Button>
      </header>
      <Spin spinning={loading} tip="数据获取中，请稍候...">
        <CommonTable type="school" data={dataSource} />
      </Spin>
    </>
  )
}
export default SchoolSummaryGrades
