// @ts-ignore
/* eslint-disable */
import { request } from 'umi'

/** 获取教师所带班级或课程  GET /coursesAndClasses_for_teacher */
export async function controllerManageClass(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: any,
  options?: { [key: string]: any },
) {
  return request<any>('/coursesAndClasses_for_teacher', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** 学生点名 POST /student_kq */
export async function controllerStudentAttendanceCreate(
  body: any,
  options?: { [key: string]: any },
) {
  return request<any>(`/student_kq`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 获取学生考勤  GET /student_kq */
export async function controllerStudentAttendanceIndex(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: any,
  options?: { [key: string]: any },
) {
  return request<API.commonResponse>(`/student_kq`, {
    method: 'GET',
    params,
    ...(options || {}),
  })
}
/** 获取学生考勤  GET /restful/student_by_classcode */
export async function controllerStudentAttendance(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    enterpriseCode: string
    semesterCode: string
    classCode?: string
    gradeCode?: string
    startTime?: string
    endTime?: string
  },
  options?: { [key: string]: any },
) {
  return request<API.commonResponse>(`/restful/student_by_classcode`, {
    method: 'GET',
    params,
    ...(options || {}),
  })
}

/**
 * 按条件查询社团报名学生名单 GET /restful/getAfterClassBMList
 *
 * @param {{
 *   enterpriseCode: string 学校编号
 *   date: string
 *   sessionNum: number
 *   gradeName: string
 *   className: string
 * }} params
 * @return {*}
 */
export async function getAfterClassBMList(params: {
  enterpriseCode: string
  date: string
  sessionNum: number
  gradeName: string
  className: string
}) {
  return request<API.ResType<any[]>>('/restful/getAfterClassBMList', {
    method: 'GET',
    params,
  })
}

/** 查询未点名班级 GET  /student_kq/get_unnamed_class */
export async function getUnnamedClass(params: {
  enterpriseCode: string
  semesterCode: string
  startDate?: string
  endDate?: string
  orderIndex?: number
  gradeCode?: string
}) {
  return request<API.ResType<any[]>>('/student_kq/get_unnamed_class', {
    method: 'GET',
    params,
  })
}
