import React from 'react'
import SchoolSummaryGrades from './school'
import GradeSummaryGrades from './grade'
import ClassSummaryGrades from './class'

export interface SummaryGradesProps {
  data?: any
}

const SummaryGrades: React.FC<SummaryGradesProps> = ({ data }) => {
  const { data: info, type } = data || {}
  switch (type) {
    case 'school':
      return <SchoolSummaryGrades examinationId={info?.id} />
    case 'grade':
      return <GradeSummaryGrades examinationId={info?.id} />
    case 'class':
      return <ClassSummaryGrades examinationId={info?.id} grade={info?.examGrads} />
  }
}
export default SummaryGrades
