/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import SearchLayout from '@/components/Search/Layout'
import { Button, DatePicker, Modal, Select, Space, Spin, Table, Tabs, message } from 'antd'
import {
  controllerManageClass,
  controllerStudentAttendance,
  controllerStudentAttendanceCreate,
  getAfterClassBMList as getAfterClassBMListApi,
} from '@/services/edu-platform-web/attendanceStudent'
import { controllerRepeatGetStudents } from '@/services/edu-platform-web/ssoRepeat'
import SemesterSelect from '@/components/SemesterSelect'
import { controllerWorkRestTimeNowEffective } from '@/services/edu-platform-web/workRestTime'
import { getColums } from './getColums'
import GroupT from '@/assets/GroupS.png'
import styles from './index.less'
import moment from 'moment'
import NoData from '@/components/NoData'
import { getAfterServiceStatistics } from '@/services/edu-platform-web/after_service_statistics'
import { WhetherJoinEnum } from '@/pages/EducationaManagement/AfterServiceStatistics/columns'
import { ProForm, ProFormDateTimePicker, ProFormText } from '@ant-design/pro-components'

type DataType = {
  id: string
  /** 学生编号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 学生学校编号 */
  work_code: string
  /** 年级名称 */
  gradeName: string
  /** 班级名称 */
  className: string
  /** 实际出勤情况 */
  isRealTo: '缺席' | '请假' | '出勤' | '回家' | '课后服务'
  /** 出勤类型 0 缺勤 1请假 2出席 3课后服务 4回家 */
  type?: 0 | 1 | 2 | 3 | 4
  /** 是否去了课后服务 */
  isKH: boolean
  /** 是否请假 */
  isLeave: boolean
  /** 是否回家 */
  isHome: boolean
  /** 请假原因 */
  leaveYY?: string
  noArrivedState?: 'earlyPermission' | 'noShowLeave'
  studentCondition: string
  trackingTime: Date
  communicationObject: string
}

const getStudentTileDom = (
  checkWorkInfo: {
    shouldArrive: any
    text: string
    key: number
  }[],
) => {
  return (
    <div className={styles.checkWorkAttendance}>
      {checkWorkInfo.map((item) => {
        return (
          <div className={styles.checkWorkInfo} key={item.key}>
            <div
              className={styles.number}
              style={item.text === '缺席' && item.shouldArrive ? { color: '#F5222D' } : {}}
            >
              {item.shouldArrive}
            </div>
            <div className={styles.word}>{item.text}</div>
          </div>
        )
      })}
    </div>
  )
}
const getStundListDom = ({
  dataSource,
  onSwitchItem,
  courseInfo,
  gradeValue,
  loading,
  /** 是不是第七节课 */
  isSeven,
  opInfo,
  onUpdateRecordEven,
  formRef,
  onUpdateRecordInfo,
}: {
  dataSource: DataType[]
  onSwitchItem: (value: any, checked: boolean) => void
  courseInfo: any
  gradeValue: string | undefined
  loading: boolean
  /** 是不是第七节课 */
  isSeven?: boolean
  opInfo?: string
  formRef?: any
  onUpdateRecordEven?: (id: string, value: string) => void
  /** 增加附属信息 */
  onUpdateRecordInfo?: (id: string, newStatus: any) => void
}) => {
  return (
    <div className={styles.studentList}>
      <Table<DataType>
        dataSource={dataSource}
        expandable={{
          columnWidth:'25px',
          expandedRowRender: (record) => (
            <div className={styles.info}>
              <ProForm
                formRef={formRef}
                layout="horizontal"
                submitter={{
                  render: () => {
                    return (
                      <Space className={styles.submit}>
                        <Button
                          type="primary"
                          htmlType="submit"
                          onClick={() => {
                            const formData = formRef.current?.getFieldsValue()
                            const { communicationObject, trackingTime, studentCondition } =
                              formData || {}
                            if (!communicationObject || !trackingTime || !studentCondition) {
                              return message.warning('请填写完整信息！')
                            }
                            onUpdateRecordInfo?.(record.id, formData)
                            message.success('已保存')
                          }}
                        >
                          保存
                        </Button>
                        <Button htmlType="reset">取消</Button>
                      </Space>
                    )
                  },
                }}
              >
                <ProFormText
                  label="学生情况"
                  name="studentCondition"
                  fieldProps={{
                    maxLength: 200,
                  }}
                />
                <ProFormDateTimePicker name="trackingTime" label="追踪时间：" />
                <ProFormText
                  label="沟通对象"
                  name="communicationObject"
                  fieldProps={{
                    maxLength: 200,
                  }}
                />
              </ProForm>
            </div>
          ),
          rowExpandable: (record) =>
            record.isRealTo !== '出勤' && record.noArrivedState === 'noShowLeave',
        }}
        columns={getColums({
          onSwitchItem,
          option: opInfo !== '社团学生去向查询',
          isSeven,
          onUpdateRecord: onUpdateRecordEven,
        })}
        rowKey={(record) =>
          courseInfo ? record.id + courseInfo?.id : gradeValue ? record.id + gradeValue : record.id
        }
        loading={loading}
        pagination={false}
        scroll={{ y: courseInfo ? 'calc(100vh - 395px)' : 'calc(100vh - 360px)' }}
      />
    </div>
  )
}

/** 合并学生列表数据 */
const mergeStudentList = ({
  studentList,
  newList = [],
  type,
}: {
  studentList: any[]
  newList: any[]
  type: 'leave' | 'afterclass'
}): DataType[] => {
  const newStudents: DataType[] = studentList?.map((v: any) => {
    const { code, IDNumber } = v
    const curInfo = newList.find(
      (val: any) =>
        val.userCode === code || val.xh === code || (IDNumber && val.id_card === IDNumber),
    )
    return {
      id: v.id,
      studentCode: v.code,
      studentName: v.name,
      work_code: v.work_code,
      gradeName: v?.classes?.[0]?.grade_name,
      className: v?.classes?.[0]?.name,
      type: curInfo
        ? type === 'leave'
          ? 1
          : curInfo.hasCourse
          ? 3
          : !curInfo.isSignUp
          ? 4
          : 2
        : 2,
      isRealTo: curInfo
        ? type === 'leave'
          ? '请假'
          : curInfo.hasCourse
          ? '课后服务'
          : !curInfo.isSignUp
          ? '回家'
          : '出勤'
        : '出勤',
      stkc_name: curInfo?.stkc_name,
      isKH: !!curInfo && type === 'afterclass' && curInfo.hasCourse,
      isHome: !!curInfo && type === 'afterclass' && !curInfo.hasCourse && !curInfo.isSignUp,
      isLeave: !!curInfo && type === 'leave',
      leaveYY: curInfo?.reason,
    }
  })
  return newStudents
}

const StudentsCheckon = () => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const date = moment(new Date())
  const [termCode, setTermCode] = useState<string>()
  const [loading, setLoading] = useState<boolean>(false)
  // '缺席'
  const [absent, setAbsent] = useState<number>(0)
  const [leaveData, setLeaveData] = useState<number>(0)
  // '出勤'
  const [comeClass, setComeClass] = useState<number>(0)
  // '课后服务'
  const [afterClass, setAfterClass] = useState<number>(0)
  // '回家'
  const [homeClass, setHomeClass] = useState<number>(0)
  // 表格数据
  const [dataSource, setDataScouse] = useState<DataType[]>([])
  const [studentData, setStudentData] = useState<any>([])
  /** 年级班级列表 */
  const [gradelist, setGradeList] = useState<any[]>([])
  /** 当前选中的年级班级 */
  const [gradeValue, setGradeValue] = useState<string>()
  /** 班级列表 */
  const [courseData, setCourseData] = useState<any>([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  /** 节次信息列表 */
  const [sectionData, setSectionData] = useState<API.workRestTime[]>()
  /** 班级、节次合并后的信息 */
  const [courseOption, setCourseOption] = useState<any>([])
  /** 当前选中的课程 */
  const [courseInfo, setCourseInfo] = useState<any>()
  /** 当前选中的年级班级 */
  const [clsInfo, setClsInfo] = useState<any>()
  /** 当前选中的状态 */
  const [opInfo, setOpInfo] = useState<any>('课堂点名')

  const [time, setTime] = useState<string>(date.format('YYYY-MM-DD'))
  /** 是不是第七节课 */
  const [isSeven, setIsSeven] = useState<boolean>()
  /** 已请假 未请假人数 */
  const [leaveInfo, setLeaveInfo] = useState<{
    leaveNum: number
    noLeaveNum: number
  }>({
    leaveNum: 0,
    noLeaveNum: 0,
  })
  const formRef = useRef<any>(null)

  const onUpdateRecord = (id: string, newStatus: any) => {
    console.log(id, newStatus)

    setDataScouse((prev) =>
      prev.map((record) => (record.id === id ? { ...record, noArrivedState: newStatus } : record)),
    )
  }

  console.log(dataSource)

  const onUpdateRecordInfo = (id: string, newStatus: any) => {
    setDataScouse((prev) =>
      prev.map((record) => (record.id === id ? { ...record, ...newStatus } : record)),
    )
  }

  const checkWorkInfo = [
    { shouldArrive: dataSource.length, text: '总人数', key: 1 },
    { shouldArrive: comeClass, text: '到课', key: 2 },
    { shouldArrive: absent, text: '缺席', key: 3 },
    {
      shouldArrive: `${leaveInfo?.leaveNum} / ${leaveInfo?.noLeaveNum}`,
      text: '已请假 / 未请假',
      key: 4,
    },
    { shouldArrive: leaveData + afterClass + homeClass, text: '其他', key: 5 },
  ]

  /** 查询查询社团报名学生名单 */
  const getAfterClassBMListST = async ({
    className,
    gradeName,
    sessionNum,
  }: {
    className: string
    gradeName: string
    sessionNum: number
  }) => {
    /** 课后服务报名名单 */
    const afterClassBmListRes: any = sessionNum
      ? await getAfterClassBMListApi({
          enterpriseCode: schoolInfo?.code || '',
          date: time,
          // sessionNum: courseInfo.orderIndex,
          sessionNum,
          gradeName,
          className,
        })
      : { list: [] }
    if (afterClassBmListRes.errCode) {
      return []
    }
    return afterClassBmListRes.list
  }
  /** 查询课后服务报名名单 */
  const getAfterClassBMList = async ({
    gradeCode,
    classCode,
  }: {
    classCode: string
    gradeCode: string
  }): Promise<any[]> => {
    const res = await getAfterServiceStatistics({
      year: moment(time).year(),
      month: moment(time).month() + 1,
      classCode,
      gradeCode,
      enterpriseCode: currentUser?.enterpriseCode || '',
    })
    if (res?.errCode) {
      message.warning('获取报名信息失败，请联系管理员或稍后再试！' + res?.msg)
      return []
    }
    return res.list ?? []
  }

  /** 查询学生请假名单 */
  const getRollCall = async ({
    classCode,
    gradeCode,
    startTime,
    endTime,
  }: {
    gradeCode: string
    classCode: string
    startTime?: string
    endTime?: string
  }) => {
    /** 班级请假学生名单 */
    const res = (await controllerStudentAttendance({
      enterpriseCode: schoolInfo?.code || '',
      semesterCode: termCode || '',
      classCode,
      gradeCode,
      startTime,
      endTime,
    })) as any

    if (res?.errCode) {
      console.warn(res?.msg || '班级请假学生获取失败，请联系管理员或稍后再试！')
      return []
    }
    return res as any[]
  }

  /**
   * 通过年级班级信息获取学生信息
   * @param classCode
   * @param grade
   */
  const getStudents = async ({
    classCode,
    grade,
    className,
    gradeName,
    sessionNum,
    startTime,
    endTime,
    isSevenStatus,
    isEight,
  }: {
    classCode: string
    grade: string
    className: string
    gradeName: string
    sessionNum: number
    startTime?: string
    endTime?: string
    /** 是否是第七节课  */
    isSevenStatus?: boolean
    /** 是否是第八节课 */
    isEight?: boolean
  }) => {
    setLoading(true)
    const result = await controllerRepeatGetStudents({
      enterpriseId: schoolInfo?.id || '',
      classCode,
      grade,
      semesterCode: termCode,
      // type: '课后服务',
      // enterpriseCode: schoolInfo?.code || '',
    })
    if (result?.errCode) {
      setLoading(false)
      return message.warning('获取学生列表失败，请联系管理员或稍后再试' + result?.msg)
    } else {
      setLoading(false)
      setStudentData(result?.list || [])
      // 查询请假名单
      const rollList = await getRollCall({
        classCode,
        gradeCode: grade,
        startTime,
        endTime,
      })
      // 查询课后服务名单 （ 社团 ）
      const afterClassBmListST = await getAfterClassBMListST({
        className,
        gradeName,
        sessionNum,
      })
      // 查询是否参与课后服务名单
      const afterClassBmList = await getAfterClassBMList({
        classCode,
        gradeCode: grade,
      })

      const newStudents_leave = mergeStudentList({
        studentList: result?.list,
        newList: rollList,
        type: 'leave',
      })
      const newStudent_afterclass = mergeStudentList({
        studentList: result?.list,
        newList: afterClassBmListST,
        type: 'afterclass',
      })
      // 合并列表数据 ( 社团 )
      const newStudents = result?.list
        ?.map((_: any, index: number) => {
          // 请假优先级大于课后服务
          if (newStudents_leave[index]?.isLeave) {
            return newStudents_leave[index]
          }

          return newStudent_afterclass[index]
        })
        ?.sort((a: any, b: any) => {
          return a.isHome === b.isHome
            ? a.isKH === b.isKH
              ? 0
              : a.isKH
              ? 1
              : -1
            : a.isHome
            ? 1
            : -1
        })

      //  家长是否确认参加，第7节使用
      const newStudentSeven = result?.list
        ?.map((_: any, index: number) => {
          // 请假优先级大于课后服务
          if (newStudents_leave[index]?.isLeave) {
            return newStudents_leave[index]
          }
          const { whetherJoin } = afterClassBmList[index] || {}
          return {
            type: whetherJoin === WhetherJoinEnum['已参与'] ? 2 : 0,
            isRealTo: whetherJoin === WhetherJoinEnum['已参与'] ? '出勤' : '缺席',
            ...afterClassBmList[index],
          }
        })
        .sort((a: any, b: any) => {
          const order: Record<number, number> = {
            1: 0,
            0: 1,
            2: 2,
          }
          const aKey = order.hasOwnProperty(a.whetherJoin) ? a.whetherJoin : 2
          const bKey = order.hasOwnProperty(b.whetherJoin) ? b.whetherJoin : 2
          return order[aKey] - order[bKey]
        })
      if (isEight) {
        // 第8节需要合并140报名信息
        newStudents.forEach((na: any) => {
          if (!na.isLeave && !na.isKH) {
            const current = newStudentSeven.find((nss: any) => nss.studentCode === na.work_code)
            if (current) {
              switch (current.whetherJoin) {
                case WhetherJoinEnum['已参与']:
                  na.isHome = false
                  na.type = 2
                  na.isRealTo = '出勤'
                  break
                case WhetherJoinEnum['未确认']:
                case WhetherJoinEnum['不参与']:
                default:
                  na.isHome = true
                  na.type = 4
                  na.isRealTo = '回家'
                  break
              }
            } else {
              na.isHome = true
              na.type = 4
              na.isRealTo = '回家'
            }
          }
        })
      }

      if (isSevenStatus) {
        setDataScouse(newStudentSeven)
      } else {
        setDataScouse(newStudents)
      }
    }
  }

  // 获取节次数据
  const getSection = async () => {
    const res = (await controllerWorkRestTimeNowEffective({
      enterpriseCode: schoolInfo?.code || '',
      sectionCode: schoolInfo?.section_code || '',
      // sectionCode: Keys || SelectedGrade?.key?.substring(0, 2),
      typeCode: '01',
      semesterCode: termCode!,
    })) as ResType<any>
    if (res?.errCode) {
      message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
    } else {
      setSectionData(res?.list)
    }
  }
  /**
   * 根据时间获取任课信息
   * @param curTime
   */
  const getCourseData = async (curTime: string) => {
    setLoading(true)
    const result = await controllerManageClass({
      enterpriseCode: schoolInfo?.code || '',
      teacherCode: currentUser?.userCode,
      memberId: currentUser?.memberId,
      weekday: new Date(curTime).getDay(),
    })
    if (result?.errCode) {
      setLoading(false)
      return message.warning(
        '获取获取教师所带班级或课程失败，请联系管理员或稍后再试！' + result?.msg,
      )
    } else {
      setLoading(false)
      if (result?.course?.length) {
        setCourseData(result?.course)
      } else {
        setCourseData([])
      }
    }
  }
  const getClass = async () => {
    setLoading(true)
    const result = await controllerManageClass({
      enterpriseCode: schoolInfo?.code || '',
      teacherCode: currentUser?.userCode,
      memberId: currentUser?.memberId,
      weekday: new Date().getDay(),
    })
    if (result?.errCode) {
      setLoading(false)
      console.warn(result?.msg || '班级获取失败，请联系管理员或稍后再试！')
    } else {
      setLoading(false)
      if (result?.course?.length) {
        setCourseData(result?.course)
      } else {
        setCourseData([])
      }
      if (result?.classes?.length) {
        const gradeArr: any[] = []
        result?.classes?.forEach((item: any) => {
          gradeArr.push({
            label: item.grade_name + ' ' + item.name,
            value: item.id,
            gradeCode: item.grade_code,
            classCode: item.code,
            gradeName: item.grade_name,
            classMc: item.name,
          })
        })
        setGradeValue(gradeArr?.[0]?.value)
        setGradeList(gradeArr)
      }
    }
  }
  const onSwitchItem = (value: any, checked: boolean) => {
    const newData = [...dataSource]
    newData.forEach((item) => {
      if (item?.id === value?.id) {
        if (
          item?.studentCondition ||
          item?.noArrivedState ||
          item?.trackingTime ||
          item?.communicationObject
        ) {
          if ('studentCondition' in item) delete (item as any).studentCondition
          if ('noArrivedState' in item) delete (item as any).noArrivedState
          if ('trackingTime' in item) delete (item as any).trackingTime
          if ('communicationObject' in item) delete (item as any).communicationObject
        }
        if (checked) {
          item.isRealTo = '出勤'
          item.type = 2
        } else {
          if (item?.isLeave) {
            item.isRealTo = '请假'
            item.type = 1
          } else if (item.isKH) {
            item.isRealTo = '课后服务'
            item.type = 3
          } else if (item.isHome) {
            item.isRealTo = '回家'
            item.type = 4
          } else {
            item.isRealTo = '缺席'
            item.type = 0
          }
        }
      }
    })
    setDataScouse(newData)
  }
  const onButtonClick = async () => {
    const params: any = {
      enterpriseCode: schoolInfo?.code,
      enterpriseName: schoolInfo?.name,
      yds: dataSource?.length,
      sds: comeClass,
      qqs: absent,
      qjs: leaveData + afterClass + homeClass,
      clockInTime: time,
      semesterCode: termCode,
      data: dataSource,
      earlyPermissionNumber: leaveInfo?.leaveNum,
      noShowLeaveNumber: leaveInfo?.noLeaveNum,
    }
    if (courseInfo) {
      // 处理调代课后教师点名问题
      // params.teacherCode = courseInfo.teacherCode
      // params.teacherName = courseInfo.teacherName
      params.teacherCode = currentUser?.userCode
      params.teacherName = currentUser?.realName
      params.courseCode = courseInfo.courseCode
      params.courseName = courseInfo.courseName
      params.subjectCode = courseInfo.subjectCode
      params.subjectName = courseInfo.subjectName
      params.gradeCode = courseInfo.gradeCode
      params.gradeName = courseInfo.gradeName
      params.classCode = courseInfo.classCode
      params.className = courseInfo.className
      params.weekday = courseInfo.weekday
      params.orderIndex = courseInfo.orderIndex
    } else {
      const grade = gradelist?.find((v) => v.value === gradeValue)
      params.bzr_teacherCode = currentUser?.userCode
      params.bzr_teacherName = currentUser?.realName
      params.gradeCode = grade?.gradeCode
      params.gradeName = grade?.gradeName
      params.classCode = grade?.classCode
      params.className = grade?.classMc
    }
    const stateLost = params?.data.some((item: any) => {
      return isSeven
        ? item?.isRealTo === '缺席' &&
            !item.hasOwnProperty('noArrivedState') &&
            item.whetherJoin === WhetherJoinEnum['已参与']
        : item?.isRealTo === '缺席' && !item.hasOwnProperty('noArrivedState')
    })

    if (stateLost) {
      return message.warning('状态不完整请检查！')
    }

    const { errCode, message: msg } = await controllerStudentAttendanceCreate({
      ...params,
    })
    if (errCode) {
      message.error(msg || '点名失败，请联系管理员或稍后再试')
    } else {
      setIsModalVisible(true)
    }
  }

  useEffect(() => {
    ;(async () => {
      // 获取任课教程与班主任带课班级信息
      await getClass()
    })()
  }, [])

  useEffect(() => {
    if (termCode) {
      // 获取当前使用的作息方案
      getSection()
    }
  }, [termCode])

  useEffect(() => {
    // 计算学生出勤状态与数据
    const absentData = dataSource.filter((item) => item.isRealTo === '缺席')
    const comeClassData = dataSource.filter((item) => item.isRealTo === '出勤')
    const afterClassData = dataSource.filter((item) => item.isRealTo === '课后服务')
    const homeClassData = dataSource.filter((item) => item.isRealTo === '回家')
    const lData = dataSource.filter(
      (item: any) => item.isLeave === true && item.isRealTo === '请假',
    )
    const leaveCount = dataSource.filter((item: any) => item.noArrivedState === 'earlyPermission')
    const noLeaveCount = dataSource.filter((item: any) => item.noArrivedState === 'noShowLeave')

    console.log(leaveCount, noLeaveCount)

    setLeaveData(lData.length)
    setAbsent(absentData.length)
    setComeClass(comeClassData.length)
    setAfterClass(afterClassData.length)
    setHomeClass(homeClassData.length)
    setLeaveInfo({
      leaveNum: leaveCount.length,
      noLeaveNum: noLeaveCount.length,
    })
  }, [dataSource])

  useEffect(() => {
    // 重构任教课程（处理节次展示问题）
    if (sectionData && courseData) {
      setLoading(false)
      const courseOp = courseData.map((v: any) => {
        const sectionInfo = sectionData?.[v.orderIndex - 1]
        return {
          ...v,
          label: sectionInfo?.title ? `${sectionInfo?.title} ${v.courseName}` : v.courseName,
          value: v.id,
          startTime: `${time} ${sectionInfo?.start_time}`,
          endTime: `${time} ${sectionInfo?.end_time}`,
        }
      })
      setCourseOption(courseOp)
    }
  }, [courseData, sectionData])
  return (
    <div className={styles.studentsCheckon}>
      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="1"
          destroyInactiveTabPane
          onChange={(activeKey: string) => {
            setCourseInfo(undefined)
            setStudentData([])
            setDataScouse([])
            setLeaveData(0)
            setAbsent(0)
            setComeClass(0)
            if (activeKey === '2') {
              const grade = gradelist.find((v) => v.value === gradeValue)
              setClsInfo({
                classCode: grade?.classCode,
                grade: grade?.gradeCode,
                className: grade?.classMc,
                gradeName: grade?.gradeName,
              })
              getStudents({
                classCode: grade?.classCode,
                grade: grade?.gradeCode,
                className: grade?.classMc,
                gradeName: grade?.gradeName,
                sessionNum: 0,
                startTime: time,
                endTime: time,
              })
            } else {
              setOpInfo('课堂点名')
            }
          }}
        >
          <Tabs.TabPane tab={'任课教师点名'} key="1" className={styles.tabPane}>
            <SearchLayout>
              <SemesterSelect
                onChange={(val) => {
                  setTermCode(val)
                }}
              />
              <div>
                <label htmlFor="xm">点名日期：</label>
                <DatePicker
                  style={{ width: '180px' }}
                  onChange={(_, dateString) => {
                    setCourseInfo(undefined)
                    setDataScouse([])
                    getCourseData(dateString)
                    setTime(dateString)
                  }}
                  // disabledDate={(current) => {
                  //   // Can not select days before today and today
                  //   return current && current > dayjs().endOf('day')
                  // }}
                  defaultValue={date}
                />
              </div>
              <div>
                <label htmlFor="xm">任教课程：</label>
                <Select
                  options={courseOption}
                  allowClear={false}
                  value={courseInfo?.value}
                  onChange={(_, option: any) => {
                    setIsSeven(option?.label?.includes('第七节'))
                    setDataScouse([])
                    getStudents({
                      classCode: option.classCode,
                      grade: option.gradeCode,
                      className: option.className,
                      gradeName: option.gradeName,
                      sessionNum: option.orderIndex,
                      startTime: option.startTime,
                      endTime: option.endTime,
                      isSevenStatus: option?.label?.includes('第七节'),
                      isEight: option?.label?.includes('第八节'),
                    })

                    setCourseInfo(option)
                  }}
                />
              </div>
            </SearchLayout>
            {courseInfo && (
              <div
                style={{
                  lineHeight: '45px',
                  color: '#2979ff',
                  maxWidth: '900px',
                  margin: '0 auto',
                }}
              >
                【{courseInfo?.gradeName}/{courseInfo?.className}】{courseInfo?.courseName}课堂点名
              </div>
            )}
            {!!dataSource?.length ? (
              <>
                {getStudentTileDom(checkWorkInfo)}
                {getStundListDom({
                  dataSource,
                  onSwitchItem,
                  courseInfo,
                  gradeValue,
                  loading,
                  isSeven,
                  formRef,
                  onUpdateRecordEven: onUpdateRecord,
                  onUpdateRecordInfo,
                })}
              </>
            ) : (
              <div
                style={{
                  marginTop: '100px',
                }}
              >
                <NoData desc="暂无学生可点名" />
              </div>
            )}
          </Tabs.TabPane>
          {/* {gradelist?.length > 0 && (
            <Tabs.TabPane tab={'班主任点名'} key="2" className={styles.tabPane}>
              <SearchLayout>
                <SemesterSelect
                  onChange={(val) => {
                    setTermCode(val)
                  }}
                />
                <div>
                  <label htmlFor="xm">年级班级：</label>
                  <Select
                    options={gradelist}
                    allowClear={false}
                    defaultValue={gradeValue}
                    onChange={(value, option) => {
                      setGradeValue(value)
                      setClsInfo({
                        classCode: option?.classCode,
                        grade: option?.gradeCode,
                        className: option?.classMc,
                        gradeName: option?.gradeName,
                      })
                      if (opInfo === '社团学生去向查询') {
                        const sectionInfo = sectionData?.[sectionData?.length - 1]
                        getStudents({
                          classCode: option.classCode,
                          grade: option.gradeCode,
                          className: option.className,
                          gradeName: option.gradeName,
                          sessionNum: sectionData?.length || 0,
                          startTime: `${time} ${sectionInfo?.start_time}`,
                          endTime: `${time} ${sectionInfo?.end_time}`,
                        })
                      } else {
                        getStudents({
                          classCode: option?.classCode,
                          grade: option?.gradeCode,
                          className: option?.classMc,
                          gradeName: option?.gradeName,
                          sessionNum: 0,
                          startTime: time,
                          endTime: time,
                        })
                      }
                    }}
                  />
                </div>
                <div>
                  <label htmlFor="xm">点名日期：</label>
                  <DatePicker
                    style={{ width: '180px' }}
                    onChange={async (_, dateString) => {
                      const grade = gradelist.find((v) => v.value === gradeValue)
                      setTime(dateString)
                      const rollList = await getRollCall({
                        classCode: grade?.classCode,
                        gradeCode: grade?.gradeCode,
                        startTime: dateString,
                        endTime: dateString,
                      })
                      const newStudents_leave = mergeStudentList({
                        studentList: studentData || [],
                        newList: rollList,
                        type: 'leave',
                      })
                      setDataScouse(newStudents_leave)
                    }}
                    disabledDate={(current) => {
                      // Can not select days before today and today
                      return current && current > dayjs().endOf('day')
                    }}
                    defaultValue={date}
                  />
                </div>
                <Segmented
                  className={styles.segmentedControl}
                  options={['课堂点名', '社团学生去向查询']}
                  onChange={(value) => {
                    setDataScouse([])
                    setOpInfo(value) // string
                    if (value === '社团学生去向查询') {
                      const sectionInfo = sectionData?.[sectionData?.length - 1]
                      getStudents({
                        ...clsInfo,
                        sessionNum: sectionData?.length || 0,
                        startTime: `${time} ${sectionInfo?.start_time}`,
                        endTime: `${time} ${sectionInfo?.end_time}`,
                      })
                    } else {
                      getStudents({
                        ...clsInfo,
                        sessionNum: 0,
                        startTime: time,
                        endTime: time,
                      })
                    }
                  }}
                />
              </SearchLayout>
              {!!dataSource?.length ? (
                opInfo === '课堂点名' ? (
                  <>
                    {getStudentTileDom(checkWorkInfo)}
                    {getStundListDom(
                      dataSource,
                      onSwitchItem,
                      courseInfo,
                      gradeValue,
                      loading,
                      isSeven,
                    )}
                  </>
                ) : (
                  <>
                    <h3
                      style={{
                        margin: '10px 0',
                        color: '#1890ff',
                        fontSize: '16px',
                        textAlign: 'center',
                        fontWeight: 'bold',
                      }}
                    >
                      社团课程期间班级学生去向查询
                    </h3>
                    {getStundListDom(
                      dataSource,
                      onSwitchItem,
                      courseInfo,
                      gradeValue,
                      loading,
                      isSeven,
                      opInfo,
                    )}
                  </>
                )
              ) : (
                <div
                  style={{
                    marginTop: '100px',
                  }}
                >
                  <NoData desc="暂无学生可点名" />
                </div>
              )}
            </Tabs.TabPane>
          )} */}
        </Tabs>
        {!!dataSource?.length && opInfo !== '社团学生去向查询' && (
          <div className={styles.footerButton}>
            <Button
              type="primary"
              shape="round"
              onClick={() => {
                if (dataSource?.length > 0) {
                  onButtonClick()
                }
              }}
              disabled={dataSource?.length === 0}
            >
              确认点名
            </Button>
          </div>
        )}
      </Spin>
      <Modal className={styles.SignIn} open={isModalVisible} footer={null} closable={false}>
        <img src={GroupT} alt="" />
        <h3>点名成功</h3>
        <Button
          type="primary"
          onClick={() => {
            // history.push('/teacher');
            setIsModalVisible(false)
          }}
        >
          我知道了
        </Button>
      </Modal>
    </div>
  )
}

export default StudentsCheckon
