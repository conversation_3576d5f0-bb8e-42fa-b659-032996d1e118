import React, { useEffect, useState } from 'react'
import {
  DrawerForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormTimePicker,
  ProFormUploadButton,
} from '@ant-design/pro-components'
import { useModel } from 'umi'
import { Button, Form, Modal, Space, Upload, message } from 'antd'
import { envjudge, getActivedTerm, getImageId, getNjName } from '@/utils'
import { controllerUploadReplace } from '@/services/edu-platform-web/upload'
import { controllerTeacherBasicTeacherList } from '@/services/edu-platform-web/teacherBasic'
import type { RcFile } from 'antd/es/upload'
import dayjs from 'dayjs'
import type { UploadFile } from 'antd/es/upload/interface'
import styles from './index.less'
import { getBase64 } from './utils'
import {
  controllerRepeatGetClasses,
  controllerRepeatGetGrades,
  controllerRepeatGetStudents,
} from '@/services/edu-platform-web/ssoRepeat'
import { homeVisitCreate, homeVisitUpdate } from '@/services/edu-platform-web/home_visit'
import { StatusEnum } from '../../utils'

type screenType = {
  label: string
  value: string
  code?: string
}[]
const ApplyForm = ({
  modalVisible,
  setModalVisible,
  refreshTable,
}: {
  modalVisible: {
    visible: boolean
    data?: any
    type?: string
  }
  setModalVisible: React.Dispatch<
    React.SetStateAction<{
      visible: boolean
      data?: any
    }>
  >
  refreshTable: () => void
}) => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const [form] = Form.useForm()
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  //教师列表
  const [teacherList, setTeacherList] = useState<any[]>([])
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [previewTitle, setPreviewTitle] = useState('')
  const [NJData, setNJData] = useState<screenType>([])
  const [BJData, setBJData] = useState<screenType>([])
  const [semesterCode, setSemesterCode] = useState<string>()
  const [isDraft, setIsDraft] = useState<boolean | null>(false)
  const [readonly, setReadonly] = useState<boolean>(false)
  // 获取当前学年学期
  const getTermCode = async () => {
    const res = (await getActivedTerm(schoolInfo?.id || '')) || {}
    setSemesterCode(res?.semester_code)
  }
  const onFinish = async (value: any) => {
    const {
      id,
      visitImg,
      studentCode,
      startTime_time,
      end_time,
      start_time,
      endTime_time,
      ...rest
    } = value
    if (dayjs(end_time + ' ' + endTime_time).isBefore(dayjs(start_time + ' ' + startTime_time))) {
      message.warning('结束日期不能早于开始日期')
      return false
    }
    const info: any = {
      ...rest,
      visitImg: visitImg
        ? visitImg
            .map(
              (item: { response: { data: { url: string } } }) => item.response?.data?.url || 'null',
            )
            .join(',')
        : undefined,
      end_time: end_time + ' ' + endTime_time,
      start_time: start_time + ' ' + startTime_time,
      startTime_time: undefined,
      endTime_time: undefined,
      studentCode: Array.isArray(studentCode) ? studentCode?.[0]?.value : studentCode,
      status:
        isDraft !== null
          ? isDraft
            ? StatusEnum['草稿']
            : modalVisible?.type
            ? StatusEnum['已结束']
            : StatusEnum['待审批']
          : undefined,
    }
    let result
    if (id) {
      result = await homeVisitUpdate(id, { ...info })
    } else {
      result = await homeVisitCreate(info)
    }
    if (result?.errCode) {
      message.warning(
        result.msg || result.message || result.error || '提交失败，请联系管理员或稍后再试！',
      )
      return false
    } else {
      message.success('提交成功')
      form?.resetFields()
      refreshTable?.()
      return true
    }
  }
  const getClassData = async (gradeCode: string) => {
    // 获取班级数据
    const BjArr: screenType = []
    const result = (await controllerRepeatGetClasses({
      grade: gradeCode,
      enterpriseId: schoolInfo?.id || '',
      semesterCode: semesterCode,
    })) as ResType<any>
    if (result?.errCode) {
      message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
    } else {
      result?.list?.map((item: any) => {
        return BjArr.push({
          label: item?.name,
          value: item?.name,
          code: item?.code,
        })
      })
      setBJData(BjArr)
    }
  }
  /** 获取教师列表 */
  const getTeacherData = async () => {
    const res = (await controllerTeacherBasicTeacherList({
      enterpriseId: schoolInfo?.id,
      enterpriseCode: schoolInfo?.code,
      sectionCode: schoolInfo?.section_code || undefined,
    })) as ResType<any>
    if (res?.errCode) {
      message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
    } else {
      const arr: any[] = []
      res?.list?.forEach((item: API.teacher) => {
        if (item.username) {
          arr.push({
            label: (
              <>
                {item.name}
                <span
                  style={{
                    color: '#999',
                  }}
                  className={styles.selectDesc}
                >
                  {item?.code}
                </span>
              </>
            ),
            detail: `${item.name}${item.code}`,
            // teacherName: item.name,
            userCode: item.code,
            value: item.name,
            username: item.username,
          })
        }
      })
      setTeacherList(arr)
    }
  }
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile)
    }
    setPreviewImage(file.url || (file.preview as string))
    setPreviewOpen(true)
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1))
  }
  useEffect(() => {
    if (modalVisible?.visible) {
      if (modalVisible?.data) {
        const { visitImg, end_time, start_time, ...rest } = modalVisible.data
        let trainImg
        if (visitImg) {
          trainImg = (visitImg.split(',') || [])?.map((item: any, index: number) => {
            return {
              uid: index,
              name: '家访照片' + index + 1,
              status: 'done',
              url: item,
              response: {
                data: {
                  id: getImageId(item),
                  url: item,
                },
              },
            }
          })
        }
        form.setFieldsValue({
          ...rest,
          start_time: dayjs(start_time, 'YYYY/MM/DD'),
          end_time: dayjs(end_time, 'YYYY/MM/DD'),
          visitImg: trainImg,
          endTime_time: dayjs(end_time)?.format('HH:mm'),
          startTime_time: dayjs(start_time)?.format('HH:mm'),
          studentCode: [
            {
              label: rest?.studentName,
              value: rest?.studentCode,
            },
          ],
        })
        if (modalVisible?.type) {
          setReadonly(true)
        } else {
          setReadonly(false)
        }
      } else {
        setReadonly(false)
      }
    }
  }, [modalVisible])

  useEffect(() => {
    if (schoolInfo) {
      // 学段名称
      const XDNameList = schoolInfo?.section_name?.split(',') || []
      ;(async () => {
        getTeacherData()
        getTermCode()
        // 获取年级数据
        const NjArr: screenType = []
        const result = (await controllerRepeatGetGrades({ section: XDNameList })) as ResType<any>
        if (result?.errCode) {
          message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
        } else {
          result?.list?.map((item: any) => {
            return NjArr.push({
              label: getNjName(item?.name, schoolInfo?.section_code),
              value: item?.name,
              code: item?.code,
            })
          })
          setNJData(NjArr)
        }
      })()
    }
  }, [schoolInfo])
  return (
    <>
      <DrawerForm<any>
        title={
          modalVisible?.data
            ? modalVisible?.type
              ? '补充家访记录'
              : '编辑家访登记'
            : '新增家访登记'
        }
        form={form}
        open={modalVisible?.visible}
        drawerProps={{
          destroyOnClose: true,
          className: 'trainModal',
        }}
        onOpenChange={(visible: boolean) => {
          if (!visible) {
            setModalVisible({
              visible,
            })
          }
          if (!visible) {
            form.resetFields()
          }
        }}
        submitter={{
          render: (props) => {
            return (
              <Space size="large">
                <Button
                  key="cancel"
                  type="dashed"
                  onClick={() => {
                    form.resetFields()
                    setModalVisible({ visible: false })
                  }}
                >
                  取消
                </Button>
                {!modalVisible?.type && (
                  <Button
                    key="ok"
                    type="primary"
                    onClick={() => {
                      setIsDraft(true)
                      props.submit()
                    }}
                  >
                    保存为草稿
                  </Button>
                )}

                {modalVisible?.data ? (
                  modalVisible?.type ? (
                    <>
                      <Button
                        key="ok"
                        type="primary"
                        onClick={() => {
                          setIsDraft(null)
                          props.submit()
                        }}
                      >
                        保存
                      </Button>
                      <Button
                        key="ok"
                        type="primary"
                        onClick={() => {
                          setIsDraft(false)
                          props.submit()
                        }}
                      >
                        提交
                      </Button>
                    </>
                  ) : (
                    <Button
                      key="ok"
                      type="primary"
                      onClick={() => {
                        setIsDraft(false)
                        props.submit()
                      }}
                    >
                      保存并提交
                    </Button>
                  )
                ) : (
                  <Button
                    key="ok"
                    type="primary"
                    onClick={() => {
                      setIsDraft(false)
                      props.submit()
                    }}
                  >
                    保存并提交
                  </Button>
                )}
              </Space>
            )
          },
        }}
        initialValues={{
          teacher1_userCode: currentUser?.userCode,
          teacher1_username: currentUser?.username,
          teacher1_realName: currentUser?.realName,
          teacherCode: currentUser?.userCode,
          enterpriseCode: schoolInfo?.code,
          enterpriseName: schoolInfo?.name,
          visitDate: dayjs().format('YYYY/MM/DD'),
        }}
        grid
        labelCol={isMobile ? {} : { flex: '6.5em' }}
        labelWrap={true}
        wrapperCol={isMobile ? {} : { flex: 'auto' }}
        layout={isMobile ? 'vertical' : 'horizontal'}
        onFinish={onFinish}
      >
        <ProFormText name="id" hidden />
        <ProFormText name="teacherCode" hidden />
        <ProFormText name="teacher1_userCode" hidden />
        <ProFormText name="teacher1_username" hidden />
        <ProFormText name="teacher2_userCode" hidden />
        <ProFormText name="teacher2_username" hidden />
        <ProFormText name="studentName" hidden />
        <ProFormText name="enterpriseCode" hidden />
        <ProFormText name="enterpriseName" hidden />
        <ProFormText name="gradeCode" hidden />
        <ProFormText name="classCode" hidden />
        <ProFormDatePicker
          name="start_time"
          label="开始日期"
          placeholder="请选择家访时间"
          colProps={{ span: 12 }}
          readonly={readonly}
          fieldProps={{
            style: { width: '100%' },
            format: 'YYYY/MM/DD',
            inputReadOnly: true,
            disabledDate: (current) => {
              // 指定为今天可选 return current && current.format('YYYY/MM/DD') !== dayjs().format('YYYY/MM/DD')
              // 指定为今天以及今天之后可选
              return current < dayjs().endOf('day').add(-1, 'day')
            },
          }}
          rules={[{ required: true, message: '请选择家访时间' }]}
        />
        <ProFormTimePicker
          name="startTime_time"
          label="开始时间"
          placeholder="请选择开始时间"
          readonly={readonly}
          fieldProps={{
            style: { width: '100%' },
            format: 'HH:mm',
            inputReadOnly: true,
            minuteStep: 5,
            disabledTime: () => {
              return { disabledHours: () => [0, 1, 2, 3, 4, 5, 6, 21, 22, 23] }
            },
            hideDisabledOptions: true,
          }}
          colProps={{ span: 12 }}
          rules={[{ required: true, message: '请选择开始时间' }]}
        />

        <ProFormDatePicker
          name="end_time"
          label="结束日期"
          readonly={readonly}
          placeholder="请选择家访时间"
          colProps={{ span: 12 }}
          fieldProps={{
            style: { width: '100%' },
            format: 'YYYY/MM/DD',
            inputReadOnly: true,
            disabledDate: (current) => {
              return current < dayjs().endOf('day').add(-1, 'day')
            },
          }}
          rules={[{ required: true, message: '请选择家访时间' }]}
        />

        <ProFormTimePicker
          name="endTime_time"
          label="结束时间"
          readonly={readonly}
          placeholder="请选择开始时间"
          fieldProps={{
            style: { width: '100%' },
            format: 'HH:mm',
            inputReadOnly: true,
            minuteStep: 5,
            disabledTime: () => {
              return { disabledHours: () => [0, 1, 2, 3, 4, 5, 6, 21, 22, 23] }
            },
            hideDisabledOptions: true,
          }}
          colProps={{ span: 12 }}
          rules={[{ required: true, message: '请选择结束时间' }]}
        />
        <ProFormText name="teacher1_realName" readonly label="家访教师1" colProps={{ span: 12 }} />
        <ProFormSelect
          name="teacher2_realName"
          label="家访教师2"
          readonly={readonly}
          showSearch
          colProps={{ span: 12 }}
          fieldProps={{
            options: teacherList,
            filterOption: (inputValue, option) => option?.detail?.includes(inputValue),
            allowClear: false,
            onChange: (value: any, option: any) => {
              form.setFieldsValue({
                teacher2_userCode: option?.userCode,
                teacher2_username: option?.username,
              })
            },
          }}
          rules={[{ required: true, message: '请选择家访教师' }]}
        />

        <ProFormSelect
          colProps={{ span: 12 }}
          readonly={readonly}
          name="gradeName"
          label="年级"
          rules={[{ required: true, message: '请选择年级' }]}
          fieldProps={{
            options: NJData,
            allowClear: false,
            onChange: (value: any, option: any) => {
              setBJData([])
              form.setFieldsValue({
                gradeCode: option?.code,
                className: undefined,
                classCode: undefined,
                studentCode: undefined,
                parentPhone: undefined,
              })
              getClassData(option?.code)
            },
          }}
        />
        <ProFormSelect
          name="className"
          label="班级"
          readonly={readonly}
          colProps={{ span: 12 }}
          rules={[{ required: true, message: '请选择班级' }]}
          fieldProps={{
            options: BJData,
            allowClear: false,
            onChange: (value: any, option: any) => {
              form.setFieldsValue({
                classCode: option?.code,
                studentCode: undefined,
                parentPhone: undefined,
              })
            },
          }}
        />
        <ProFormDependency name={['gradeName', 'gradeCode', 'className', 'classCode']}>
          {({ gradeName, gradeCode, className, classCode }) => {
            return (
              <ProFormSelect
                showSearch
                name="studentCode"
                label="学生姓名"
                readonly={readonly}
                colProps={{ span: 12 }}
                rules={[{ required: true, message: '请选择学生' }]}
                fieldProps={{
                  onChange: (value: any, option: any) => {
                    form.setFieldsValue({
                      studentName: option?.title,
                    })
                  },
                }}
                debounceTime={300}
                params={[gradeName, gradeCode, className, classCode]}
                request={async ({ keyWords }) => {
                  if (gradeCode && classCode) {
                    const res = await controllerRepeatGetStudents({
                      grade: gradeCode,
                      classCode: classCode,
                      name: keyWords,
                      enterpriseId: schoolInfo?.id || '',
                    })
                    if (res?.errCode) {
                      message.error(res?.msg || '数据获取失败，请联系管理员或稍后再试！')
                      return []
                    } else {
                      const arr = res?.list?.map((item: any) => {
                        return {
                          value: item?.id,
                          label: item?.name,
                          ...item,
                        }
                      })
                      return arr
                    }
                  } else {
                    return []
                  }
                }}
              />
            )
          }}
        </ProFormDependency>

        {/* <ProFormText
          name="parentPhone"
          label="家长电话"
          readonly={readonly}
          colProps={{ span: 12 }}
          rules={[
            {
              required: true,
              message: '请输入手机号码!',
            },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '手机号码格式不正确!',
            },
          ]}
        /> */}
        {/* <ProFormText
          name="visitAddress"
          label="家访地点"
          fieldProps={{
            maxLength: 25,
            showCount: true,
          }}
        /> */}
        {modalVisible?.type && (
          <ProFormTextArea
            label="家访记录"
            name="visitRecord"
            placeholder={'家访记录最多不可超过500个字符'}
            fieldProps={{
              maxLength: 500,
              autoSize: {
                maxRows: 5,
                minRows: 3,
              },
              showCount: true,
            }}
          />
        )}
        {modalVisible?.type && (
          <ProFormUploadButton
            name="visitImg"
            listType="picture-card"
            accept="image/*"
            action={`/edu_api/upload/uploadSingle?subPath=tourImg/${schoolInfo?.code}`}
            getValueFromEvent={(e: any) => {
              console.log('Upload event:', e)
              if (Array.isArray(e)) {
                return e
              }
              return e?.fileList
            }}
            max={2}
            title="点击上传"
            fieldProps={{
              maxCount: 2,
              onRemove: async (file) => {
                if (file?.response?.data?.id) {
                  const res = await controllerUploadReplace({ id: file.response.data.id })
                  if (res?.errCode) {
                    message.error('删除失败，请联系管理员或稍后再试')
                    return false
                  }
                }
                return true
              },
              onPreview: (file) => {
                handlePreview(file)
              },
              beforeUpload: (file) => {
                const isLt10M = file.size / 1024 / 1024 < 2
                if (!isLt10M) {
                  message.warning('图片大小不应超过2M!')
                }
                return isLt10M || Upload.LIST_IGNORE
              },
            }}
            label={'家访照片'}
          />
        )}
        <Modal
          open={previewOpen}
          title={previewTitle}
          footer={null}
          onCancel={() => setPreviewOpen(false)}
        >
          <img alt="example" style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </DrawerForm>
    </>
  )
}

export default ApplyForm
