import type { WhetherJoinEnum } from '@/pages/EducationaManagement/AfterServiceStatistics/columns'
import { request } from 'umi'

/** 查询列表  GET /after_class_detail */
export async function getAfterServiceStatistics(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: [] }>>('/after_class_detail', {
    method: 'GET',
    params,
  })
}

/** 修改  PUT /after_class_detail/${id} */
export async function updateAfterServiceStatistics(
  id: string,
  body: {
    /** 是否参与课后服务状态 */
    whetherJoin: Omit<WhetherJoinEnum, '未确认'>
  },
) {
  return request<API.ResType<unknown>>(`/after_class_detail/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  })
}

/** 获取上月配置并应用到当月 GET /after_class_detail/updateCurrMonthData  */
export async function getUpdateCurrMonthData(params: {
  enterpriseCode: string
  semesterCode: string
  gradeCode: string
  classCode: string
  year: string
  month: string
  memberId?: string
}) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    '/after_class_detail/updateCurrMonthData',
    {
      method: 'GET',
      params,
    },
  )
}
/**
 * 获取上月配置并应用到当月
 * @param params 请求参数
 * @param params.enterpriseCode 企业代码
 * @param params.semesterCode 学期代码
 * @param params.memberId 教师ID
 * @returns 返回包含总数和列表的响应数据
 */
export async function manualSync(params: {
  enterpriseCode: string
  semesterCode: string
  memberId: string
}) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    '/after_class_detail/syncStudentData',
    {
      method: 'GET',
      params,
    },
  )
}
