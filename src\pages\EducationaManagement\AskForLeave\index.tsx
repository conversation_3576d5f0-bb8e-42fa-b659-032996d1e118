/*
 * @Author: wuzhan <EMAIL>
 * @Date: 2022-06-14 17:11:11
 * @LastEditors: SissleLynn
 * @LastEditTime: 2024-02-29 12:04:08
 * @FilePath: \edu_platform_web\src\pages\AttendanceManagement\OnlineLeave\index.tsx
 * @Description: 考勤打卡
 */
import React, { useEffect, useState } from 'react'
import { useAccess } from 'umi'
import type { JumpCardProp } from './components/JumpCard'
import JumpCard from './components/JumpCard'
import tLeave from '@/assets/qingJia/tLeave.png'
import tLeaveAudit from '@/assets/qingJia/tLeaveAudit.png'
import sLeaveAudit from '@/assets/qingJia/sLeaveAudit.png'
import finishAudit from '@/assets/qingJia/tFinishAudit.png'
import tQuery from '@/assets/qingJia/tQuery.png'
import sQuery from '@/assets/qingJia/sQuery.png'
import setting from '@/assets/qingJia/setting.png'
import styles from './index.less'
import { envjudge, getBuildOptions } from '@/utils'
import ExportRecord from '@/components/ExportRecord'
import { Divider } from 'antd'

type DataType = {
  id: string
  title: string
  subTitle: string
  img: any
  bgcolor: string
  url: string
}
const AskForLeave = () => {
  const { isAdministration } = useAccess()
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  const [dataSource_sq, setDataSource_sq] = useState<DataType[]>([])
  const [dataSource_sh, setDataSource_sh] = useState<DataType[]>([])
  const [dataSource_cx, setDataSource_cx] = useState<DataType[]>([])
  const [dataSource_gl, setDataSource_gl] = useState<DataType[]>([])
  const getData = async () => {
    const { askforLeaveHost } = await getBuildOptions()
    const arr_sq: DataType[] = [
      {
        id: '1',
        title: '教师请销假',
        subTitle: '',
        img: tLeave,
        bgcolor: '#FF9800',
        url: askforLeaveHost + '/teacher/applyList',
      },
    ]
    const arr_sh: DataType[] = [
      {
        id: 'auditStudent',
        title: '学生请假审批',
        subTitle: '学生请假审批',
        url: askforLeaveHost + '/student/auditList',
        img: sLeaveAudit,
        bgcolor: '#2196F3',
      },
    ]
    const arr_cx: DataType[] = []
    const arr_gl: DataType[] = []
    if (isAdministration(ENV_QJGL, ENV_GL)) {
      arr_cx.push({
        id: '5',
        title: '学生请假查询',
        subTitle: '',
        url: askforLeaveHost + '/student/alllist ',
        img: sQuery,
        bgcolor: '#4CAF50',
      })
      arr_cx.push({
        id: '6',
        title: '教师请假查询',
        subTitle: '',
        url: askforLeaveHost + '/teacher/alllist',
        img: tQuery,
        bgcolor: '#FF5722',
      })
      arr_sh.push({
        id: '2',
        title: '教师请假审批',
        subTitle: '',
        url: askforLeaveHost + '/teacher/auditList',
        img: tLeaveAudit,
        bgcolor: '#4CAF50',
      })
      arr_sh.push({
        id: 'finishList',
        title: '教师销假审批',
        subTitle: '',
        url: askforLeaveHost + '/teacher/finishList',
        img: finishAudit,
        bgcolor: '#FF5722',
      })
      arr_gl.push({
        id: 'configuration',
        title: '请假类别设置',
        subTitle: '',
        url: askforLeaveHost + '/admin/configuration',
        img: setting,
        bgcolor: '#009688',
      })
      // arr_gl.push({
      //   id: 'infection',
      //   title: '请假类别设置',
      //   subTitle: '',
      //   url: askforLeaveHost + '/admin/infection',
      //   img: setting,
      //   bgcolor: '#4CAF50',
      // })
    }
    setDataSource_sq(arr_sq)
    setDataSource_sh(arr_sh)
    setDataSource_cx(arr_cx)
    setDataSource_gl(arr_gl)
  }

  useEffect(() => {
    getData()
  }, [])
  return (
    <div className={styles.askForLeave}>
      {isAdministration(ENV_QJGL, ENV_GL) && !isMobile && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginBottom: 24,
          }}
        >
          <ExportRecord type="教师请假" title="导出教师请假记录" />
          <span
            style={{
              display: 'block',
              width: 18,
            }}
          />
          <ExportRecord type="学生请假" title="导出学生请假记录" />
        </div>
      )}
      <div>
        {!!dataSource_sq.length && <Divider orientation="left">申请</Divider>}
        {dataSource_sq.map((item: JumpCardProp) => (
          <JumpCard key={item.id} {...item} />
        ))}
        {!!dataSource_sh.length && <Divider orientation="left">审核</Divider>}
        {dataSource_sh.map((item: JumpCardProp) => (
          <JumpCard key={item.id} {...item} />
        ))}
        {!!dataSource_cx.length && <Divider orientation="left">查询</Divider>}
        {dataSource_cx.map((item: JumpCardProp) => (
          <JumpCard key={item.id} {...item} />
        ))}
        {!!dataSource_gl.length && <Divider orientation="left">管理</Divider>}
        {dataSource_gl.map((item: JumpCardProp) => (
          <JumpCard key={item.id} {...item} />
        ))}
      </div>
    </div>
  )
}

export default AskForLeave
