import type { ProColumns } from '@ant-design/pro-components'
import { But<PERSON>, Divider, Popconfirm, Space } from 'antd'
import moment from 'moment'
import { history } from 'umi'
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const getSchoolColums = ({
  type,
  hanleAction,
}: {
  type: string
  hanleAction?: (type: string, data: any) => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
  },
  {
    title: '筛查名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    align: 'center',
    width: 150,
  },
  {
    title: '检查时间',
    dataIndex: 'screen_time',
    key: 'screen_time',
    valueType: 'date',
    align: 'center',
    width: 80,
  },
  {
    title: '描述',
    dataIndex: 'describe',
    key: 'describe',
    align: 'center',
    ellipsis: true,
    width: 150,
  },
  {
    title: '检查学生数',
    ellipsis: true,
    dataIndex: 'studentCount',
    key: 'studentCount',
    align: 'center',
    width: 70,
  },
  {
    title: '数据更新时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    valueType: 'dateTime',
    align: 'center',
    width: 100,
  },
  {
    title: '最后分析时间',
    dataIndex: 'analysisTime',
    key: 'analysisTime',
    valueType: 'dateTime',
    align: 'center',
    width: 100,
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 200,
    hideInTable: type !== 'record',
    fixed: 'right',
    render: (_, record: any) => {
      return (
        <>
          <a
            key="look"
            onClick={() => {
              hanleAction?.('loadData', record)
            }}
          >
            数据管理
          </a>
          <Divider type="vertical" />
          {record?.studentCount > 0 && (
            <>
              <a
                key="look"
                onClick={() => {
                  hanleAction?.('analyse', record)
                }}
              >
                开始分析
              </a>
              <Divider type="vertical" />
            </>
          )}
          <a
            key="edit"
            onClick={() => {
              hanleAction?.('edit', record)
            }}
          >
            编辑
          </a>
          <Divider type="vertical" />
          <Popconfirm
            key="delete"
            title="删除之后，数据不可恢复，确定要删除吗?"
            onConfirm={async () => {
              hanleAction?.('delete', record)
            }}
            okText="确定"
            cancelText="取消"
            placement="topLeft"
          >
            <a>删除 </a>
          </Popconfirm>
        </>
      )
    },
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 100,
    hideInTable: type === 'record',
    fixed: 'right',
    render: (text, record) => [
      <Button
        type="link"
        size="small"
        key="result"
        onClick={() => {
          history.push({
            pathname:
              type === 'eyeAnalyse'
                ? `/archives/studentGrowthPlan/dataAnalyse/dataStatistic`
                : `/archives/studentGrowthPlan/physiqueAnalyse/dataStatistic`,
            state: { detail: record },
          })
        }}
      >
        详情
      </Button>,
      <a key="export" href={`/edu_api/download/student_screen_analysis/grade_list/${record?.id}`}>
        导出
      </a>,
    ],
  },
]

const getSchoolMetas = ({ type }: { type: string; onExport?: () => void }): any => {
  return {
    title: {
      dataIndex: 'name',
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>检查人数：{entity?.studentCount}</p>
            <p style={styleP}>检查时间：{entity?.screen_time}</p>
            <p style={styleP}>
              数据更新时间：{moment(entity?.updatedAt).format('YYYY-MM-DD HH:mm')}
            </p>
            <p style={styleP}>
              最后分析时间：{moment(entity?.updatedAt).format('YYYY-MM-DD HH:mm')}
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (_: any, row: any) => {
        return (
          <Space>
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                history.push({
                  pathname:
                    type === 'eyeAnalyse'
                      ? `/archives/studentGrowthPlan/dataAnalyse/dataStatistic`
                      : `/archives/studentGrowthPlan/physiqueAnalyse/dataStatistic`,
                  state: { detail: row },
                })
              }}
            >
              详情
            </Button>
          </Space>
        )
      },
    },
  }
}
const getGradeColums = ({
  screen_id,
  detail,
}: {
  screen_id?: string
  detail?: any
  onExport?: () => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '年级',
    dataIndex: 'gradeName',
    key: 'gradeName',
    ellipsis: true,
    align: 'center',
    width: 120,
    fixed: 'left',
  },
  {
    title: '体检总人数',
    dataIndex: 'zrs',
    key: 'zrs',
    align: 'center',
    width: 90,
  },
  {
    title: '视力正常人数',
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '视力正常率',
    ellipsis: true,
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slzcrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '视力不良人数',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 100,
  },
  {
    title: '视力不良率',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slblrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '其中',
    children: [
      {
        title: '男生总人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '男生不良人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '男生不良率',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 90,
        render: (_, record: any) => {
          if (record?.boyrs) {
            return ((record?.boyblrs / record?.boyrs) * 100).toFixed(2) + '%'
          } else {
            return '--'
          }
        },
      },
      {
        title: '女生总人数',
        dataIndex: 'girlrs',
        key: 'girlrs',
        align: 'center',
        width: 80,
      },
      {
        title: '女生不良人数',
        dataIndex: 'girlblrs',
        key: 'girlblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '女生不良率',
        dataIndex: 'girlblrs',
        key: 'girlblrs',
        align: 'center',
        width: 90,
        render: (_, record: any) => {
          console.log(screen_id);

          if (record?.girlrs) {
            return ((record?.girlblrs / record?.girlrs) * 100).toFixed(2) + '%'
          } else {
            return '--'
          }
        },
      },
    ],
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 100,
    fixed: 'right',
    render: (text, record) => [
      <Button
        type="link"
        size="small"
        key="result"
        onClick={() => {
          history.push({
            pathname: `/archives/studentGrowthPlan/dataAnalyse/gradeStatistic`,
            state: {
              detail: {
                ...detail,
                gradeCode: record?.gradeCode,
                gradeName: record?.gradeName,
              },
            },
          })
        }}
      >
        详情
      </Button>,
      <a
        key="export"
        href={`/edu_api/download/student_screen_analysis/class_list/${screen_id}?gradeCode=${record?.gradeCode}`}
      >
        导出
      </a>,
    ],
  },
]

const getGradeMetas = ({ detail, onExport }: { detail?: any; onExport?: () => void }): any => {
  return {
    title: {
      dataIndex: 'gradeName',
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>检查人数：{entity?.zrs}</p>
            <p style={styleP}>
              视力正常人数：{entity?.slzcrs}（
              {((entity?.slzcrs / entity?.zrs) * 100).toFixed(2) + '%'}）
            </p>
            <p style={styleP}>
              视力不良人数：{entity?.slblrs}（
              {((entity?.slblrs / entity?.zrs) * 100).toFixed(2) + '%'}）
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (_: any, row: any) => {
        return (
          <Space>
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                history.push({
                  pathname: `/archives/studentGrowthPlan/dataAnalyse/gradeStatistic`,
                  state: {
                    detail: {
                      ...detail,
                      gradeCode: row?.gradeCode,
                      gradeName: row?.gradeName,
                    },
                  },
                })
              }}
            >
              详情
            </Button>
            {/* <Button
              type="link"
              size="small"
              key="export"
              onClick={() => {
                onExport?.()
              }}
            >
              导出
            </Button> */}
          </Space>
        )
      },
    },
  }
}
const getPhyGradeColums = ({
  detail,
  onExport,
}: {
  detail?: any
  onExport?: () => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '年级',
    dataIndex: 'gradeName',
    key: 'gradeName',
    ellipsis: true,
    align: 'center',
    width: 120,
    fixed: 'left',
  },
  {
    title: '体检总人数',
    dataIndex: 'zrs',
    key: 'zrs',
    align: 'center',
    width: 90,
  },
  {
    title: '及格人数',
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '及格率',
    ellipsis: true,
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slzcrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '不及格人数',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 100,
  },
  {
    title: '不及格率',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slblrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '体重',
    children: [
      {
        title: '正常人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '超重人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '低体重人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '肺活量',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '坐位体前屈',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '50米x8往返',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '一分钟仰卧起坐',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '一分钟跳绳',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 100,
    fixed: 'right',
    render: (text, record) => [
      <Button
        type="link"
        size="small"
        key="result"
        onClick={() => {
          history.push({
            pathname: `/archives/studentGrowthPlan/physiqueAnalyse/gradeStatistic`,
            state: {
              detail: {
                ...detail,
                gradeCode: record?.gradeCode,
                gradeName: record?.gradeName,
              },
            },
          })
        }}
      >
        详情
      </Button>,
      // <Button
      //   type="link"
      //   size="small"
      //   key="export"
      //   onClick={() => {
      //     onExport?.()
      //   }}
      // >
      //   导出
      // </Button>
    ],
  },
]

const getPhyGradeMetas = ({ detail, onExport }: { detail?: any; onExport?: () => void }): any => {
  return {
    title: {
      dataIndex: 'gradeName',
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>检查人数：{entity?.zrs}</p>
            <p style={styleP}>
              及格人数：{entity?.slzcrs}（{((entity?.slzcrs / entity?.zrs) * 100).toFixed(2) + '%'}
              ）
            </p>
            <p style={styleP}>
              不及格人数：{entity?.slblrs}（
              {((entity?.slblrs / entity?.zrs) * 100).toFixed(2) + '%'}）
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (_: any, row: any) => {
        return (
          <Space>
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                history.push({
                  pathname: `/archives/studentGrowthPlan/physiqueAnalyse/gradeStatistic`,
                  state: {
                    detail: {
                      ...detail,
                      gradeCode: row?.gradeCode,
                      gradeName: row?.gradeName,
                    },
                  },
                })
              }}
            >
              详情
            </Button>
            {/* <Button
              type="link"
              size="small"
              key="export"
              onClick={() => {
                onExport?.()
              }}
            >
              导出
            </Button> */}
          </Space>
        )
      },
    },
  }
}
const getClassColums = ({
  detail,
  onExport,
}: {
  detail?: any
  onExport?: () => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '班级',
    dataIndex: 'className',
    key: 'className',
    ellipsis: true,
    align: 'center',
    width: 120,
    fixed: 'left',
    render: (_, record) => {
      return record?.gradeName + record?.className
    },
  },
  {
    title: '体检总人数',
    dataIndex: 'zrs',
    key: 'zrs',
    align: 'center',
    width: 90,
  },
  {
    title: '视力正常人数',
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '视力正常率',
    ellipsis: true,
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slzcrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '视力不良人数',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 100,
  },
  {
    title: '视力不良率',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slblrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '其中',
    children: [
      {
        title: '男生总人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '男生不良人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '男生不良率',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 90,
        render: (_, record: any) => {
          if (record?.boyrs) {
            return ((record?.boyblrs / record?.boyrs) * 100).toFixed(2) + '%'
          } else {
            return '--'
          }
        },
      },
      {
        title: '女生总人数',
        dataIndex: 'girlrs',
        key: 'girlrs',
        align: 'center',
        width: 80,
      },
      {
        title: '女生不良人数',
        dataIndex: 'girlblrs',
        key: 'girlblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '女生不良率',
        dataIndex: 'girlblrs',
        key: 'girlblrs',
        align: 'center',
        width: 90,
        render: (_, record: any) => {
          if (record?.girlrs) {
            return ((record?.girlblrs / record?.girlrs) * 100).toFixed(2) + '%'
          } else {
            return '--'
          }
        },
      },
    ],
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 100,
    fixed: 'right',
    render: (text, record) => [
      <Button
        type="link"
        size="small"
        key="result"
        onClick={() => {
          history.push({
            pathname: `/archives/studentGrowthPlan/dataAnalyse/classStatistic`,
            state: {
              detail: {
                ...detail,
                classCode: record?.classCode,
                className: record?.className,
              },
            },
          })
        }}
      >
        详情
      </Button>,
      // <Button
      //   type="link"
      //   size="small"
      //   key="export"
      //   onClick={() => {
      //     onExport?.()
      //   }}
      // >
      //   导出
      // </Button>
    ],
  },
]

const getClassMetas = ({ detail, onExport }: { detail?: any; onExport?: () => void }): any => {
  return {
    title: {
      dataIndex: 'className',
      render: (dom: React.ReactNode, entity: any) => {
        return entity?.gradeName + entity?.className
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>检查人数：{entity?.zrs}</p>
            <p style={styleP}>
              视力正常人数：{entity?.slzcrs}（
              {((entity?.slzcrs / entity?.zrs) * 100).toFixed(2) + '%'}）
            </p>
            <p style={styleP}>
              视力不良人数：{entity?.slblrs}（
              {((entity?.slblrs / entity?.zrs) * 100).toFixed(2) + '%'}）
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (_: any, row: any) => {
        return (
          <Space>
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                history.push({
                  pathname: `/archives/studentGrowthPlan/dataAnalyse/classStatistic`,
                  state: {
                    detail: {
                      ...detail,
                      classCode: row?.classCode,
                      className: row?.className,
                    },
                  },
                })
              }}
            >
              详情
            </Button>
            {/*
            <Button
              type="link"
              size="small"
              key="export"
              onClick={() => {
                onExport?.()
              }}
            >
              导出
            </Button> */}
          </Space>
        )
      },
    },
  }
}
const getPhyClassColums = ({
  detail,
  onExport,
}: {
  detail?: any
  onExport?: () => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '班级',
    dataIndex: 'className',
    key: 'className',
    ellipsis: true,
    align: 'center',
    width: 120,
    fixed: 'left',
    render: (_, record) => {
      return record?.gradeName + record?.className
    },
  },
  {
    title: '体检总人数',
    dataIndex: 'zrs',
    key: 'zrs',
    align: 'center',
    width: 90,
  },
  {
    title: '及格人数',
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '及格率',
    ellipsis: true,
    dataIndex: 'slzcrs',
    key: 'slzcrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slzcrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '不及格人数',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 100,
  },
  {
    title: '不及格率',
    dataIndex: 'slblrs',
    key: 'slblrs',
    align: 'center',
    width: 90,
    render: (_, record: any) => {
      if (record?.zrs) {
        return ((record?.slblrs / record?.zrs) * 100).toFixed(2) + '%'
      } else {
        return '--'
      }
    },
  },
  {
    title: '体重',
    children: [
      {
        title: '正常人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '超重人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '低体重人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '肺活量',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '坐位体前屈',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '50米x8往返',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '一分钟仰卧起坐',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '一分钟跳绳',
    children: [
      {
        title: '优秀人数',
        dataIndex: 'boyrs',
        key: 'boyrs',
        align: 'center',
        width: 80,
      },
      {
        title: '良好人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
      {
        title: '不及格人数',
        dataIndex: 'boyblrs',
        key: 'boyblrs',
        align: 'center',
        width: 100,
      },
    ],
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 100,
    fixed: 'right',
    render: (text, record) => [
      <Button
        type="link"
        size="small"
        key="result"
        onClick={() => {
          history.push({
            pathname: `/archives/studentGrowthPlan/dataAnalyse/gradeStatistic`,
            state: {
              detail: {
                ...detail,
                gradeCode: record?.gradeCode,
                gradeName: record?.gradeName,
              },
            },
          })
        }}
      >
        详情
      </Button>,
      // <Button
      //   type="link"
      //   size="small"
      //   key="export"
      //   onClick={() => {
      //     onExport?.()
      //   }}
      // >
      //   导出
      // </Button>
    ],
  },
]

const getPhyClassMetas = ({ detail, onExport }: { detail?: any; onExport?: () => void }): any => {
  return {
    title: {
      dataIndex: 'className',
      render: (dom: React.ReactNode, entity: any) => {
        return entity?.gradeName + entity?.className
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>检查人数：{entity?.zrs}</p>
            <p style={styleP}>
              及格人数：{entity?.slzcrs}（{((entity?.slzcrs / entity?.zrs) * 100).toFixed(2) + '%'}
              ）
            </p>
            <p style={styleP}>
              不及格人数：{entity?.slblrs}（
              {((entity?.slblrs / entity?.zrs) * 100).toFixed(2) + '%'}）
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (_: any, row: any) => {
        return (
          <Space>
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                history.push({
                  pathname: `/archives/studentGrowthPlan/dataAnalyse/gradeStatistic`,
                  state: {
                    detail: {
                      ...detail,
                      gradeCode: row?.gradeCode,
                      gradeName: row?.gradeName,
                    },
                  },
                })
              }}
            >
              详情
            </Button>
            {/* <Button
              type="link"
              size="small"
              key="export"
              onClick={() => {
                onExport?.()
              }}
            >
              导出
            </Button> */}
          </Space>
        )
      },
    },
  }
}
const getStudentColums = ({
  detail,
  onExport,
}: {
  detail?: any
  onExport?: () => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '姓名',
    dataIndex: 'studentName',
    key: 'studentName',
    ellipsis: true,
    align: 'center',
    width: 100,
    fixed: 'left',
  },
  {
    title: '学号',
    dataIndex: 'studentCode',
    key: 'studentCode',
    align: 'center',
    width: 100,
  },
  {
    title: '性别',
    dataIndex: 'gender',
    key: 'gender',
    ellipsis: true,
    align: 'center',
    width: 50,
  },
  {
    title: '右眼',
    children: [
      {
        title: '裸眼视力',
        dataIndex: 'yylysl',
        key: 'yylysl',
        width: 60,
        align: 'center',
        render: (_, record) => {
          return record?.yylysl !== undefined ? record?.yylysl?.toFixed(1) : '-'
        },
      },
      {
        title: '屈光球镜S',
        dataIndex: 'yyqgqjs',
        key: 'yyqgqjs',
        width: 65,
        align: 'center',
        render: (_, record) => {
          return record?.yyqgqjs !== undefined ? record?.yyqgqjs?.toFixed(2) : '-'
        },
      },
      {
        title: '屈光柱镜C',
        dataIndex: 'yyqgzjc',
        key: 'yyqgqjc',
        width: 65,
        align: 'center',
        render: (_, record) => {
          return record?.yyqgzjc !== undefined ? record?.yyqgzjc?.toFixed(2) : '-'
        },
      },
      {
        title: '屈光轴位A',
        dataIndex: 'yyqgzwa',
        key: 'yyqgqja',
        width: 65,
        align: 'center',
      },
    ],
  },
  {
    title: '左眼',
    children: [
      {
        title: '裸眼视力',
        dataIndex: 'zylysl',
        key: 'zylysl',
        width: 60,
        align: 'center',
        render: (_, record) => {
          return record?.zylysl !== undefined ? record?.zylysl?.toFixed(1) : '-'
        },
      },
      {
        title: '屈光球镜S',
        dataIndex: 'zyqgqjs',
        key: 'zyqgqjs',
        width: 65,
        align: 'center',
        render: (_, record) => {
          return record?.zyqgqjs !== undefined ? record?.zyqgqjs?.toFixed(2) : '-'
        },
      },
      {
        title: '屈光柱镜C',
        dataIndex: 'zyqgzjc',
        key: 'zyqgqjc',
        width: 65,
        align: 'center',
        render: (_, record) => {
          return record?.zyqgzjc !== undefined ? record?.zyqgzjc?.toFixed(2) : '-'
        },
      },
      {
        title: '屈光轴位A',
        dataIndex: 'zyqgzwa',
        key: 'zyqgqja',
        width: 65,
        align: 'center',
      },
    ],
  },
  {
    title: (
      <div>
        是否为角膜塑形镜
        <br />
        （OK镜）佩戴者
      </div>
    ),
    dataIndex: 'is_yjmsxj',
    key: 'is_yjmsxj',
    ellipsis: true,
    align: 'center',
    width: 95,
    render: (_, record) => {
      return record.is_yjmsxj ? '是' : '否'
    },
  },
  {
    title: '操作',
    valueType: 'option',
    align: 'center',
    width: 100,
    fixed: 'right',
    render: (text, record) => [
      <Button
        type="link"
        size="small"
        key="result"
        onClick={() => {
          history.push(
            `/archives/studentGrowthPlan/dataAnalyse/personalAnalyse?studentCode=${record?.studentCode}`,
          )
        }}
      >
        详情
      </Button>,
      // <Button
      //   type="link"
      //   size="small"
      //   key="export"
      //   onClick={() => {
      //     onExport?.()
      //   }}
      // >
      //   导出
      // </Button>,
    ],
  },
]

const getStudentMetas = ({ detail, onExport }: { detail?: any; onExport?: () => void }): any => {
  return {
    title: {
      dataIndex: 'studentName',
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>左眼裸眼视力：{entity?.zylysl?.toFixed(1)}</p>
            <p style={styleP}>右眼裸眼视力：{entity?.yylysl?.toFixed(1)}</p>
          </div>
        )
      },
    },
    actions: {
      render: (
        _: any,
        row: {
          studentCode: any
        },
      ) => {
        return (
          <Space>
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                history.push(
                  `/archives/studentGrowthPlan/dataAnalyse/personalAnalyse?studentCode=${row?.studentCode}`,
                )
              }}
            >
              详情
            </Button>
            {/* <Button
              type="link"
              size="small"
              key="export"
              onClick={() => {
                onExport?.()
              }}
            >
              导出
            </Button> */}
          </Space>
        )
      },
    },
  }
}
export {
  getSchoolColums,
  getSchoolMetas,
  getGradeColums,
  getGradeMetas,
  getPhyGradeColums,
  getPhyGradeMetas,
  getClassColums,
  getClassMetas,
  getPhyClassColums,
  getPhyClassMetas,
  getStudentColums,
  getStudentMetas,
}
