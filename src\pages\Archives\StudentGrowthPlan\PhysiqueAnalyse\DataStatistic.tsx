import React, { useRef, useState } from 'react'
import styles from './index.less'
import { Button, Col, Row } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import CommonList from '@/components/CommonList'
import { useHistory } from 'umi'
import { getGradeColums, getGradeMetas, getPhyGradeColums, getPhyGradeMetas } from '../getColums'
import { gradeAnalysis } from '@/services/edu-platform-web/growth'
import moment from 'moment'
import { envjudge } from '@/utils'
import ChartGroup from './ChartGroup'
import { floatBottomStyle } from '@/constant'

const DataStatistic = () => {
  const childRef = useRef()
  const { location } = useHistory()
  const env_screen = envjudge()
  const { detail } = (location?.state || {}) as any
  const [analysisData, setAnalysisData] = useState<any>()
  const [chartData,setChartData] = useState<any>()
  // 导出
  const onExport = async () => {}

  return (
    <div className={styles.dataStatistic}>
      {!env_screen.includes('mobile') && (
        <Button
          type="primary"
          style={{
            marginBlockEnd: 24,
          }}
          onClick={() => {
            history.go(-1)
          }}
        >
          <LeftOutlined />
          返回上一页
        </Button>
      )}
      <div className={styles.topBar}>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 24 }}>
          <Col xs={24} sm={24} md={12} lg={6}>
            <div>筛查名称：{detail?.name}</div>
          </Col>
          <Col xs={24} sm={12} md={12} lg={6}>
            <div>检查时间：{moment(detail?.screen_time).format('YYYY.MM.DD')}</div>
          </Col>
          {!env_screen.includes('mobile') && (
            <Col xs={24} sm={12} md={12} lg={6}>
              <div>数据更新时间：{moment(detail?.updatedAt).format('YYYY.MM.DD HH:mm')}</div>
            </Col>
          )}
          {!env_screen.includes('mobile') && (
            <Col xs={24} sm={24} md={12} lg={6}>
              <div>
                最后分析时间：
                {moment(detail?.analysisTime).format('YYYY.MM.DD HH:mm') || '暂未分析'}
              </div>
            </Col>
          )}
        </Row>
      </div>
      <div className={styles.middleBar}>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 24 }}>
          <Col xs={8} sm={8} md={8} lg={4}>
            <div
              style={{
                backgroundColor: 'rgba(255, 152, 0, 0.1)',
              }}
            >
              <h3 style={env_screen.includes('mobile') ? { fontSize: 18 } : {}}>{analysisData?.zrs}</h3>
              <p>体检总人数</p>
            </div>
          </Col>
          <Col xs={8} sm={8} md={8} lg={4}>
            <div
              style={{
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
              }}
            >
              <h3 style={env_screen.includes('mobile') ? { fontSize: 18 } : {}}>{analysisData?.slzcrs}</h3>
              <p>优秀人数</p>
            </div>
          </Col>
          <Col xs={8} sm={8} md={8} lg={4}>
            <div
              style={{
                backgroundColor: 'rgba(41, 121, 255, 0.1)',
              }}
            >
              <h3 style={env_screen.includes('mobile') ? { fontSize: 18 } : {}}>{analysisData?.slblrs}</h3>
              <p>良好人数</p>
            </div>
          </Col>
          <Col xs={8} sm={8} md={8} lg={4}>
            <div
              style={{
                backgroundColor: 'rgba(255, 87, 46, 0.1)',
              }}
            >
              <h3 style={env_screen.includes('mobile') ? { fontSize: 18 } : {}}>{analysisData?.bll}</h3>
              <p>及格人数</p>
            </div>
          </Col>
          <Col xs={8} sm={8} md={8} lg={4}>
            <div
              style={{
                backgroundColor: 'rgba(255, 174, 52, 0.1)',
              }}
            >
              <h3 style={env_screen.includes('mobile') ? { fontSize: 18 } : {}}>{analysisData?.boybll}</h3>
              <p>及格率</p>
            </div>
          </Col>
          <Col xs={8} sm={8} md={8} lg={4}>
            <div
              style={{
                backgroundColor: 'rgba(101, 67, 243, 0.1)',
              }}
            >
              <h3 style={env_screen.includes('mobile') ? { fontSize: 18 } : {}}>{analysisData?.girlbll}</h3>
              <p>不及格率</p>
            </div>
          </Col>
        </Row>
      </div>
      <div className={styles.bottomBar}>
        <CommonList
          ref={childRef}
          params={{
            detail,
          }}
          request={async (
            // 第一个参数 params 查询表单和 params 参数的结合
            // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
            params: any,
            sort,
            filter,
          ) => {
            // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
            // 如果需要转化参数可以在这里进行修改
            if (detail?.id) {
              const { errCode, data, analysis } = (await gradeAnalysis({
                /** 学校编码  */
                screen_id: detail.id,
                // offset: Number((page || 1) - 1) * Number(pageSize),
                // limit: Number(pageSize),
              })) as any
              const { zrs = 0, slzcrs = 0, slblrs = 0, boyblrs = 0, girlblrs = 0 } = analysis || {}
              setAnalysisData({
                zrs,
                slzcrs,
                slblrs,
                bll: zrs ? ((slblrs / zrs) * 100).toFixed(2) + '%' : 0,
                boybll: zrs ? ((boyblrs / zrs) * 100).toFixed(2) + '%' : 0,
                girlbll: zrs ? ((girlblrs / zrs) * 100).toFixed(2) + '%' : 0,
              })
              setChartData({
                analysis,
                data,
              })
              return {
                success: errCode ? false : true,
                data: data || [],
                total: data?.length,
              }
            } else {
              return {
                success: true,
                data: [],
                total: 0,
              }
            }
          }}
          columns={getPhyGradeColums({
            detail,
            onExport,
          })}
          metas={getPhyGradeMetas({
            detail,
            onExport,
          })}
        />
        <ChartGroup type="年级" chartData={chartData} />
      </div>

      {env_screen.includes('mobile') && (
        <div
          style={floatBottomStyle}
        >
          <Button
            type="primary"
            shape="round"
            style={{
              width: '100%',
            }}
            onClick={() => {
              history.go(-1)
            }}
          >
            返回上一页
          </Button>
        </div>
      )}
    </div>
  )
}

export default DataStatistic
