import type { ProColumns } from '@ant-design/pro-components'
import { Button, Popconfirm, Select, Space, Tag } from 'antd'
import moment from 'moment'
import SwitchItem from './SwitchItem'

const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const getFillColums = ({
  handleEvent,
}: {
  handleEvent: (type: string, data: any) => void
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '年级班级',
    dataIndex: 'gradeName',
    key: 'gradeName',
    align: 'center',
    width: 80,
    fixed: 'left',
    render: (_dom: React.ReactNode, entity: any) => {
      return `${entity?.gradeName} ${entity?.className}`
    },
  },
  {
    title: '班主任',
    width: 80,
    dataIndex: 'teachername',
    key: 'teachername',
    align: 'center',
  },
  {
    title: '填报日期',
    dataIndex: 'check_time',
    key: 'check_time',
    width: 80,
    align: 'center',
    valueType: 'date',
  },
  {
    title: '填报时段',
    dataIndex: 'period',
    key: 'period',
    width: 80,
    align: 'center',
    valueType: 'select',
    valueEnum: {
      上午: '上午',
      下午: '下午',
    },
  },
  {
    title: '应到人数',
    dataIndex: 'total_number',
    key: 'total_number',
    width: 60,
    align: 'center',
  },
  {
    title: '实到人数',
    dataIndex: 'real_number',
    key: 'real_number',
    width: 60,
    align: 'center',
  },
  {
    title: '缺勤人数',
    dataIndex: 'absence_number',
    key: 'absence_number',
    width: 60,
    align: 'center',
  },
  {
    title: '缺勤原因',
    children: [
      {
        title: '迟到',
        dataIndex: 'late_count',
        key: 'late_count',
        width: 60,
        align: 'center',
      },
      {
        title: '因病',
        dataIndex: 'ill_count',
        key: 'ill_count',
        width: 60,
        align: 'center',
      },
      {
        title: '因事',
        dataIndex: 'other_count',
        key: 'other_count',
        width: 60,
        align: 'center',
      },
      {
        title: '其他',
        dataIndex: 'other_reason_count',
        key: 'other_reason_count',
        width: 60,
        align: 'center',
      },
    ],
  },
  {
    title: '到校学生身体状况',
    children: [
      {
        title: '良好',
        dataIndex: 'healthy_count',
        key: 'healthy_count',
        width: 60,
        align: 'center',
      },
      {
        title: '生病',
        dataIndex: 'sick_count',
        key: 'sick_count',
        width: 60,
        align: 'center',
      },
    ],
  },
  {
    title: '处理结果',
    children: [
      {
        title: '留校观察',
        dataIndex: 'observing_count',
        key: 'observing_count',
        width: 60,
        align: 'center',
      },
      {
        title: '回家治疗',
        dataIndex: 'home_treatment_count',
        key: 'home_treatment_count',
        width: 60,
        align: 'center',
      },
    ],
  },
  // {
  //   title: '备注',
  //   dataIndex: 'comment',
  //   key: 'comment',
  //   align: 'center',
  //   width: 100,
  //   render: (_, record) => {
  //     return (
  //       <Paragraph
  //         ellipsis={{
  //           rows: 2,
  //           expandable: true,
  //         }}
  //         style={{
  //           marginBottom: 0,
  //         }}
  //         title={`${record?.comment}`}
  //       >
  //         {record?.comment}
  //       </Paragraph>
  //     )
  //   },
  // },
  {
    title: '操作',
    key: 'option',
    width: 140,
    fixed: 'right',
    render: (_: any, record: any) => (
      <Space>
        <Button size="small" type="text" onClick={() => handleEvent('lookTable', record)}>
          查看
        </Button>
        <>
          {!record?.forbid && (
            <>
              <Button
                size="small"
                type="link"
                style={{
                  cursor: 'pointer',
                }}
                onClick={() => handleEvent('editTable', record)}
              >
                编辑
              </Button>
              <Popconfirm
                key="deleteTable"
                title="确定删除吗?"
                onConfirm={() => handleEvent('deleteTable', record)}
                okText="确定"
                cancelText="取消"
                placement="topRight"
              >
                <Button
                  size="small"
                  type="link"
                  danger
                  style={{
                    cursor: 'pointer',
                  }}
                >
                  删除
                </Button>
              </Popconfirm>
            </>
          )}
        </>
      </Space>
    ),
    align: 'center',
  },
]

const getFillMetas = ({ handleEvent }: { handleEvent: (type: string, data: any) => void }): any => {
  return {
    title: {
      dataIndex: 'check_time',
      render: (dom: React.ReactNode, entity: any) => {
        return `${entity?.check_time}填报的晨午检`
      },
    },
    subTitle: {
      render: (_dom: React.ReactNode, entity: any) => {
        return (
          <Space
            style={{
              marginTop: '5px',
            }}
          >
            <Tag>
              {entity.gradeName}
              {entity.className}
            </Tag>
            {entity.period && (
              <Tag color={entity.period === '下午' ? 'gold' : 'blue'}>{entity.period}</Tag>
            )}
          </Space>
        )
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>班主任：{entity?.teachername}</p>
            <p style={styleP}>
              应到：{entity?.total_number} 实到：{entity?.real_number} 缺勤：
              {entity?.absence_number}
            </p>
            <p style={styleP}>填报时间: {moment(entity?.check_time).format('YYYY.MM.DD HH:mm')}</p>
          </div>
        )
      },
    },
    actions: {
      render: (_dom: any, row: any) => {
        return (
          <Space>
            <Button type="text" onClick={() => handleEvent('lookTable', row)}>
              查看
            </Button>
            <Button
              type="link"
              style={{
                cursor: 'pointer',
              }}
              onClick={() => handleEvent('editTable', row)}
            >
              编辑
            </Button>
            <Popconfirm
              key="deleteTable"
              title="确定删除吗?"
              onConfirm={() => handleEvent('deleteTable', row)}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Button
                type="link"
                danger
                style={{
                  cursor: 'pointer',
                }}
              >
                删除
              </Button>
            </Popconfirm>
          </Space>
        )
      },
    },
  }
}

const getTableColums = ({
  onSwitchItemHandler,
  onChangeDates,
  type,
  isMobile,
}: {
  onSwitchItemHandler: (value: any, checked: boolean) => void
  onChangeDates: (value: any, data: any, fields: any) => void
  type: 'look' | 'edit' | 'add' | undefined
  isMobile: boolean
}): any => {
  const arr = [
    {
      title: '姓名',
      dataIndex: 'studentName',
      key: 'studentName',
      align: 'center',
    },
    {
      title: '年级',
      dataIndex: 'gradeName',
      key: 'gradeName',
      align: 'center',
    },
    {
      title: '班级',
      dataIndex: 'className',
      key: 'className',
      align: 'center',
      hidden: isMobile,
    },
    {
      title: '到校',
      dataIndex: 'isRealTo',
      key: 'isRealTo',
      align: 'center',
      render: (_dom: string, record: any) => {
        if (type !== 'look') {
          return (
            <SwitchItem
              realTo={record.isRealTo}
              record={record}
              onSwitchItem={onSwitchItemHandler}
            />
          )
        } else {
          return record?.is_school ? '到校' : '未到校'
        }
      },
    },
    {
      title: '到校学生身体状况',
      dataIndex: 'physical_condition',
      key: 'physical_condition',
      align: 'center',
      render: (_: string, record: any) => {
        if (record?.isRealTo === '出勤' && type !== 'look') {
          return (
            <Select
              style={{ width: 120 }}
              value={record.physical_condition}
              options={[
                {
                  value: '良好',
                  label: '良好',
                },
                {
                  value: '生病',
                  label: '生病',
                },
              ]}
              defaultValue={record.physical_condition}
              onSelect={(v) => {
                onChangeDates?.(v, record, 'physical_condition')
              }}
            />
          )
        }
        return record?.physical_condition ? record?.physical_condition : '-'
      },
    },
    {
      title: '缺勤原因',
      dataIndex: 'absence_reason',
      key: 'absence_reason',
      align: 'center',
      render: (_: string, record: any) => {
        if (record?.isRealTo !== '出勤' && type !== 'look') {
          return (
            <Select
              style={{ width: 120 }}
              value={record.absence_reason}
              options={[
                {
                  value: '迟到',
                  label: '迟到',
                },
                {
                  value: '因事',
                  label: '因事',
                },
                {
                  value: '因病',
                  label: '因病',
                },
                {
                  value: '其他',
                  label: '其他',
                },
              ]}
              onSelect={(v) => {
                onChangeDates?.(v, record, 'absence_reason')
              }}
            />
          )
        }
        return record?.absence_reason ? record?.absence_reason : '-'
      },
    },
    {
      title: '处理结果',
      dataIndex: 'handle_result',
      key: 'handle_result',
      align: 'center',
      render: (_: string, record: any) => {
        if (
          ((record?.isRealTo !== '出勤' && record?.absence_reason === '因病') ||
            record?.physical_condition === '生病') &&
          type !== 'look'
        ) {
          return (
            <Select
              style={{ width: 120 }}
              value={record.handle_result}
              options={[
                {
                  value: '回家治疗',
                  label: '回家治疗',
                },
                {
                  value: '留校观察',
                  label: '留校观察',
                },
              ]}
              onSelect={(v) => {
                onChangeDates?.(v, record, 'handle_result')
              }}
            />
          )
        }
        return record?.handle_result ? record?.handle_result : '-'
      },
    },
  ]
  if (isMobile) {
    return arr.filter((item) => item.key !== 'gradeName' && item.key !== 'className')
  }
  return arr
}
export { getFillColums, getFillMetas, getTableColums }
