/** 雁塔晨钟 */
import React, { useState, useEffect, useCallback } from 'react'
import styles from './index.less'
import Wheel from '../Wheel'
import { firstGradePoetry, secondGradePoetry } from '../data'
import { Drawer } from 'antd'
import IdentifyFlag from '../IdentifyFlag'
import Audio from '../Audio'
import two01 from '../Audio/two01.mp3'
import two02 from '../Audio/two02.mp3'
import Common from '../common'
import AnswerAncientPoetry from '../AnswerAncientPoetry'

const ThroughForest: React.FC<{ currentStudent: any }> = ({ currentStudent }) => {
  const [playWheel, setPlayWheel] = useState(false)
  const [poetryList, setPoetryList] = useState<string[]>([]) // 古诗列表
  const [nextOpen, setNextOpen] = useState<boolean>(false)
  const [currentPoetry, setCurrentPoetry] = useState<string>('') // 当前古诗
  const [mp3Url, setMp3Url] = useState<string>(two01)

  const [commonText, setCommonText] = useState<{
    /** 是否结束 */
    status: boolean
    message: string
  }>({
    status: false,
    message: '',
  })

  useEffect(() => {
    setPlayWheel(true)
  }, [])

  const getRandomItems = (array: string[], numItems: number) => {
    const shuffledArray = array.slice(0).sort(() => 0.5 - Math.random())
    return shuffledArray.slice(0, numItems)
  }

  const initializeTopicText = useCallback((gradeName?: string) => {
    if (gradeName === '一年级') {
      return setPoetryList(getRandomItems(firstGradePoetry, 8))
    } else {
      return setPoetryList(getRandomItems(secondGradePoetry, 8))
    }
  }, [])

  useEffect(() => {
    if (currentStudent && currentStudent.classes && currentStudent.classes[0]) {
      const gradeName = currentStudent.classes[0]?.grade_name
      initializeTopicText(gradeName)
      setCommonText({
        status: false,
        message: `${currentStudent?.name} 点击开始，选择你要背诵的古诗吧！`,
      })
    }
  }, [currentStudent, initializeTopicText])

  return (
    <>
      <div className={styles.titleBox}>
        <h1 className={styles.title}>欢迎来到长安八景“雁塔晨钟”</h1>
        <div className={styles.described}>——夏之趣</div>
      </div>
      <div className={styles.videoBox}>
        <div className={`${styles.wheel} ${playWheel ? styles.animate : ''}`}>
          <Wheel
            wordList={poetryList.map((item) => ({
              title: item,
              hasQuestion: !!item.length,
            }))}
            style={{
              position: 'absolute',
              top: '50%',
              left: '85%',
              transform: 'translate(-50%,-50%)',
            }}
            callback={(title: string) => {
              setCurrentPoetry(title)
              setMp3Url(two02)
              setCommonText({
                status: false,
                message: `${currentStudent?.name} 请背诵古诗 ${title}`,
              })
            }}
            gotoNext={() => {
              setNextOpen(true)
            }}
          />
        </div>
      </div>
      <Common currentHello={commonText.message} />
      {currentStudent?.classes[0]?.grade_name === '一年级' ? (
        <Drawer
          width={'100vw'}
          open={nextOpen}
          onClose={() => setNextOpen(false)}
          headerStyle={{ display: 'none' }}
          bodyStyle={{ padding: 0 }}
        >
          <AnswerAncientPoetry currentStudent={currentStudent} currentPoetry={currentPoetry} />
        </Drawer>
      ) : (
        <Drawer
          width={'100vw'}
          open={nextOpen}
          onClose={() => setNextOpen(false)}
          headerStyle={{ display: 'none' }}
          bodyStyle={{ padding: 0 }}
        >
          <IdentifyFlag currentStudent={currentStudent} />
        </Drawer>
      )}
      <Audio src={mp3Url} />
    </>
  )
}

export default ThroughForest
