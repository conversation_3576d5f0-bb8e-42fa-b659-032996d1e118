import CommonList from '@/components/CommonList'
import { queryList } from '@/services/edu-platform-web/archives_unusual'
import { message } from 'antd'
import React from 'react'
import { getAuditColumns, getAuditMetas, UnusualStatus } from './columns'
import { unusualFormatData } from './TransferApplication'
import styles from './index.less'
import { useModel } from 'umi'

const List: React.FC = () => {
  const { initialState } = useModel('@@initialState')
  const { currentUser } = initialState || {}
  return (
    <>
      <div className={styles.commonList}>
        <CommonList
          params={{}}
          request={async (params: any) => {
            const { pageSize, current: page } = params
            const { errCode, list, count, total } = await queryList({
              checker: currentUser?.username,
              status: `${UnusualStatus['已通过']},${UnusualStatus['已拒绝']}`,
              offset: Number((page || 1) - 1) * Number(pageSize),
              limit: Number(pageSize),
              chooseSchool: currentUser?.enterpriseCode || '',
            })
            if (errCode) {
              message.error('获取已审核列表获取失败')
              return {
                success: true,
                data: [],
                total: 0,
              }
            } else {
              const newList = unusualFormatData(list)
              return {
                success: true,
                data: newList || [],
                total: count || total,
              }
            }
          }}
          columns={getAuditColumns({ isExamined: true })}
          metas={getAuditMetas({ isExamined: true })}
        />
      </div>
    </>
  )
}
export default List
