import { diseaseStudentRemove } from '@/services/edu-platform-web/disease_student'
import type { ProColumns } from '@ant-design/pro-components'
import { Button, Popconfirm, Space, Tag, Tooltip, Typography, message } from 'antd'
import moment from 'moment'
import { history } from 'umi'

const { Paragraph } = Typography
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const getRecordColums = ({
  refreshTable,
  setModalProps,
}: {
  refreshTable: () => void
  setModalProps: React.Dispatch<
    React.SetStateAction<{
      visible: boolean
      data?: any
    }>
  >
  currentUser?: UserInfo | null
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '年级班级',
    dataIndex: 'gradeName',
    key: 'gradeName',
    align: 'center',
    width: 60,
    fixed: 'left',
    render: (_, record) => {
      return (
        <Tag color="blue">
          {record.gradeName}
          {record.className}
        </Tag>
      )
    },
  },
  {
    title: '姓名',
    dataIndex: 'studentName',
    key: 'studentName',
    width: 60,
    align: 'center',
  },
  {
    title: '学号',
    dataIndex: 'studentCode',
    key: 'studentCode',
    width: 60,
    align: 'center',
  },
  {
    title: '性别',
    dataIndex: 'gender',
    key: 'gender',
    width: 60,
    align: 'center',
    hideInTable: true,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 60,
    align: 'center',
    hideInTable: true,
  },
  {
    title: '家长姓名',
    dataIndex: 'parentName',
    key: 'parentName',
    width: 60,
    align: 'center',
  },
  {
    title: '联系电话',
    dataIndex: 'parentPhone',
    key: 'parentPhone',
    width: 80,
    align: 'center',
  },
  {
    title: '排查原因',
    dataIndex: 'reason',
    key: 'reason',
    align: 'center',
    width: 100,
    hideInTable: true,
    render: (_, record) => {
      return (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: true,
          }}
          style={{
            marginBottom: 0,
          }}
          title={`${record?.comment}`}
        >
          {record?.comment}
        </Paragraph>
      )
    },
  },
  {
    title: '缺勤天数',
    dataIndex: 'absenceNumber',
    key: 'absenceNumber',
    width: 60,
    align: 'center',
  },
  {
    title: '发病日期',
    dataIndex: 'disease_date',
    key: 'disease_date',
    width: 80,
    align: 'center',
    valueType: 'date',
  },
  {
    title: '主要症状',
    dataIndex: 'disease_symptoms',
    key: 'disease_symptoms',
    width: 160,
    align: 'center',
    render: (_, record) => {
      return (
        <>
          {record?.disease_type === '非传染病'
            ? record?.disease_symptoms?.map((v: any) => <Tag key={v.name}>{v.name}</Tag>)
            : record?.contagion}
        </>
      )
    },
  },
  {
    title: '是否就诊',
    dataIndex: 'is_diagnose',
    key: 'is_diagnose',
    width: 60,
    align: 'center',
    render: (_, record) => {
      return <>{record?.is_diagnose ? '是' : '否'}</>
    },
  },
  {
    title: '排查结果',
    dataIndex: 'check_result',
    key: 'check_result',
    align: 'center',
    width: 100,
    hideInTable: true,
    render: (_, record) => {
      return (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: true,
          }}
          style={{
            marginBottom: 0,
          }}
          title={`${record?.comment}`}
        >
          {record?.comment}
        </Paragraph>
      )
    },
  },
  {
    title: '是否内宿',
    dataIndex: 'is_resident',
    key: 'is_resident',
    width: 60,
    align: 'center',
    hideInTable: true,
  },
  {
    title: '登记人',
    dataIndex: 'teacherName',
    key: 'teacherName',
    width: 60,
    align: 'center',
  },
  {
    title: '登记日期',
    dataIndex: 'record_date',
    key: 'record_date',
    width: 60,
    align: 'center',
    valueType: 'date',
  },
  {
    title: '操作',
    key: 'option',
    width: 120,
    fixed: 'right',
    render: (_: any, record: any) => (
      <>
        <Button
          type="link"
          style={{
            cursor: 'pointer',
          }}
          onClick={async () => {
            setModalProps({
              visible: true,
              data: record,
            })
          }}
        >
          编辑
        </Button>
        <Popconfirm
          key="deleteTable"
          title="确定删除吗?"
          onConfirm={async () => {
            if (record.id) {
              const res = (await diseaseStudentRemove(record.id)) as ResType<any>
              if (res?.errCode) {
                message.error(res.message || '删除失败，请联系管理员或稍后再试')
              } else {
                message.success('删除成功')
                refreshTable?.()
              }
            }
          }}
          okText="确定"
          cancelText="取消"
          placement="topRight"
        >
          <Button
            type="link"
            danger
            style={{
              cursor: 'pointer',
            }}
          >
            删除
          </Button>
        </Popconfirm>
        <Button
          type="text"
          onClick={() => {
            history.push({
              pathname: '/educationaManagement/noonInspection/inspectRecord/detail',
              query: {
                id: record.id,
              },
            })
          }}
        >
          查看
        </Button>
      </>
    ),
    align: 'center',
  },
]

const getRecordMetas = ({
  refreshTable,
  setModalProps,
}: {
  setModalProps: React.Dispatch<
    React.SetStateAction<{
      visible: boolean
      data?: any
    }>
  >
  refreshTable: () => void
  currentUser?: UserInfo | null
}): any => {
  return {
    title: {
      dataIndex: 'studentName',
      render: (_dom: React.ReactNode, entity: any) => {
        return (
          <div>
            {entity.studentName}
            <Tag
              style={{
                marginInlineStart: '5px',
              }}
              color={entity.gender === '男' ? 'processing' : 'red'}
            >
              {entity.gender}
            </Tag>
            {entity.age}岁
          </div>
        )
      },
    },
    subTitle: {
      render: (_dom: React.ReactNode, entity: any) => {
        return (
          <Tag
            color="blue"
            style={{
              marginBlockStart: '8px',
            }}
          >
            {entity.gradeName}
            {entity.className}
          </Tag>
        )
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>登记人: {entity?.teacherName}</p>
            <p style={styleP}>
              家长信息: {entity?.parentName}
              {entity?.parentPhone && (
                <>
                  （<a href={`tel:${entity?.parentPhone}`}>{entity?.parentPhone}</a>）
                </>
              )}
            </p>
            <p style={styleP}>
              主要症状:{' '}
              {entity?.disease_symptoms?.map((v: any) => (
                <Tag>{v.name}</Tag>
              ))}
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (_: any, row: any) => {
        return (
          <Space>
            {row.status === '草稿' && (
              <>
                <Popconfirm
                  key="deleteTable"
                  title="确定删除吗?"
                  onConfirm={async () => {
                    if (row.id) {
                      const res = (await diseaseStudentRemove(row.id)) as ResType<any>
                      if (res?.errCode) {
                        message.error(res.message || '删除失败，请联系管理员或稍后再试')
                      } else {
                        message.success('删除成功')
                        refreshTable?.()
                      }
                    }
                  }}
                  okText="确定"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Button
                    type="link"
                    danger
                    style={{
                      cursor: 'pointer',
                    }}
                  >
                    删除
                  </Button>
                </Popconfirm>
              </>
            )}
            <Button
              type="text"
              onClick={() => {
                history.push({
                  pathname: '/educationaManagement/noonInspection/inspectRecord/detail',
                  query: {
                    id: row.id,
                  },
                })
              }}
            >
              查看
            </Button>
            <Button
              type="link"
              style={{
                cursor: 'pointer',
              }}
              onClick={async () => {
                setModalProps({
                  visible: true,
                  data: row,
                })
              }}
            >
              编辑
            </Button>
          </Space>
        )
      },
    },
  }
}

export { getRecordColums, getRecordMetas }
