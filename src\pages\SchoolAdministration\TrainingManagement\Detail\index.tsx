/*
 * @description: 审核流，自己只查看，管理员可审核
 * @author: zpl
 * @Date: 2023-11-27 11:25:53
 * @LastEditTime: 2024-01-10 14:42:03
 * @LastEditors: SissleLynn
 */
import { envjudge, getQueryObj } from '@/utils'
import {
  Button,
  Empty,
  Space,
  Tag,
  Timeline,
  message,
  Image,
  Popconfirm,
  Modal,
  Input,
} from 'antd'
import { LeftOutlined } from '@ant-design/icons'

import moment from 'moment'
import { useEffect, useState } from 'react'
import styles from './index.less'
import { useLocation, useModel } from 'umi'
import { teacherTrainShow, teacherTrainUpdate } from '@/services/edu-platform-web/teacherTrain'
import { ProDescriptions } from '@ant-design/pro-components'
import { floatBottomStyle } from '@/constant'
import { generatePDF } from '../utils'
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const { TextArea } = Input
const Detail = () => {
  const { id } = getQueryObj()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const env_screen = envjudge()
  const { pathname }: any = useLocation()
  const [loading, setLoading] = useState(false)

  const [data, setData] = useState<any>()
  const [visible, setVisible] = useState({
    show: false,
    img: undefined,
  })
  const getData = async () => {
    const result = await teacherTrainShow(id!)
    if (!result?.errCode) {
      setData(result)
    }
  }

  useEffect(() => {
    if (id) {
      getData()
    }
  }, [id])

  if (!id) {
    return <Empty style={{ marginTop: '35vh' }} description="非法访问" />
  }

  const exportPdf = async () => {
    setLoading(true)
    const { status, data: pdfBytes, msg } = await generatePDF([data])
    if (!status) {
      setLoading(false)
      return message.warning(`导出失败 ${msg}`)
    }
    if (!pdfBytes) {
      return message.warning('导出失败 PDF生成内容为空！')
    }
    const blob = new Blob([pdfBytes], { type: 'application/pdf' })
    const fileName = `培训记录.pdf`
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = fileName
    link.click()
    URL.revokeObjectURL(link.href)
    message.success('导出成功')
    setLoading(false)
  }

  return (
    <div className={styles.detailWrapper}>
      <div>
        {!env_screen.includes('mobile') && (
          <Button
            type="primary"
            style={{
              marginBlockEnd: 24,
            }}
            onClick={() => {
              history.go(-1)
            }}
          >
            <LeftOutlined />
            返回上一页
          </Button>
        )}
        <div className={styles.topCon}>
          <div className={styles.title}>
            <span>{data?.realName}提交的培训登记</span>
            {!env_screen.includes('mobile') && (
              <Button type="primary" loading={loading} onClick={exportPdf}>
                {loading ? '导出中' : '导出'}
              </Button>
            )}
          </div>
          <div className={styles.desc}>{schoolInfo?.name}</div>
          <Space>
            <Tag
              color={
                data?.status === '已同意'
                  ? '#87d068'
                  : data?.status === '已拒绝'
                  ? '#f50'
                  : data?.status === '待审批'
                  ? '#108ee9'
                  : 'default'
              }
            >
              {data?.status}
            </Tag>
          </Space>
        </div>

        <div className={styles.middleCon}>
          <div className={styles.desc}>培训信息</div>
          <div className={styles.flowCon}>
            <ProDescriptions column={env_screen.includes('mobile') ? 1 : 2}>
              <ProDescriptions.Item span={2} valueType="text" label="培训主题或内容">
                {data?.train_theme}
              </ProDescriptions.Item>
              <ProDescriptions.Item span={2} label="培训起止时间" valueType="text">
                {moment(data?.start_time).format('MM/DD HH:mm')}至
                {moment(data?.end_time).format('MM/DD HH:mm')}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="组织单位" valueType="text">
                {data?.organization}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="培训时长" valueType="text">
                {data?.class_duration}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="学时" valueType="text">
                {data?.class_hour}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="学科" valueType="text">
                {data?.xk}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="培训方式" valueType="text">
                {data?.train_type}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="培训级别" valueType="text">
                {data?.train_level}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="培训地点" valueType="text" span={2}>
                {data?.address}
              </ProDescriptions.Item>
              {/* <ProDescriptions.Item label="是否有证书" valueType="text">
                <Switch
                  checked={data?.if_certificate}
                  checkedChildren="是"
                  unCheckedChildren="否"
                />
              </ProDescriptions.Item>
              {data?.if_certificate && (
                <>
                  <ProDescriptions.Item label="发证单位" valueType="text">
                    {data?.certificate_org}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="证书图片" valueType="image">
                    {data?.certificate_img}
                  </ProDescriptions.Item>
                </>
              )} */}
            </ProDescriptions>
          </div>
          <div className={styles.desc}>备注</div>
          <div style={{ marginBottom: '10px', fontSize: 'larger', wordBreak: 'break-all' }}>
            {data?.describe || '无'}
          </div>
          <div>
            {data?.train_img && (
              <>
                <div className={styles.desc}>外出照片</div>
                <Image.PreviewGroup>
                  {JSON.parse(data?.train_img)?.map((item: any) => {
                    return (
                      <div
                        key={item?.url}
                        style={{
                          display: 'inline-block',
                          padding: env_screen.includes('mobile') ? '6px 0' : '16px',
                        }}
                      >
                        <Image width={200} src={item?.url} />
                        <p style={styleP}>
                          拍照时间：{moment(item?.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                        </p>
                        <p style={styleP}>地图定位：{item?.address}</p>
                      </div>
                    )
                  })}
                </Image.PreviewGroup>
              </>
            )}
          </div>
          <div>
            {data?.if_certificate && data?.certificate_img && (
              <>
                <div className={styles.desc}>培训证书</div>
                <Image.PreviewGroup>
                  {data?.certificate_img.split(',')?.map((item: any) => {
                    return (
                      <div
                        key={item}
                        style={{
                          display: 'inline-block',
                          padding: env_screen.includes('mobile') ? '6px 0' : '16px',
                        }}
                      >
                        <Image width={200} src={item} />
                      </div>
                    )
                  })}
                </Image.PreviewGroup>
              </>
            )}
          </div>
        </div>
        {data?.sq_time && (
          <div className={styles.bottomCon}>
            <div className={styles.title}>流程</div>
            <Timeline
              mode="left"
              style={{
                marginTop: '10px',
                paddingTop: '30px',
              }}
            >
              <Timeline.Item>
                <p>{moment(data?.sq_time).format('YYYY/MM/DD HH:mm')}</p>
                <p>发起申请</p>
              </Timeline.Item>
              {data?.status !== '待审批' && (
                <Timeline.Item>
                  <p>{moment(data?.sp_time || data?.updatedAt).format('YYYY/MM/DD HH:mm')}</p>
                  <div>
                    审核结果： {data?.status}{' '}
                    {data?.status === '已拒绝' ? (
                      <>
                        <br />
                        审批意见： {data?.sp_result || '无'}
                      </>
                    ) : null}
                  </div>
                </Timeline.Item>
              )}
            </Timeline>
          </div>
        )}
        <Image
          width={200}
          style={{ display: 'none' }}
          src={visible?.img}
          preview={{
            visible: visible.show,
            src: visible.img,
            onVisibleChange: (value) => {
              setVisible({
                show: value,
                img: undefined,
              })
            },
          }}
        />
      </div>
      {env_screen.includes('mobile') ? (
        <div style={floatBottomStyle}>
          <Button
            type={
              pathname.includes('management') && data?.status === '待审批' ? 'default' : 'primary'
            }
            shape="round"
            style={{
              width:
                pathname.includes('management') && data?.status === '待审批' ? '100px' : '100%',
            }}
            onClick={() => {
              history.go(-1)
            }}
          >
            返回上一页
          </Button>
          {pathname.includes('management') && data?.status === '待审批' && (
            <>
              <Popconfirm
                key="agree"
                title={'确定同意' + data?.realName + '的培训登记吗？'}
                onConfirm={async () => {
                  if (data.id) {
                    const res = (await teacherTrainUpdate(data.id, {
                      status: '已同意',
                      sp_time: new Date(),
                      sp_userCode: currentUser?.userCode,
                      sp_realName: currentUser?.realName,
                      sp_username: currentUser?.username,
                    })) as ResType<any>
                    if (res?.errCode) {
                      message.error(res.message || '操作失败，请联系管理员或稍后再试')
                    } else {
                      message.success('操作成功')
                      getData?.()
                    }
                  }
                }}
                okText="确定"
                cancelText="取消"
                placement="topRight"
              >
                <Button
                  shape="round"
                  type="primary"
                  style={{
                    cursor: 'pointer',
                    width: '80px',
                    marginInline: 16,
                  }}
                >
                  同意
                </Button>
              </Popconfirm>
              <Button
                shape="round"
                type="ghost"
                danger
                onClick={async () => {
                  if (data?.id) {
                    // 审核意见
                    let approvalOpinion: string
                    Modal.confirm({
                      title: `确定拒绝${data?.realName}的培训登记吗？`,
                      content: (
                        <TextArea
                          placeholder="审批意见"
                          autoSize={{ minRows: 3, maxRows: 6 }}
                          maxLength={100}
                          showCount
                          onChange={(e) => {
                            approvalOpinion = e.target.value
                          }}
                        />
                      ),
                      onOk: async () => {
                        const res = await teacherTrainUpdate(data.id, {
                          status: '已拒绝',
                          sp_time: new Date(),
                          sp_result: approvalOpinion,
                          sp_userCode: currentUser?.userCode,
                          sp_realName: currentUser?.realName,
                          sp_username: currentUser?.username,
                        })
                        if (res?.errCode) {
                          message.error('操作失败，请联系管理员或稍后再试')
                        } else {
                          message.success('操作成功')
                          getData?.()
                        }
                      },
                    })
                  }
                }}
                style={{
                  cursor: 'pointer',
                  width: '80px',
                }}
              >
                拒绝
              </Button>
            </>
          )}
        </div>
      ) : pathname.includes('management') && data?.status === '待审批' ? (
        <div
          style={{
            position: 'fixed',
            insetInlineEnd: 24,
            insetBlockEnd: 24,
            width: 'calc(100vw - 280px)',
            background: '#fff',
            padding: '8px 24px',
            borderTop: '1px solid #ececec',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Popconfirm
            key="agree"
            title={'确定同意' + data?.realName + '的培训登记吗？'}
            onConfirm={async () => {
              if (data.id) {
                const res = (await teacherTrainUpdate(data.id, {
                  status: '已同意',
                  sp_time: new Date(),
                  sp_userCode: currentUser?.userCode,
                  sp_realName: currentUser?.realName,
                  sp_username: currentUser?.username,
                })) as ResType<any>
                if (res?.errCode) {
                  message.error(res.message || '操作失败，请联系管理员或稍后再试')
                } else {
                  message.success('操作成功')
                  getData?.()
                }
              }
            }}
            okText="确定"
            cancelText="取消"
            placement="topRight"
          >
            <Button
              type="primary"
              style={{
                cursor: 'pointer',
                width: '100px',
                marginRight: 16,
              }}
            >
              同意
            </Button>
          </Popconfirm>
          <Button
            type="ghost"
            danger
            onClick={async () => {
              if (data?.id) {
                // 审核意见
                let approvalOpinion: string
                Modal.confirm({
                  title: `确定拒绝${data?.realName}的培训登记吗？`,
                  content: (
                    <TextArea
                      placeholder="审批意见"
                      autoSize={{ minRows: 3, maxRows: 6 }}
                      maxLength={100}
                      onChange={(e) => {
                        approvalOpinion = e.target.value
                      }}
                    />
                  ),
                  onOk: async () => {
                    const res = await teacherTrainUpdate(data.id, {
                      status: '已拒绝',
                      sp_time: new Date(),
                      sp_result: approvalOpinion,
                      sp_userCode: currentUser?.userCode,
                      sp_realName: currentUser?.realName,
                      sp_username: currentUser?.username,
                    })
                    if (res?.errCode) {
                      message.error('操作失败，请联系管理员或稍后再试')
                    } else {
                      message.success('操作成功')
                      getData?.()
                    }
                  },
                })
              }
            }}
            style={{
              cursor: 'pointer',
              width: '100px',
            }}
          >
            拒绝
          </Button>
        </div>
      ) : (
        ''
      )}
    </div>
  )
}

export default Detail
