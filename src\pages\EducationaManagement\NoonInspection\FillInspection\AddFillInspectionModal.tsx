import { getTeacherInfoAPI } from '@/services/edu-platform-web/class_duty'
import { But<PERSON>, Col, DatePicker, Drawer, Radio, Result, Row, Select, Space, message } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import { useModel } from 'umi'
import dayjs from 'dayjs'
import { controllerRepeatGetStudents } from '@/services/edu-platform-web/ssoRepeat'
import moment from 'moment'
import { controllerStudentAttendance } from '@/services/edu-platform-web/attendanceStudent'
import styles from './index.less'
import { getById } from '@/services/edu-platform-web/noonInspection'
import { envjudge } from '@/utils'
import MakeOut from './MakeOut'

const now = new Date()
const isAfternoon =
  now.getHours() >= 13 || (now.getHours() === 12 && (now.getMinutes() > 0 || now.getSeconds() > 0))
const isPeriod = isAfternoon ? '下午' : '上午'

const AddFillInspectionModal = ({
  termCode,
  modalProps,
  setModalProps,
  onSubmit,
  loadings,
}: {
  termCode?: string
  modalProps: {
    visible: boolean
    data?: any
    type: 'look' | 'edit' | 'add' | undefined
  }
  setModalProps: React.Dispatch<
    React.SetStateAction<{
      visible: boolean
      data?: any
      type: 'look' | 'edit' | 'add' | undefined
    }>
  >
  onSubmit: (data: any) => void
  loadings?: boolean
}) => {
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  /** 年级班级列表 */
  const [gradeList, setGradeList] = useState<any[]>([])
  /** 当前选中的年级班级 */
  const [selectGrade, setSelectGrade] = useState<any>()
  /** 学生列表 */
  const [studentData, setStudentData] = useState<any>([])
  const [dataSource, setDataScouse] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  /* 填报日期 */
  const [time, setTime] = useState<string>()
  /** 填报时段 */
  const [period, setPeriod] = useState<string | undefined>(isPeriod)

  /** 获取教师班级信息 */
  const getTeacherInfo = useCallback(async () => {
    const res = await getTeacherInfoAPI('getClassesByMemberId', {
      query: { memberId: currentUser?.memberId, assistant: '1' },
    })
    if (res?.code) {
      message.warning('获取教师任课班级信息失败，请联系管理员或稍后再试')
      return []
    }
    const newData = res?.map(
      (item: {
        grade_name: string
        name: string
        grade_code: string
        code: string
        id: string
      }) => ({
        label: item.grade_name + item.name,
        value: `${item.grade_code},${item.code}`,
        grade_name: item.grade_name,
        grade_code: item.grade_code,
        class_name: item.name,
        class_code: item.code,
        id: item.id,
      }),
    )
    setGradeList(newData)
    if (newData.length === 0) {
      setSelectGrade({})
    } else if (newData.length >= 1) {
      setSelectGrade(newData[0])
    }
  }, [currentUser?.memberId])

  /** 查询学生请假名单 */
  const getRollCall = useCallback(
    async ({
      classCode,
      gradeCode,
      startTime,
      endTime,
    }: {
      classCode: string
      gradeCode: string
      startTime?: string
      endTime?: string
    }) => {
      /** 班级请假学生名单 */
      const res = (await controllerStudentAttendance({
        enterpriseCode: schoolInfo?.code || '',
        semesterCode: termCode || '',
        classCode,
        gradeCode,
        startTime,
        endTime,
      })) as any

      if (res?.errCode) {
        console.warn(
          '班级请假学生获取失败，请联系管理员或稍后再试！' + ' ' + (res?.msg ?? res?.message),
        )
        return []
      }
      return res as any[]
    },
    [schoolInfo?.code, termCode],
  )

  /** 修改学生是否出勤 */
  const onSwitchItem = (value: any, checked: boolean) => {
    const newData = [...dataSource]
    newData.forEach((item) => {
      if (item?.id === value?.id) {
        if (checked) {
          item.isRealTo = '出勤'
          item.type = 2
          item.isType = undefined
          item.isLeave = false
          item.physical_condition = null
          item.absence_reason = null
          item.handle_result = null
        } else {
          item.physical_condition = null
          item.absence_reason = null
          item.handle_result = null
          if (item?.isLeave) {
            item.isRealTo = '请假'
            item.type = 1
          } else {
            item.isRealTo = '缺席'
            item.type = 0
          }
        }
      }
    })
    setDataScouse(newData)
  }

  /** 修改指定字段的指定值 */
  const onChangeDate = (value: any, data: any, fields: any) => {
    const newData = [...dataSource]
    newData.forEach((item) => {
      if (item?.id === data?.id) {
        /** 当改变处理结果时，不需要为 null */
        if (fields !== 'handle_result') {
          item.physical_condition = null
          item.absence_reason = null
          item.handle_result = null
        }
        item[fields] = value
      }
    })
    setDataScouse(newData)
  }

  /** 合并学生列表数据 */
  const mergeStudentList = ({
    studentList,
    newList = [],
    type,
  }: {
    studentList: any[]
    newList: any[]
    type: 'leave' | 'afterclass'
  }): any[] => {
    const newStudents: any[] = studentList?.map((v: any) => {
      const { work_code, code, IDNumber } = v
      const curInfo = newList.find(
        (val: any) =>
          val.userCode === work_code || val.xh === code || (IDNumber && val.id_card === IDNumber),
      )
      return {
        id: v.id,
        studentCode: v.work_code,
        studentName: v.name,
        gradeName: v?.classes?.[0]?.grade_name,
        className: v?.classes?.[0]?.name,
        type: curInfo ? (type === 'leave' ? 1 : 2) : 2,
        isRealTo: curInfo ? (type === 'leave' ? '请假' : '出勤') : '出勤',
        isLeave: !!curInfo && type === 'leave',
        leaveYY: curInfo?.reason,
        isType: curInfo?.type,
      }
    })
    return newStudents
  }

  /** 通过年级班级信息获取学生信息 */
  const getStudents = async ({ classCode, grade }: { classCode: string; grade: string }) => {
    if (!classCode || !grade)
      return message.warning('获取学生列表失败（未获取到年级班级），请联系管理员或稍后再试。')
    setLoading(true)
    const result = await controllerRepeatGetStudents({
      enterpriseId: schoolInfo?.id || '',
      classCode,
      grade,
      semesterCode: termCode,
    })
    if (result?.errCode) {
      return message.warning(
        '获取学生列表失败，请联系管理员或稍后再试' + ' ' + (result?.msg ?? result?.message),
      )
    } else {
      setStudentData(result?.list || [])
      // 查询请假名单
      const rollList = await getRollCall({
        classCode,
        gradeCode: grade,
        startTime: new Date().toLocaleDateString(),
        endTime: new Date().toLocaleDateString(),
      })
      const newStudents = mergeStudentList({
        studentList: result?.list,
        newList: rollList,
        type: 'leave',
      })
      setDataScouse(newStudents)
      setLoading(false)
    }
  }

  /** 获取晨午检详情 */
  const getMorningCheck = useCallback(async (id: string) => {
    setLoading(true)
    const res = await getById(id)
    if (res?.errCode) {
      setLoading(false)
      return message.warning(
        '获取晨午检详情失败，请联系管理员或稍后再试' + '' + (res?.msg ?? res?.message),
      )
    }
    const newData = res?.morning_check_details?.map((item: any) => {
      return {
        ...item,
        gradeName: res.gradeName,
        className: res.className,
        isRealTo: item?.is_school ? '出勤' : '缺勤',
      }
    })
    newData.sort(
      (
        a: { isRealTo: string; physical_condition: string },
        b: { isRealTo: string; physical_condition: string },
      ) => {
        if (a.isRealTo === '出勤' && b.isRealTo !== '出勤') {
          return 1
        }
        if (a.isRealTo !== '出勤' && b.isRealTo === '出勤') {
          return -1
        }
        if (a.physical_condition === '良好' && b.physical_condition !== '良好') {
          return 1
        }
        if (a.physical_condition !== '良好' && b.physical_condition === '良好') {
          return -1
        }
        return 0
      },
    )
    setDataScouse(newData)
    setLoading(false)
  }, [])

  const drawerTitle = (type?: 'look' | 'edit' | 'add') => {
    switch (type) {
      case 'look':
        return '查看晨午检填报信息'
      case 'edit':
        return '编辑晨午检填报信息'
      default:
        return '晨午检填报'
    }
  }

  const onClose = () => {
    setModalProps({ visible: false, type: undefined, data: undefined })
    setDataScouse([])
    setPeriod(isPeriod)
  }

  /** 组装数据并提交 */
  const addData = () => {
    const absenteeismList = dataSource.filter((item) => item?.isRealTo !== '出勤')
    if (absenteeismList.some((item) => item.absence_reason === undefined)) {
      message.warning('请填写所有缺勤学生的缺勤原因')
      return
    }
    const { gradeCode, gradeName, classCode, className } = modalProps?.data || {}
    const newDataSource = {
      period,
      gradeCode: selectGrade?.grade_code ?? gradeCode,
      gradeName: selectGrade?.grade_name ?? gradeName,
      classCode: selectGrade?.class_code ?? classCode,
      className: selectGrade?.class_name ?? className,
      check_time: time ?? moment(new Date()),
      total_number: dataSource?.length,
      real_number: dataSource.filter((item) => item?.isRealTo === '出勤').length ?? 0,
      absence_number: dataSource.filter((item) => item?.isRealTo !== '出勤').length ?? 0,
      teacherCode: currentUser?.userCode,
      teacherName: currentUser?.realName,
      enterpriseCode: schoolInfo?.code,
      enterpriseName: schoolInfo?.name,
      semesterCode: termCode,
      data: dataSource.map((item) => {
        return {
          studentCode: item?.studentCode,
          studentName: item?.studentName,
          absence_reason: item?.absence_reason,
          physical_condition: item?.physical_condition,
          handle_result: item?.handle_result,
          is_school: item?.isRealTo === '出勤' ? true : false,
        }
      }),
    }
    onSubmit(newDataSource)
  }

  useEffect(() => {
    if (currentUser?.memberId && modalProps?.type === 'add' && modalProps?.visible) {
      getTeacherInfo()
    }
  }, [currentUser?.memberId, getTeacherInfo, modalProps?.type, modalProps?.visible])

  useEffect(() => {
    if (modalProps?.visible && selectGrade && modalProps?.type === 'add') {
      const { class_code, grade_code } = selectGrade ?? {}
      if (class_code && grade_code) {
        getStudents({
          classCode: class_code,
          grade: grade_code,
        })
      }
    }
  }, [modalProps?.type, modalProps?.visible, selectGrade])

  useEffect(() => {
    if (dataSource) {
      const newData = dataSource.map((item: any) => {
        const getDefaultAbsenceReason = (v: {
          isType: string
          isRealTo: string
          absence_reason: string
        }) => {
          return (
            v?.absence_reason ??
            (v?.isType?.includes('病假') ? '因病' : v?.isType === '事假' ? '因事' : undefined)
          )
        }

        const getDefaultPhysicalCondition = (v: { physical_condition: any; isRealTo: string }) => {
          return v?.physical_condition ?? (v?.isRealTo === '出勤' ? '良好' : undefined)
        }

        const getDefaultHandleResult = (v: {
          handle_result: any
          isRealTo: string
          absence_reason: string
        }) => {
          return (
            v?.handle_result ??
            ((v?.isRealTo !== '出勤' && v?.absence_reason === '因病') ||
            item?.physical_condition === '生病'
              ? '回家治疗'
              : undefined)
          )
        }
        return {
          ...item,
          absence_reason: getDefaultAbsenceReason(item),
          physical_condition: getDefaultPhysicalCondition(item),
          handle_result: getDefaultHandleResult(item),
        }
      })

      // 比较新旧数据是否有变化
      const hasChanged = dataSource.some((oldItem, index) => {
        const newItem = newData[index]
        return (
          oldItem.absence_reason !== newItem.absence_reason ||
          oldItem.physical_condition !== newItem.physical_condition ||
          oldItem.handle_result !== newItem.handle_result
        )
      })

      // 只有当数据有变化时才更新 dataSource
      if (hasChanged) {
        setDataScouse(newData)
      }
    }
  }, [dataSource])

  const onMakeOut = useCallback(
    async (type: 'look' | 'edit' | 'add' | undefined, data: { id: string }) => {
      if (type === 'look' || type === 'edit') {
        if (type === 'edit') {
          // 获取教师任课班级信息，防止编辑显示无权限。
          getTeacherInfo()
        }
        await getMorningCheck(data?.id)
      }
    },
    [getTeacherInfo, getMorningCheck],
  )

  useEffect(() => {
    const { visible, type, data } = modalProps
    if (visible) {
      onMakeOut(type, data)
    }
  }, [modalProps, onMakeOut])

  return (
    <Drawer
      title={drawerTitle(modalProps?.type)}
      open={modalProps?.visible}
      destroyOnClose={false}
      width={isMobile ? '100vw' : '80vw'}
      onClose={onClose}
      footer={
        modalProps.type !== 'look' && gradeList.length > 0 ? (
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Space>
              <Button onClick={onClose} loading={loadings}>
                取消
              </Button>
              <Button type="primary" loading={loadings} onClick={addData}>
                提交
              </Button>
            </Space>
          </div>
        ) : (
          false
        )
      }
    >
      {(modalProps.type !== 'look' && gradeList.length > 0) || modalProps.type === 'look' ? (
        <>
          <Row gutter={16}>
            <Col
              span={isMobile ? (modalProps.type === 'add' ? 24 : 12) : undefined}
              style={{
                marginBottom: isMobile && modalProps.type === 'add' ? '10px' : 0,
              }}
            >
              <label htmlFor="xm">年级班级：</label>
              {modalProps?.type === 'add' ? (
                <>
                  <Select
                    options={gradeList}
                    allowClear={false}
                    value={selectGrade?.value}
                    onChange={(value, option) => {
                      setSelectGrade(option)
                      getStudents({
                        classCode: option?.class_code,
                        grade: option?.grade_code,
                      })
                    }}
                  />
                </>
              ) : (
                <>{modalProps?.data?.gradeName + ' ' + modalProps?.data?.className}</>
              )}
            </Col>
            <Col span={isMobile ? (modalProps.type === 'add' ? 24 : 12) : undefined}>
              <label htmlFor="xm">填报日期：</label>
              {modalProps?.type === 'add' ? (
                <DatePicker
                  style={{ width: '180px' }}
                  onChange={async (_, dateString) => {
                    setDataScouse([])
                    setTime(dateString)
                    const rollList = await getRollCall({
                      classCode: selectGrade?.class_code,
                      gradeCode: selectGrade?.class_Code,
                      startTime: dateString,
                      endTime: dateString,
                    })
                    const newStudents = mergeStudentList({
                      studentList: studentData || [],
                      newList: rollList,
                      type: 'leave',
                    })
                    setDataScouse(newStudents)
                  }}
                  disabledDate={(current) => {
                    // Can not select days before today and today
                    return current && current > dayjs().endOf('day')
                  }}
                  defaultValue={moment(new Date())}
                />
              ) : (
                <>{modalProps?.data?.check_time}</>
              )}
            </Col>
            <Col
              span={isMobile ? (modalProps.type === 'add' ? 24 : 12) : undefined}
              style={{
                marginTop: isMobile && modalProps.type === 'add' ? '10px' : 0,
              }}
            >
              <div className={styles.period}>
                <label htmlFor="period">填报时段：</label>
                {modalProps?.type === 'add' ? (
                  <div>
                    <Radio.Group
                      value={period}
                      buttonStyle="solid"
                      onChange={(e) => {
                        setPeriod(e.target.value)
                      }}
                    >
                      <Radio.Button value="上午">上午</Radio.Button>
                      <Radio.Button value="下午" disabled={!isAfternoon}>
                        下午
                      </Radio.Button>
                    </Radio.Group>
                  </div>
                ) : (
                  <>{modalProps?.data?.period}</>
                )}
              </div>
            </Col>
          </Row>

          <Row className={styles.header}>
            <Col span={isMobile ? 12 : 8} className={styles.col}>
              <h3>应到人数：{dataSource.length ?? 0} 人</h3>
            </Col>
            <Col span={isMobile ? 12 : 8} className={styles.col}>
              <h3>
                实到人数：
                {dataSource.filter((item) => item?.isRealTo === '出勤').length ?? 0} 人
              </h3>
            </Col>
            <Col span={isMobile ? 12 : 8} className={styles.col}>
              <h3>
                缺勤人数：
                <span
                  style={{
                    color: '#ff0000',
                  }}
                >
                  {dataSource.filter((item) => item?.isRealTo !== '出勤').length ?? 0}
                </span>
                &nbsp;人
              </h3>
            </Col>
          </Row>
          <MakeOut
            dataSources={dataSource}
            onSwitchItemHandler={onSwitchItem}
            onChangeDates={onChangeDate}
            gradeValue={selectGrade?.value}
            loadings={loading || loadings}
            type={modalProps?.type}
          />
        </>
      ) : (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <Result
            status="warning"
            title={`请联系当前学生的班主任为其${modalProps?.type === 'add' ? '填报' : '编辑'}信息`}
          />
        </div>
      )}
    </Drawer>
  )
}
export default AddFillInspectionModal
