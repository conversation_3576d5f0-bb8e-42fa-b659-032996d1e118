import type { ActionType, ProColumns } from '@ant-design/pro-components'
import { ProFormSelect, ProTable } from '@ant-design/pro-components'
import React, { useRef, useState } from 'react'
import { Button, Col, Input, message, Row, Space, Tooltip } from 'antd'
import { ToTopOutlined } from '@ant-design/icons'
import { useAccess, useModel } from 'umi'
import {
  getAfterServiceStatistics,
  getUpdateCurrMonthData,
  manualSync,
  updateAfterServiceStatistics,
} from '@/services/edu-platform-web/after_service_statistics'
import columns, { WhetherJoinEnum } from './columns'
import SemesterSelect from '@/components/SemesterSelect'
import SearchLayout from '@/components/Search/Layout'
import styles from './index.less'
import {
  controllerRepeatGetClasses,
  controllerRepeatGetGrades,
} from '@/services/edu-platform-web/ssoRepeat'
import { envjudge } from '@/utils'

const { Search } = Input

const AfterServiceStatistics: React.FC = () => {
  const { initialState } = useModel('@@initialState')
  const { isAdministration } = useAccess()
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  const { currentUser, schoolInfo } = initialState || {}
  const actionRef = useRef<ActionType>()
  /** 学期开启时间和结束时间 */
  const [semesterTime, setSemesterTime] = useState<{
    startTime?: string | null
    endTime?: string | null
  }>({
    startTime: null,
    endTime: null,
  })
  /** 当前选择的学期 */
  const [schoolSemesterValue, setSchoolSemesterValue] = useState<string>()
  /** 当前选择的月份 */
  const [momentValue, setMomentValue] = useState<string>()
  /** 当前选择的年级 */
  const [gradeValue, setGradeValue] = useState<string>()
  /** 当前选择的班级 */
  const [classValue, setClassValue] = useState<string>()
  /** 搜索框 */
  const [searchValue, setSearchValue] = useState<string>()
  /** 班级列表 */
  const [classList, setClassList] = useState<
    {
      label: string
      value: string
    }[]
  >([])
  /** 分页 */
  const [pagination, setPagination] = React.useState<{
    current?: number
    pageSize?: number
  }>({
    current: 1,
    pageSize: 10,
  })
  const [whetherJoinValue, setWhetherJoinValue] = useState<string>()
  const [loading, setLoading] = useState<boolean>(false)
  const [updateDataLoading, setUpdateDataLoading] = useState<boolean>(false)
  const [tableLoading, setTableLoading] = useState<boolean>(false)

  console.log(currentUser)

  /**
   * 获取两个日期之间的所有月份
   * @param startDate
   * @param endDate
   * @returns string[]
   */
  const getMonthsBetweenDates = (startDate?: string | null, endDate?: string | null): string[] => {
    if (!startDate || !endDate) {
      return []
    }
    const start = new Date(startDate)
    const end = new Date(endDate)
    const months: string[] = []
    const current = new Date(start.getFullYear(), start.getMonth(), 1)
    while (current <= end) {
      months.push(`${current.getFullYear()},${current.getMonth() + 1}`)
      current.setMonth(current.getMonth() + 1)
    }
    return months
  }
  /** 更改参与课后服务状态 */
  const handleEvents = async (id: string, value: Omit<WhetherJoinEnum, '未确认'>) => {
    if (!id) {
      message.warning('请选择要操作的学生！')
      return
    }
    const res = await updateAfterServiceStatistics(id, {
      whetherJoin: value,
    })
    if (res?.errCode) {
      message.warning('操作失败，请联系管理员或稍后再试！' + res?.msg)
      return
    } else {
      message.success('操作成功')
      actionRef.current?.reload()
    }
  }

  /** 获取班级列表 */
  const getClassList = async (grade: string) => {
    if (!grade) return
    const res = await controllerRepeatGetClasses({
      grade,
      enterpriseId: schoolInfo?.id || '',
      semesterCode: schoolSemesterValue,
    })
    if (res?.errCode) {
      message.warning('班级列表查询失败，请联系管理员' + res?.msg)
      setClassList([])
    }
    const list = res?.list.map((t: { name: string; code: string }) => ({
      label: t.name,
      value: t.code,
    }))
    setClassList(list ?? [])
  }

  /** 剔除移动端不显示的 */
  const mobileColumns = (data: ProColumns[]) => {
    const list = ['index', 'startDate', 'endDate', 'action']
    if (!data && !Array.isArray(data)) return []
    const newData = data.filter((item) => {
      return !list.includes(item.key as string)
    })
    return newData ?? []
  }

  /** 导出 */
  const handleDownload = async () => {
    if (!schoolSemesterValue) {
      message.warning('请选择学期和查询条件！')
      return
    }
    setLoading(true)

    try {
      const baseUrl = '/edu_api/download/after_class_detail'
      const params = new URLSearchParams()
      params.append('enterpriseCode', schoolInfo?.code || '')
      params.append('semesterCode', schoolSemesterValue || '')

      if (whetherJoinValue) {
        params.append('whetherJoin', whetherJoinValue)
      }

      if (searchValue) {
        params.append('studentName', searchValue)
      }

      if (momentValue) {
        params.append('year', momentValue.split(',')[0])
        params.append('month', momentValue.split(',')[1])
      }

      if (gradeValue) {
        params.append('gradeCode', gradeValue)
      }

      if (classValue) {
        params.append('classCode', classValue)
      }
      const url = `${baseUrl}?${params.toString()}`
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      if (!response.ok) {
        return message.warning('下载失败，请联系管理员或稍后再试！')
      }
      const blob = await response.blob()
      const a = document.createElement('a')
      a.href = URL.createObjectURL(blob)
      a.download = '课后服务详情.xlsx'
      document.body.appendChild(a)
      a.click()
      a.remove()
      setLoading(false)
      message.success('导出成功')
    } catch (error) {
      setLoading(false)
      message.error('导出失败，请联系管理员或稍后再试！' + error)
    }
  }

  /** 代入上月数据 */
  const handleInsertData = async () => {
    if (!schoolSemesterValue) {
      message.warning('请选择学期！')
      return
    }
    if (!momentValue) {
      message.warning('请选择月份！')
      return
    }
    if (!gradeValue) {
      message.warning('请选择年级！')
      return
    }
    if (!classValue) {
      message.warning('请选择班级！')
      return
    }
    setUpdateDataLoading(true)
    setTableLoading(true)
    const res = await getUpdateCurrMonthData({
      enterpriseCode: schoolInfo?.code || '',
      semesterCode: schoolSemesterValue,
      gradeCode: gradeValue,
      classCode: classValue,
      year: momentValue.split(',')[0],
      month: momentValue.split(',')[1],
      memberId:
        currentUser?.memberId === '6d9bcc5d-1883-4d27-a1cd-8d13ec8edd75'
          ? undefined
          : currentUser?.memberId || '',
    })
    if (res?.errCode) {
      setUpdateDataLoading(false)
      setTableLoading(false)
      message.warning('同步失败，请联系管理员或稍后再试！' + ' ' + (res?.msg ?? res?.message))
      return
    }
    setUpdateDataLoading(false)
    setTableLoading(false)
    message.success('同步成功！')
    actionRef.current?.reload()
  }

  /** 手动同步学生信息 */
  const handleManualSync = async () => {
    const res = await manualSync({
      enterpriseCode: schoolInfo?.code || '',
      semesterCode: schoolSemesterValue || '',
      memberId: currentUser?.memberId || '',
    })
    if (res?.errCode) {
      message.warning('同步失败，请联系管理员或稍后再试！' + '' + (res?.msg ?? res?.message))
      return
    }
    message.success('同步成功！')
    actionRef.current?.reload()
  }

  return (
    <div className={styles.container}>
      <Row
        className={styles.header}
        style={{
          padding: isMobile ? '0' : '24px 24px 0 24px',
        }}
      >
        <Col span={isMobile ? 24 : 18}>
          <SearchLayout>
            <div
              style={{
                marginBottom: '24px',
              }}
            >
              <SemesterSelect
                onChange={(val, _name, _lock, start, end) => {
                  setMomentValue(undefined)
                  setSemesterTime({
                    startTime: start,
                    endTime: end,
                  })
                  setSchoolSemesterValue(val)
                }}
              />
            </div>

            <ProFormSelect
              width={140}
              wrapperCol={{ span: 18 }}
              labelCol={{ span: 6 }}
              labelAlign="left"
              name="month"
              label="月份"
              fieldProps={{
                value: momentValue,
                loading: loading,
              }}
              onChange={(val: string) => {
                setMomentValue(val)
              }}
              options={getMonthsBetweenDates(semesterTime?.startTime, semesterTime?.endTime).map(
                (item) => {
                  const year = item?.split(',')?.[0]
                  const month = item?.split(',')?.[1]
                  const label = `${year} 年 ${Number(month) > 9 ? month : `0${month}`} 月`
                  return {
                    label,
                    value: item,
                  }
                },
              )}
            />

            <ProFormSelect
              width={140}
              wrapperCol={{ span: 18 }}
              labelCol={{ span: 6 }}
              name="grade"
              label="年级"
              labelAlign="left"
              fieldProps={{
                value: gradeValue,
                onClear() {
                  setGradeValue(undefined)
                  setClassValue(undefined)
                  setClassList([])
                },
                onSelect(val) {
                  setGradeValue(val)
                  getClassList(val)
                  setClassValue(undefined)
                },
              }}
              request={async () => {
                const res = await controllerRepeatGetGrades({
                  section: [schoolInfo?.section_name || ''],
                })
                if (res?.errCode) {
                  message.warning('年级列表查询失败，请联系管理员' + res?.msg)
                  return []
                }
                return res?.list.map((t: { name: string; code: string }) => ({
                  label: t.name,
                  value: t.code,
                }))
              }}
            />

            <ProFormSelect
              width={140}
              wrapperCol={{ span: 18 }}
              labelCol={{ span: 6 }}
              name="class"
              label="班级"
              labelAlign="left"
              onChange={(val: string) => {
                setClassValue(val)
              }}
              fieldProps={{
                value: classValue,
              }}
              options={classList}
            />

            <Search
              allowClear
              placeholder="模糊搜索学生姓名"
              onSearch={(value) => {
                setSearchValue(value)
              }}
              style={{
                width: '200px',
                marginBottom: '24px',
              }}
            />
          </SearchLayout>
        </Col>
        {!isMobile && (
          <Col span={6} style={{ textAlign: 'right' }}>
            <Space>
              {isAdministration(ENV_KHFWTJ, ENV_GL) && !isMobile && (
                <Button
                  type="primary"
                  icon={<ToTopOutlined />}
                  style={{
                    marginBottom: '24px',
                  }}
                  loading={loading}
                  onClick={handleDownload}
                >
                  导出
                </Button>
              )}
              {!isAdministration(ENV_KHFWTJ, ENV_GL) && (
                <Tooltip
                  title="当学生档案发生变更时，系统每日凌晨自动同步学生档案数据。如需立即更新，请点击同步学生按钮。"
                  placement="left"
                >
                  <Button
                    type="primary"
                    style={{
                      marginBottom: '24px',
                    }}
                    loading={updateDataLoading}
                    onClick={handleManualSync}
                  >
                    同步学生
                  </Button>
                </Tooltip>
              )}
              {!isAdministration(ENV_KHFWTJ, ENV_GL) && (
                <Tooltip
                  title="同步上月数据到本月会自动导入上个月的数据，请谨慎使用。"
                  placement="left"
                >
                  <Button
                    type="primary"
                    style={{
                      marginBottom: '24px',
                    }}
                    loading={updateDataLoading}
                    onClick={handleInsertData}
                  >
                    同步上月数据到本月
                  </Button>
                </Tooltip>
              )}
            </Space>
          </Col>
        )}
        {isMobile && (
          <Col
            span={24}
            style={{
              marginTop: '24px',
              textAlign: 'right',
            }}
          >
            <Space>
              {!isAdministration(ENV_KHFWTJ, ENV_GL) && (
                <Button
                  type="primary"
                  style={{
                    marginBottom: '24px',
                  }}
                  loading={updateDataLoading}
                  onClick={handleManualSync}
                >
                  同步学生
                </Button>
              )}
              {!isAdministration(ENV_KHFWTJ, ENV_GL) && (
                <Tooltip
                  title="同步上月数据到本月会自动导入上个月的数据，请谨慎使用。"
                  placement="left"
                >
                  <Button
                    type="primary"
                    style={{
                      marginBottom: '24px',
                    }}
                    loading={updateDataLoading}
                    onClick={handleInsertData}
                  >
                    同步上月数据到本月
                  </Button>
                </Tooltip>
              )}
            </Space>
          </Col>
        )}
      </Row>
      <ProTable
        ghost={isMobile}
        loading={tableLoading}
        columns={
          isMobile
            ? mobileColumns(
                columns({ handleEvents, isAdmin: isAdministration(ENV_KHFWTJ, ENV_GL) }),
              )
            : columns({ handleEvents, isAdmin: isAdministration(ENV_KHFWTJ, ENV_GL) })
        }
        actionRef={actionRef}
        params={{ searchValue, schoolSemesterValue, momentValue, gradeValue, classValue }}
        request={async (params, _sort, filter) => {
          if (!schoolSemesterValue) {
            return []
          }
          const whetherJoin = filter?.whetherJoin || []
          const newState = whetherJoin?.length
            ? whetherJoin.map((item: any) => WhetherJoinEnum[item]).toString()
            : undefined
          setWhetherJoinValue(newState)
          const { pageSize, current } = params
          setTableLoading(true)
          const res = await getAfterServiceStatistics({
            enterpriseCode: currentUser?.enterpriseCode,
            offset: Number((current || 1) - 1) * Number(pageSize),
            limit: Number(pageSize),
            whetherJoin: newState,
            studentName: searchValue || undefined,
            semesterCode: schoolSemesterValue || undefined,
            year: momentValue?.split(',')?.[0] || undefined,
            month: momentValue?.split(',')?.[1] || undefined,
            gradeCode: gradeValue || undefined,
            classCode: classValue || undefined,
            memberId: isAdministration(ENV_KHFWTJ, ENV_GL) ? undefined : currentUser?.memberId,
          })
          if (res?.errCode) {
            setTableLoading(false)
            message.warning('获取列表失败，请联系管理员或稍后再试！' + res?.msg)
            return []
          } else {
            setTableLoading(false)
            return {
              success: true,
              data: res?.list,
              total: res?.total,
            }
          }
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        options={false}
        form={{
          // 由于配置了 transform，提交的参数与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              }
            }
            return values
          },
        }}
        pagination={{
          current: pagination?.current,
          pageSize: pagination?.pageSize,
          showTotal(total) {
            return `总共 ${total} 条`
          },
          onChange: (page, pageSize) => {
            setPagination({
              current: page,
              pageSize: pageSize,
            })
          },
        }}
        dateFormatter="string"
      />
    </div>
  )
}
export default AfterServiceStatistics
