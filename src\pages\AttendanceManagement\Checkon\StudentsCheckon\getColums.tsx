import { Select, Tag, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import SwitchItem from './SwitchItem'
import { WhetherJoinEnum } from '@/pages/EducationaManagement/AfterServiceStatistics/columns'
import { envjudge } from '@/utils'

const env_screen = envjudge()
const isMobile = env_screen?.includes('mobile')

const getColums = ({
  onSwitchItem,
  option = true,
  isSeven,
  onUpdateRecord,
}: {
  onSwitchItem: (value: any, checked: boolean) => void
  option?: boolean
  /** 是不是第七节课 */
  isSeven?: boolean
  onUpdateRecord?: (id: string, value: string) => void
}): any => {
  const arr = [
    {
      title: '姓名',
      dataIndex: 'studentName',
      key: 'studentName',
      align: 'center',
      width: isMobile ? 55 : undefined,
      ellipsis: true,
    },
    !isMobile && {
      title: '年级',
      dataIndex: 'gradeName',
      key: 'grade_name',
      align: 'center',
    },
    !isMobile && {
      title: '班级',
      dataIndex: 'className',
      key: 'className',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'isLeave',
      key: 'isLeave',
      align: 'center',
      render: (_: string, record: any) => {
        if (isSeven) {
          const whetherJoin = record?.whetherJoin
          if (whetherJoin === WhetherJoinEnum['已参与']) {
            return <Tag color="#87d068">已参与</Tag>
          } else if (whetherJoin === WhetherJoinEnum['不参与']) {
            return <Tag color="#108ee9">不参与</Tag>
          } else if (whetherJoin === WhetherJoinEnum['未确认']) {
            return <Tag color="#aaaaaa">未确认</Tag>
          } else {
            return '-'
          }
        } else {
          if (record.isLeave) {
            return (
              <Tooltip title={record.leaveYY} trigger="click">
                <span style={{ color: 'var(--primary-color)' }}>请假</span>
              </Tooltip>
            )
          }
          if (record.isKH) {
            return (
              <Tooltip title={`该学生前往参加【${record?.stkc_name}】`} trigger="click">
                <span style={{ color: 'var(--primary-color)' }}>
                  课后 <QuestionCircleOutlined />
                </span>
              </Tooltip>
            )
          }
          if (record.isHome) {
            return (
              <Tooltip title="该学生未参加课后服务" trigger="click">
                <span style={{ color: '#888' }}>回家</span>
              </Tooltip>
            )
          }
          if (record.isRealTo !== '出勤') {
            return (
              <Select
                style={{
                  width: '100%',
                  maxWidth: '140px',
                }}
                options={[
                  { label: '提前离校已请假', value: 'earlyPermission' },
                  { label: '未到未请假', value: 'noShowLeave' },
                ]}
                onChange={(value: string) => {
                  onUpdateRecord?.(record.id, value)
                }}
              />
            )
          }
        }
      },
    },
    isSeven && {
      title: '未到课原因',
      dataIndex: 'isLeave',
      key: 'isLeave',
      align: 'center',
      render: (_: string, record: any) => {
        return record.isRealTo !== '出勤' && record?.whetherJoin === WhetherJoinEnum['已参与'] ? (
          <Select
            style={{
              width: '100%',
              maxWidth: '140px',
            }}
            options={[
              { label: '离校已请假', value: 'earlyPermission' },
              { label: '未到未请假', value: 'noShowLeave' },
            ]}
            onChange={(value: string) => {
              onUpdateRecord?.(record.id, value)
            }}
          />
        ) : (
          '-'
        )
      },
    },
  ].filter(Boolean)
  if (option) {
    arr.push({
      title: '到课',
      dataIndex: 'isRealTo',
      key: 'isRealTo',
      width: 80,
      align: 'center',
      render: (text: string, record: any) => {
        return (
          <div key={record.id}>
            <SwitchItem realTo={record.isRealTo} record={record} onSwitchItem={onSwitchItem} />
          </div>
        )
      },
    })
  }
  return arr
}

export { getColums }
