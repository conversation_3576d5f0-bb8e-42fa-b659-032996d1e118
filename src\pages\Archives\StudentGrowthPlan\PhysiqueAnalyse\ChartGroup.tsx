import { Col, Row } from 'antd'
import React, { useEffect, useState } from 'react'
import { Pie, Column } from '@ant-design/plots'
import { pieConfig, columnConfig } from '../components/chartConfig'
import { envjudge } from '@/utils'

const ChartGroup = ({ type, chartData }: { type: string; chartData: any }) => {
  const env_screen = envjudge()
  const [pieChart, setPieChart] = useState<any>([])
  const [numChart, setNumChart] = useState<any>([])
  const [rateChart, setRateChart] = useState<any>([])
  const asyncFetch = () => {
    const { analysis, data } = chartData
    const newNum = []
    const newRate = []
    for (let i = 0; i < data?.length; i++) {
      newNum.push(
        {
          name: type === '班级' ? data[i]?.className : data[i]?.gradeName,
          type: '优秀人数',
          value: data[i]?.slzcrs,
        },
        {
          name: type === '班级' ? data[i]?.className : data[i]?.gradeName,
          type: '良好人数',
          value: data[i]?.boyblrs,
        },
        {
          name: type === '班级' ? data[i]?.className : data[i]?.gradeName,
          type: '及格人数',
          value: data[i]?.girlblrs,
        },
        {
          name: type === '班级' ? data[i]?.className : data[i]?.gradeName,
          type: '不及格人数',
          value: data[i]?.girlblrs,
        },
      )
      newRate.push(
        {
          name: type === '班级' ? data[i]?.className : data[i]?.gradeName,
          type: '及格率',
          value: data[i]?.zrs ? Number(((data[i]?.slzcrs / data[i]?.zrs) * 100).toFixed(2)) : 0,
        },
        {
          name: type === '班级' ? data[i]?.className : data[i]?.gradeName,
          type: '不及格率',
          value: data[i]?.zrs ? Number(((data[i]?.slblrs / data[i]?.zrs) * 100).toFixed(2)) : 0,
        },
      )
    }
    setRateChart(newRate)
    setNumChart(newNum)
    setPieChart([
      {
        type: '优秀人数',
        value: analysis?.slzcrs,
      },
      {
        type: '良好人数',
        value: analysis?.boyblrs,
      },
      {
        type: '及格人数',
        value: analysis?.girlblrs,
      },
      {
        type: '不及格人数',
        value: analysis?.girlblrs,
      },
    ])
  }
  useEffect(() => {
    if (chartData) {
      asyncFetch()
    }
  }, [chartData])

  return (
    <div>
      <Row>
        <Col xs={24} sm={24} md={6} lg={6}>
          <div
            style={{
              height: '260px',
            }}
          >
            <Pie
              {...pieConfig}
              data={pieChart}
              statistic={{
                title: false,
                content: {
                  style: {
                    whiteSpace: 'pre-wrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    fontSize: 14,
                  },
                  content: chartData?.analysis?.zrs + '\n体检总人数',
                },
              }}
            />
          </div>
        </Col>
        <Col xs={24} sm={24} md={9} lg={9}>
          <div
            style={{
              height: '260px',
            }}
          >
            <Column {...columnConfig} data={numChart} />
          </div>
        </Col>
        {!env_screen?.includes('mobile') && (
          <Col xs={24} sm={24} md={9} lg={9}>
            <div
              style={{
                height: '260px',
              }}
            >
              <Column
                {...columnConfig}
                data={rateChart}
                yAxis={{
                  label: {
                    formatter: (value) => `${value}%`, // 自定义 Y 轴展示的字符
                  },
                }}
                tooltip={{
                  formatter: (datum) => ({ name: `${datum.type}`, value: `${datum.value}%` }), // 自定义 tooltip 展示的字符
                }}
                legend={{
                  position: 'right-bottom',
                }}
              />
            </div>
          </Col>
        )}
      </Row>
    </div>
  )
}

export default ChartGroup
