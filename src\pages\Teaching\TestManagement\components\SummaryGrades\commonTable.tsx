import React from 'react'
import { Table } from 'antd'
import styles from './index.less'

interface CommonTableProps {
  data: any[]
  type: string
}

const CommonTable: React.FC<CommonTableProps> = ({ data, type }) => {
  const schoolColumns: any = [
    {
      title: '年级',
      dataIndex: 'gradeName',
      align: 'center',
      key: 'gradeName',
    },
    {
      title: '应考人数',
      align: 'center',
      dataIndex: 'yyrs',
      key: 'yyrs',
    },
    {
      title: '实考人数',
      dataIndex: 'skrs',
      align: 'center',
      key: 'skrs',
    },
    {
      title: '双率',
      children: [
        {
          title: '优秀人数',
          align: 'center',
          dataIndex: 'yx',
          key: 'yx',
        },
        {
          title: '优秀率 % ',
          dataIndex: 'yxl',
          align: 'center',
          key: 'yxl',
        },
        {
          title: '合格人数',
          dataIndex: 'hg',
          align: 'center',
          key: 'hg',
        },
        {
          title: '合格率 % ',
          dataIndex: 'hgl',
          align: 'center',
          key: 'hgl',
        },
      ],
    },
    {
      title: '平均分',
      dataIndex: 'pjf',
      align: 'center',
      key: 'pjf',
    },
    {
      title: '优秀',
      dataIndex: 'yx',
      align: 'center',
      key: 'yx_duplicate',
    },
    {
      title: '优秀占比 % ',
      dataIndex: 'yxl',
      align: 'center',
      key: 'yxl_duplicate',
    },
    {
      title: '良好',
      align: 'center',
      dataIndex: 'lh',
      key: 'lh',
    },
    {
      title: '良好占比 % ',
      align: 'center',
      dataIndex: 'lhl',
      key: 'lhl',
    },
    {
      title: '合格',
      align: 'center',
      dataIndex: 'hg',
      key: 'hg_duplicate',
    },
    {
      title: '合格占比 % ',
      align: 'center',
      dataIndex: 'hgl',
      key: 'hgl_duplicate',
    },
    {
      title: '不合格',
      align: 'center',
      dataIndex: 'bhg',
      key: 'bhg',
    },
    {
      title: '不合格占比 % ',
      align: 'center',
      dataIndex: 'bhgl',
      key: 'bhgl',
    },
    {
      title: '缺考人数',
      align: 'center',
      dataIndex: 'qkrs',
      key: 'qkrs',
    },
  ]

  const gradeColumns: any = [
    {
      title: '班级',
      dataIndex: 'className',
      align: 'center',
      key: 'className',
    },
    {
      title: '任课教师',
      dataIndex: 'teacherName',
      align: 'center',
    },
    {
      title: '应考人数',
      align: 'center',
      dataIndex: 'ykrs',
      key: 'ykrs',
    },
    {
      title: '实考人数',
      dataIndex: 'skrs',
      align: 'center',
      key: 'skrs',
    },
    {
      title: '双率',
      children: [
        {
          title: '优秀人数',
          align: 'center',
          dataIndex: 'zrs',
          key: 'zrs',
        },
        {
          title: '优秀率 % ',
          dataIndex: 'ratio_1',
          align: 'center',
          key: 'ratio_1',
        },
        {
          title: '合格人数',
          dataIndex: 'rs_4',
          align: 'center',
          key: 'hg',
        },
        {
          title: '合格率 % ',
          dataIndex: 'ratio_4',
          align: 'center',
          key: 'hgl',
        },
      ],
    },
    {
      title: '平均分',
      dataIndex: 'pjf',
      align: 'center',
      key: 'pjf',
    },
    {
      title: '优秀',
      dataIndex: 'zrs',
      align: 'center',
      key: 'zrs',
    },
    {
      title: '优秀占比 % ',
      dataIndex: 'ratio_1',
      align: 'center',
      key: 'ratio_1',
    },
    {
      title: '良好',
      align: 'center',
      dataIndex: 'rs_3',
      key: 'rs_3',
    },
    {
      title: '良好占比 % ',
      align: 'center',
      dataIndex: 'ratio_3',
      key: 'ratio_3',
    },
    {
      title: '合格',
      align: 'center',
      dataIndex: 'rs_4',
      key: 'rs_4',
    },
    {
      title: '合格占比 % ',
      align: 'center',
      dataIndex: 'ratio_4',
      key: 'ratio_4',
    },
    {
      title: '不合格',
      align: 'center',
      dataIndex: 'rs_5',
      key: 'rs_5',
    },
    {
      title: '不合格占比 % ',
      align: 'center',
      dataIndex: 'ratio_5',
      key: 'bhgl',
    },
    {
      title: '缺考人数',
      align: 'center',
      dataIndex: 'qkrs',
      key: 'qkrs',
    },
  ]

  const classColumns: any = [
    {
      title: '班级',
      dataIndex: 'className',
      align: 'center',
      key: 'gradeName',
    },
    {
      title: '应考人数',
      align: 'center',
      dataIndex: 'yyrs',
      key: 'yyrs',
    },
    {
      title: '实考人数',
      dataIndex: 'skrs',
      align: 'center',
      key: 'skrs',
    },
    {
      title: '双率',
      children: [
        {
          title: '优秀人数',
          align: 'center',
          dataIndex: 'yx',
          key: 'yx',
        },
        {
          title: '优秀率 % ',
          dataIndex: 'yxl',
          align: 'center',
          key: 'yxl',
        },
        {
          title: '合格人数',
          dataIndex: 'hg',
          align: 'center',
          key: 'hg',
        },
        {
          title: '合格率 % ',
          dataIndex: 'hgl',
          align: 'center',
          key: 'hgl',
        },
      ],
    },
    {
      title: '平均分',
      dataIndex: 'pjf',
      align: 'center',
      key: 'pjf',
    },
    {
      title: '优秀',
      dataIndex: 'yx',
      align: 'center',
      key: 'yx_duplicate',
    },
    {
      title: '优秀占比 % ',
      dataIndex: 'yxl',
      align: 'center',
      key: 'yxl_duplicate',
    },
    {
      title: '良好',
      align: 'center',
      dataIndex: 'lh',
      key: 'lh',
    },
    {
      title: '良好占比 % ',
      align: 'center',
      dataIndex: 'lhl',
      key: 'lhl',
    },
    {
      title: '合格',
      align: 'center',
      dataIndex: 'hg',
      key: 'hg_duplicate',
    },
    {
      title: '合格占比 % ',
      align: 'center',
      dataIndex: 'hgl',
      key: 'hgl_duplicate',
    },
    {
      title: '不合格',
      align: 'center',
      dataIndex: 'bhg',
      key: 'bhg',
    },
    {
      title: '不合格占比 % ',
      align: 'center',
      dataIndex: 'bhgl',
      key: 'bhgl',
    },
    {
      title: '缺考人数',
      align: 'center',
      dataIndex: 'qkrs',
      key: 'qkrs',
    },
  ]
  return (
    <Table
      bordered
      className={styles.commonTabel}
      columns={type === 'school' ? schoolColumns : type === 'grade' ? gradeColumns : classColumns}
      dataSource={data}
    />
  )
}
export default CommonTable
