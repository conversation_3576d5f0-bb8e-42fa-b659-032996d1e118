/** 曲江流饮 */
import React, { useEffect, useState, useCallback } from 'react'
import { Drawer } from 'antd'
import { secondGrade, secondGradeExpressions } from '../data'
import oneLeft from '../../assets/images/oneLeft.png'
import oneNext from '../../assets/images/oneNext.png'
import Common from '../common'
import Audio from '../Audio'
import one01 from '../Audio/one01.mp3'
import styles from './index.less'
import { useLocation } from 'umi'
import ThroughForest from '../ThroughForest'
import IdentifyFlag from '../IdentifyFlag'

const SwimFlowers: React.FC<{
  currentStudent: any
  data: boolean
  setCurrentStudent: any
}> = ({ currentStudent, setCurrentStudent, data }) => {
  const { pathname } = useLocation()
  const [readList, setReadList] = useState<string[]>([])
  const [nextOpen, setNextOpen] = useState<boolean>(false)
  const [topicText, setTopicText] = useState<string[]>([])
  const [isInitialized, setIsInitialized] = useState<boolean>(false)
  const [audioSrc, setAudioSrcFun] = useState<string>(one01)
  const [commonText, setCommonText] = useState<{
    /** 是否结束 */
    status: boolean
    message: string
  }>({
    status: false,
    message: '',
  })

  const [showNextButton, setShowNextButton] = useState<boolean>(false)
  const [grade, setGrade] = useState<boolean>(true)

  // 打乱顺序，每次取8个
  const getRandomItems = (array: string[], numItems: number) => {
    const shuffledArray = array.slice(0).sort(() => 0.5 - Math.random())
    return shuffledArray.slice(0, numItems)
  }

  // 初始化题目列表
  const initializeTopicText = useCallback(() => {
    setAudioSrcFun(one01)
    setCommonText({
      status: false,
      message: `${currentStudent?.name} 请读出上面的词语。`,
    })
    return getRandomItems(secondGradeExpressions, 8)
  }, [currentStudent?.name])

  useEffect(() => {
    if (currentStudent && currentStudent.classes && currentStudent.classes[0]) {
      const gradeNames = currentStudent.classes[0].grade_name
      setGrade(gradeNames === '一年级' ? true : false)
      const newTopicText = initializeTopicText()
      setTopicText(newTopicText)
      setIsInitialized(true) // 设置初始化标志
    }
  }, [currentStudent, initializeTopicText])

  useEffect(() => {
    if (isInitialized && readList.length === topicText.length) {
      setTimeout(() => {
        setNextOpen(true)
      }, 1000)
    }
  }, [readList, topicText.length, isInitialized])

  /** 清除已选 */
  useEffect(() => {
    if (data) {
      setReadList([])
      setShowNextButton(false)
    }
  }, [data])

  return (
    <>
      <div className={styles.videoBox}>
        {pathname !== '/entertainment/gameTwo' ? (
          <a
            className={styles.retreat}
            onClick={() => {
              setCurrentStudent(false)
            }}
          >
            <img width={70} src={oneLeft} alt="" />
          </a>
        ) : (
          <></>
        )}
        <div className={styles.titleBox}>
          <h1 className={styles.title}>欢迎来到长安八景“骊山晚照”</h1>
          <div className={styles.described}>——夏之梦</div>
        </div>
        <div
          className={styles.topicBox}
          style={{
            margin: pathname !== '/entertainment/gameTwo' ? '11.9955vh auto' : '21.9955vh auto',
          }}
        >
          {topicText.map((item, i) => {
            const isRead = readList.includes(item)
            return (
              <span
                key={i}
                className={`${styles.topic} ${isRead ? styles.read : ''}`}
                onClick={() => {
                  if (!isRead) {
                    setReadList([...readList, item])
                  }
                }}
              >
                {item}
              </span>
            )
          })}
        </div>
        {showNextButton && (
          <img
            src={oneNext}
            className={styles.nextButton}
            onClick={() => {
              setNextOpen(true)
            }}
          />
        )}
      </div>
      <Common currentHello={commonText.message} />
      <Drawer
        width={'100vw'}
        open={nextOpen}
        onClose={() => setNextOpen(false)}
        headerStyle={{ display: 'none' }}
        bodyStyle={{ padding: 0 }}
        destroyOnClose
      >
        {grade ? (
          <ThroughForest currentStudent={currentStudent} />
        ) : (
          <IdentifyFlag currentStudent={currentStudent} />
        )}
      </Drawer>
      <Audio src={audioSrc} />
    </>
  )
}

export default SwimFlowers
