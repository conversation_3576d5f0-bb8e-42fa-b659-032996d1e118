import type { ProColumns } from '@ant-design/pro-components'
import { Switch, Tag } from 'antd'

export enum WhetherJoinEnum {
  '未确认' = 0,
  '已参与' = 1,
  '不参与' = 2,
}

const columns: ({
  isAdmin,
  handleEvents,
}: {
  isAdmin: boolean
  handleEvents: (id: string, value: Omit<WhetherJoinEnum, '未确认'>) => void
}) => ProColumns[] = ({
  isAdmin,
  handleEvents,
}: {
  isAdmin: boolean
  handleEvents?: (id: string, value: Omit<WhetherJoinEnum, '未确认'>) => void
}) => {
  return [
    {
      title: '序号',
      valueType: 'index',
      key: 'index',
      width: 48,
    },
    {
      title: '学生名称',
      dataIndex: 'studentName',
      key: 'studentName',
      align: 'center',
    },
    {
      title: '年级',
      dataIndex: 'gradeName',
      key: 'gradeName',
      align: 'center',
    },
    {
      title: '班级',
      dataIndex: 'className',
      key: 'className',
      align: 'center',
    },
    {
      title: '开始日期',
      dataIndex: 'startDate',
      key:'startDate',
      align: 'center',
    },
    {
      title: '结束日期',
      dataIndex: 'endDate',
      key: 'endDate',
      align: 'center',
    },
    {
      title: '是否参与课后服务',
      dataIndex: 'whetherJoin',
      key: 'whetherJoin',
      align: 'center',
      valueType: 'select',
      filters: true,
      valueEnum: {
        已参与: {
          text: '已参与',
        },
        不参与: {
          text: '不参与',
        },
        未确认: {
          text: '未确认',
        },
      },
      render: (_dom: React.ReactNode, record: any) => {
        const whetherJoin = record?.whetherJoin
        if (whetherJoin === WhetherJoinEnum['已参与']) {
          return <Tag color="#87d068">已参与</Tag>
        } else if (whetherJoin === WhetherJoinEnum['不参与']) {
          return <Tag color="#108ee9">不参与</Tag>
        } else if (whetherJoin === WhetherJoinEnum['未确认']) {
          return <Tag color="#aaaaaa">未确认</Tag>
        } else {
          return <Tag color="#f50">未知</Tag>
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 200,
      render: (_dom: any, record: { whetherJoin: WhetherJoinEnum | undefined; id: string }) => {
        if (isAdmin) {
          return <>--</>
        } else {
          return record?.whetherJoin !== undefined ? (
            <Switch
              key="edit"
              checked={record.whetherJoin === WhetherJoinEnum['已参与']}
              checkedChildren="已参与"
              unCheckedChildren="不参与"
              onChange={(checked) => {
                handleEvents?.(
                  record.id,
                  checked ? WhetherJoinEnum['已参与'] : WhetherJoinEnum['不参与'],
                )
              }}
            />
          ) : (
            '用户未确认'
          )
        }
      },
    },
  ]
}

export default columns
