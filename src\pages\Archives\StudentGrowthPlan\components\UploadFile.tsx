import { Mo<PERSON>, Spin, Upload, But<PERSON>, Badge, message } from 'antd'
import React, { useRef, useState } from 'react'
import styles from './index.less'
import { UploadOutlined } from '@ant-design/icons'
import { useModel } from 'umi'

type propsType = {
  screen_id?: string
  onRefresh: () => void
}
const UploadGrade = (props: propsType) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false)
  // 导入
  const [Loading, setLoading] = useState<boolean>(false)
  const { initialState } = useModel('@@initialState')

  const { schoolInfo } = initialState || {}
  const { screen_id, onRefresh } = props
  const [hrefs, setHrefs] = useState<string>()
  const refA = useRef<any>()
  // const [fileList, setFileList] = useState<UploadFile[]>()

  // 导入
  const UploadProps: any = {
    name: 'xlsx',
    action: `/edu_api/upload/student_growth/${screen_id}`,

    // fileList,
    beforeUpload(file: any) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const isType =
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel'
      if (!isType) {
        message.error('请上传正确表格文件!')
        return Upload.LIST_IGNORE
      }
      if (!isLt2M) {
        message.error('文件大小不能超过2M')
        return Upload.LIST_IGNORE
      }
      return true
    },
    onChange(info: {
      file: { status: string; name: any; response: any; error: any }
      fileList: any
      event: any
    }) {
      console.log('info', info)
      if (info.file.error === 'Created' || info.file.status === 'done') {
        const res = info.file.response
        console.log(res)

        if (res.errCode) {
          message.error(`导入失败: ${res.msg || res?.error || '请联系管理员或稍后再试'}`)
          info.file.status = 'error'
          info.file.response = res.msg || res.error
        } else {
          if (res?.data?.errorData?.length) {
            Modal.warning({
              title: `以下数据导入失败`,
              content: (
                <table className={styles.errorTable}>
                  <tr>
                    <th>学生姓名</th>
                    <th>失败原因</th>
                  </tr>
                  {res?.data?.errorData?.map(
                    (value: { studentName?: string; message?: string }) => {
                      return (
                        <tr key={value.message}>
                          <td>{value?.studentName}</td>
                          <td>{value?.message}</td>
                        </tr>
                      )
                    },
                  )}
                </table>
              ),
              className: styles.modalTable,
            })
          } else {
            onRefresh?.()
            message.success('导入成功')
            setModalVisible(false)
          }
        }
      }
    },
  }

  return (
    <div style={{ display: 'inline-block' }}>
      <Button
        onClick={() => {
          setModalVisible(true)
        }}
      >
        {' '}
        导入数据
      </Button>
      <Modal
        title={<span>导入数据</span>}
        destroyOnClose
        width="35vw"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          onRefresh?.()
          // setFileList([])
        }}
        footer={null}
        centered
        maskClosable={false}
        bodyStyle={{
          maxHeight: '65vh',
          overflowY: 'auto',
        }}
        className={styles.ImportBox}
      >
        <Spin spinning={Loading}>
          <p>
            <Upload {...UploadProps}>
              <Button type="primary" icon={<UploadOutlined />}>
                上传文件
              </Button>
            </Upload>
          </p>
          <div className={styles.messageDiv}>
            <Badge color="#aaa" />
            上传文件仅支持模板格式
            <a
              style={{ marginLeft: '16px' }}
              href={`/edu_api/student_screen/template?enterpriseCode=${schoolInfo?.code}&enterpriseName=${schoolInfo?.name}`}
            >
              下载模板
            </a>
            <br />
            <Badge color="#aaa" />
            确保表格内只有一个工作薄，如果有多个只有第一个会被处理 <br />
            <Badge color="#aaa" />
            重复导入会覆盖更新已有数据
          </div>
        </Spin>
      </Modal>
    </div>
  )
}

export default UploadGrade
