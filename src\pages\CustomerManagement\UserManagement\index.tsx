/* eslint-disable react-hooks/exhaustive-deps */
/*
 * @description: 教师账号管理
 * @author: zpl
 * @Date: 2021-08-14 18:04:21
 * @LastEditTime: 2024-09-10 12:35:13
 * @LastEditors: 朱鹏亮 <EMAIL>
 */
import { controllerSsoUpdateUser } from '@/services/edu-platform-web/sso'
import UserList from '@/components/UserList'
import { controllerSchoolRoleIndex as getAllSchooleRole } from '@/services/edu-platform-web/schoolRoles'
import {
  controllerRepeatCreateStudents,
  controllerRepeatCreateTeachers,
  controllerRepeatGetMemberTemplate,
  controllerRepeatGetStudentTemplate,
} from '@/services/edu-platform-web/ssoRepeat'
import {
  controllerStudentBasicList,
  controllerStudentBasicUpsert,
} from '@/services/edu-platform-web/studentBasic'
import {
  controllerTeacherBasicList,
  controllerTeacherBasicUpsert,
} from '@/services/edu-platform-web/teacherBasic'
import { controllerTeacherRoleBulkCreate } from '@/services/edu-platform-web/teacherRole'
import { getActivedTerm, getXDListUtils } from '@/utils'
import { UploadOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons'
import type { ActionType, ProTableProps } from '@ant-design/pro-components'
import { Badge, Button, message, Modal, Select, Tabs, Upload } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import styles from './index.less'
import tableAlertOptionRender_enterprise from './TableAlertOptionRender'
import AddAccount from './AddAccount'

const { TabPane } = Tabs
type screenType = { label: string; value: string }[]
const UserManagement = () => {
  const { initialState } = useModel('@@initialState')
  const { currentUser, schoolInfo } = initialState || {}
  const actionRef = useRef<ActionType>()
  const [schooleRoleList, setSchooleRoleList] = useState<any>([])
  const [RoleName, setRoleName] = useState<string>()
  // 已配置的学年学期
  const [termCode, setTermCode] = useState<string>()
  // 设置模态框显示状态
  const [modalVisible, setModalVisible] = useState<boolean>(false)
  const [uploadType, setUploadType] = useState<string>('default')
  const [hrefs, setHrefs] = useState<string>()
  const [XDData, setXDData] = useState<screenType>([])
  const [XDName, setXDName] = useState<{
    label: string
    value: string
  }>()
  const refA = useRef<any>()
  useEffect(() => {
    // 获取学段数据
    const data = getXDListUtils(schoolInfo?.section_code)
    const XDArr: screenType = []
    data?.forEach((item: any) => {
      const obj: any = {
        label: item.name,
        value: item.code,
      }
      XDArr.push(obj)
    })
    setXDData(XDArr)
    setXDName(XDArr?.[0])
  }, [])
  const getSchooleRoleList = async () => {
    if (schoolInfo?.code) {
      const res = (await getAllSchooleRole({ enterpriseCode: schoolInfo.code })) as ResType<any>
      if (res?.errCode) {
        message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
      } else {
        if (res?.list?.length) {
          let newDatas: any = {}
          const objs = {}
          res?.list?.forEach((item: any) => {
            const obj = {
              [item.id]: { text: item?.name },
            }
            newDatas = Object.assign(objs, obj)
          })
          setSchooleRoleList(newDatas)
        }
      }
    }
  }
  useEffect(() => {
    if (schoolInfo?.id) {
      ;(async () => {
        const semester = await getActivedTerm(schoolInfo.code!)
        setTermCode(semester?.semester_code)
      })()
    }
  }, [schoolInfo?.id])
  // 获取角色
  useEffect(() => {
    setRoleName('教职工')
    getSchooleRoleList()
  }, [])

  const getListHandler: ProTableProps<UserInfo, UserInfo>['request'] = async (
    tableParams,
    sort,
    filter,
  ) => {
    const { current, pageSize, ...opts } = tableParams
    void current
    void pageSize
    const params: any = {
      sort,
      ...opts,
    }
    params.enterpriseCode = schoolInfo && schoolInfo.code
    params.enterpriseId = schoolInfo?.id || ''
    if (filter?.status?.length === 1) {
      params.status = filter?.status?.[0]
    }
    let res
    if (RoleName === '学生') {
      // 获取学生账号
      res = (await controllerStudentBasicList({
        ...params,
        semesterCode: termCode,
      })) as ResType<any>
    } else {
      // 获取教职工账号
      res = (await controllerTeacherBasicList({ ...params })) as ResType<any>
    }
    if (res?.errCode) {
      message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
      return {
        success: true,
        data: [],
        total: 0,
      }
    } else {
      const { count, list } = res
      return {
        success: true,
        data: list,
        total: count || total,
      }
    }
  }

  const onChange = (key: string) => {
    setRoleName(key)
  }

  const createHandler = async (value: any) => {
    const { username, code, realName, roles, classId, section, sectionCode, ...info } = value
    void roles
    let res
    if (RoleName === '教职工') {
      if (value?.id) {
        const objs = {
          ...info,
          sync: true,
          teacherCode: code,
          teacherName: realName,
          enterpriseCode: currentUser?.corpId || '',
          enterpriseName: schoolInfo?.name || '',
          username: value?.username,
          section,
          sectionCode,
        }
        // 通用启用修改
        const newStatus = info?.status
        await controllerSsoUpdateUser(
          { username },
          {
            username,
            status: newStatus,
            enterpriseCode: currentUser?.corpId || '',
          },
        )
        res = (await controllerTeacherBasicUpsert(objs)) as ResType<any>
      } else {
        const obj = {
          ...info,
          sync: true,
          code: value?.code,
          name: value?.realName,
          username: value?.username,
          work_code: value?.code,
          enterpriseId: schoolInfo?.id || '',
          section,
          sectionCode,
        }
        res = (await controllerRepeatCreateTeachers(obj)) as ResType<any>
      }
      if (res?.errCode) {
        return res
      } else {
        message.success('保存成功')
        if (value?.roles?.length) {
          const roleres = (await controllerTeacherRoleBulkCreate({
            memberId: res?.id || value?.id,
            teacherCode: res?.code || res?.[0]?.teacherCode,
            teacherName: res?.name || res?.[0]?.teacherName,
            enterpriseCode: schoolInfo?.code || '',
            enterpriseName: schoolInfo?.name || '',
            roleIds: value?.roles,
          })) as ResType<any>
          if (roleres?.errCode) {
            message.error(roleres.message || '数据获取失败，请联系管理员或稍后再试')
          }
        }
        // // 同步给custome更新username字段
        // console.log('--------', {
        //   enterpriseCode: schoolInfo?.code || '',
        //   code: res?.code || res?.[0]?.teacherCode,
        // })

        // await updateTeacher(
        //   { enterpriseCode: schoolInfo?.code || '', code: res?.code || res?.[0]?.teacherCode },
        //   { username: value?.username },
        // )
        actionRef?.current?.reload()
        return res
      }
    } else {
      if (value?.id) {
        const objs = {
          ...info,
          sync: true,
          studentCode: code,
          studentName: realName,
          enterpriseCode: currentUser?.corpId || '',
          enterpriseName: schoolInfo?.name || '',
          classId: classId?.value,
          username: value?.username,
        }
        res = (await controllerStudentBasicUpsert(objs)) as ResType<any>
      } else {
        const objs = {
          ...info,
          sync: true,
          code: code,
          username: username,
          name: realName,
          classId: classId?.value,
          enterpriseId: schoolInfo?.id || '',
        }
        res = (await controllerRepeatCreateStudents(objs)) as ResType<any>
      }
      if (res?.errCode) {
        message.error(res.message || '保存失败，请联系管理员或稍后再试')
        return res
      } else {
        message.success('保存成功')
        actionRef?.current?.reload()
        return res
      }
    }
  }
  // 导入
  const UploadProps: any = {
    name: 'xlsx',
    action:
      RoleName === '教职工'
        ? uploadType === 'wechat'
          ? `/edu_api/api/teachers/import/wecom?enterpriseId=${schoolInfo?.id}&sync=${true}`
          : `/edu_api/api/teachers/import?enterpriseId=${schoolInfo?.id}&sync=${true}&section=${
              XDName?.label
            }&sectionCode=${XDName?.value}`
        : uploadType === 'wechat'
        ? `/edu_api/api/students/import/wecom?enterpriseId=${
            schoolInfo?.id
          }&sync=${true}&semesterCode=${termCode}`
        : `/edu_api/api/students/import?enterpriseId=${
            schoolInfo?.id
          }&sync=${true}&semesterCode=${termCode}`,
    beforeUpload(file: any) {
      const isLt2M = file.size / 1024 / 1024 < 2
      const isType =
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel'
      if (!isType) {
        message.error('请上传正确表格文件!')
        return Upload.LIST_IGNORE
      }
      if (!isLt2M) {
        message.error('文件大小不能超过2M')
        return Upload.LIST_IGNORE
      }
      return true
    },
    onChange(info: {
      file: { status: string; name: any; response: any; error: any }
      fileList: any
      event: any
    }) {
      const res = info.file.response
      if (info.file.error === 'Created' || info.file.status === 'done') {
        if (res?.errCode) {
          message.error(`导入失败: ${res.msg || res?.error || '请联系管理员或稍后再试'}`)
          info.file.status = 'error'
          info.file.response = res.msg || res.error
        } else if (res?.data?.length) {
          setTimeout(function () {
            function warning() {
              Modal.warning({
                title: `以下账号导入失败`,
                content: (
                  <table className={styles.errorTable}>
                    <tr>
                      <th>失败原因</th>
                    </tr>
                    {info.file.response.data?.map((value: { message?: string }) => {
                      return (
                        <tr key={value.message}>
                          <td>{value?.message}</td>
                        </tr>
                      )
                    })}
                  </table>
                ),
                className: styles.modalTable,
              })
            }
            warning()
            actionRef.current?.reload()
            setModalVisible(false)
          }, 500)
        } else {
          message.success('导入成功')
          setModalVisible(false)
          actionRef?.current?.reload()
        }
      }
    },
  }
  // const Types = ['教职工', '学生']
  const Types = ['教职工']
  return (
    <div className={styles.accountWrapper}>
      {RoleName ? (
        <>
          <Tabs defaultActiveKey="教职工" onChange={onChange}>
            {Types.map((item) => {
              return <TabPane tab={item} key={item} />
            })}
          </Tabs>{' '}
          <UserList
            key={RoleName}
            actionRef={actionRef}
            title={schoolInfo?.name}
            RoleType={RoleName}
            termCode={termCode}
            createHandler={createHandler}
            schooleRoleList={schooleRoleList}
            filterType="light"
            columnOptions={{
              enterprise: {
                hidden: true,
                search: false,
              },
              roles: {
                width: 200,
                hidden: RoleName === '教职工' ? false : true,
                search: RoleName === '教职工' ? true : false,
              },
            }}
            getListHandler={getListHandler}
            toolBarRender={() => [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  setUploadType('default')
                  setModalVisible(true)
                }}
                style={{ padding: '0 10px', marginRight: '10px' }}
              >
                <VerticalAlignBottomOutlined /> 导入
              </Button>,
              <AddAccount
                key="createAccount"
                termCode={termCode}
                onSave={createHandler}
                RoleType={RoleName}
              />,
            ]}
            tableAlertOptionRender={({
              selectedRowKeys,
              onCleanSelected,
            }: {
              selectedRowKeys: any
              onCleanSelected: any
            }) => {
              return tableAlertOptionRender_enterprise({
                selectedRowKeys,
                enterpriseCode: schoolInfo?.code,
                action: actionRef.current,
                onCleanSelected,
              })
            }}
          />
        </>
      ) : (
        <></>
      )}
      <Modal
        title={`导入${RoleName}`}
        destroyOnClose
        width="35vw"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
        }}
        footer={null}
        centered
        maskClosable={false}
        bodyStyle={{
          maxHeight: '65vh',
          overflowY: 'auto',
        }}
        className={styles.ImportBox}
      >
        <div style={{ marginBlockEnd: 16 }}>
          <label htmlFor="njs">所属学段：</label>
          <Select
            style={{ width: 200 }}
            options={XDData}
            value={XDName}
            placeholder="请选择"
            onChange={(value, option: any) => {
              setXDName(option)
            }}
          />
        </div>
        <p>
          <Upload {...UploadProps}>
            <Button type="primary" icon={<UploadOutlined />}>
              上传文件
            </Button>
          </Upload>
        </p>
        <div className={styles.messageDiv}>
          <Badge color="#aaa" style={{ paddingRight: 5 }} />
          {uploadType === 'default' ? (
            <>
              上传文件仅支持模板格式
              <a
                style={{ marginLeft: '16px' }}
                type="download"
                onClick={async () => {
                  let res
                  if (RoleName === '学生') {
                    res = (await controllerRepeatGetStudentTemplate({
                      enterpriseId: schoolInfo?.id || '',
                      semesterCode: termCode,
                    })) as ResType<any>
                  } else if (RoleName === '教职工') {
                    res = (await controllerRepeatGetMemberTemplate({
                      enterpriseId: schoolInfo?.id || '',
                    })) as ResType<any>
                  }

                  if (res?.errCode) {
                    message.error(res.message || '下载失败，请联系管理员或稍后再试')
                  } else {
                    setHrefs(res?.url)
                    setTimeout(() => {
                      refA?.current.click()
                    }, 500)
                  }
                }}
              >
                下载模板
              </a>
              <a href={hrefs} ref={refA} />
            </>
          ) : (
            <>上传文件仅支持从企业微信管理后台导出的通讯录模板</>
          )}
          <br />
          <Badge color="#aaa" style={{ paddingRight: 5 }} />
          确保表格内只有一个工作薄，如果有多个只有第一个会被处理
          <br />
          <Badge color="#aaa" style={{ paddingRight: 5 }} />
          批量生成的账号默认密码为：Aa@{schoolInfo?.code}，应通知使用者登录后修改个人密码
        </div>
      </Modal>
    </div>
  )
}
export default UserManagement
