import React from 'react'
import { history } from 'umi'
import styles from './index.less'

export type CardProp = {
  item?: boolean
  id: string
  /** 标题 */
  title: string
  /** 描述 */
  descriptions?: string
  /** 跳转链接 */
  url?: string
  /** 背景色 */
  bgcolor: string
  /** logo */
  img: string
}

const Card: React.FC<CardProp> = ({ title, descriptions, url, bgcolor, img }) => {
  return (
    <div
      className={styles.card}
      onClick={async () => {
        history.push(url || '/')
      }}
    >
      <div className={`${styles.left}`} style={{ background: `${bgcolor}` }}>
        <img src={img} alt="" />
      </div>
      <div className={styles.right}>
        <div className={styles.title}>{title}</div>
        <div className={styles.descriptions}>{descriptions || title}</div>
      </div>
    </div>
  )
}
export default Card
