/** 晨午检 */
import { request } from 'umi'

/** 获取晨午检列表  GET /morning_check */
export async function getAfternoonList(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: any[] }>>('/morning_check', {
    method: 'GET',
    params,
  })
}

/** 创建  POST /morning_check */
export async function create(body: any) {
  return request<API.ResType<any>>('/morning_check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  })
}

/** 按ID查询  GET /morning_check/:id */
export async function getById(id: string) {
  return request<API.ResType<any>>(`/morning_check/${id}`, {
    method: 'GET',
  })
}

/** 修改  PUT /morning_check/:id */
export async function update(id: string, body: any) {
  return request<API.ResType<unknown>>(`/morning_check/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  })
}

/** 删除晨午检  DELETE /morning_check/:id */
export async function remove(id: string) {
  return request<API.ResType<unknown>>(`/morning_check/${id}`, {
    method: 'DELETE',
  })
}
/** 获取晨午检记录汇总列表  GET /morning_check/statistic */
export async function getNoonStatisticList(params: Record<string, any>) {
  return request<API.ResType<{ count?: number; list?: any[] }>>('/morning_check/statistic', {
    method: 'GET',
    params,
  })
}
/** 获取缺课人员详细信息  GET /morning_check/absence_info */
export async function getAbsentList(params: Record<string, any>) {
  return request<API.ResType<{ count?: number; list?: any[] }>>('/morning_check/absence_info', {
    method: 'GET',
    params,
  })
}

/** 获取未填报班级列表 GET /morning_check/get_unsubmitted_class */
export async function getUnsubmittedClass(params: Record<string, any>) {
  return request<any>(
    '/morning_check/get_unsubmitted_class',
    {
      method: 'GET',
      params,
    },
  )
}
