/*
 * @Author: wuzhan <EMAIL>
 * @Date: 2022-06-08 15:13:00
 * @LastEditors: <PERSON>ssleLynn
 * @LastEditTime: 2023-12-22 10:28:01
 * @FilePath: \edu_platform_web\src\pages\CustomerManagement\index.tsx
 * @Description: 基础信息管理首页 主要用于判断权限跳转到不同的默认页
 */
import React, { useEffect } from 'react'
import { history, useAccess } from 'umi'
import noAuthImg from '@/assets/noAuth.png';
import NoData from '@/components/NoData';

const Index = () => {
  const access = useAccess()
  const list = [
    { code: '13030100', path: '/archives/studentGrowthPlan/dataInput' },
    { code: '13030200', path: '/archives/studentGrowthPlan/dataAnalyse' },
  ]

  useEffect(() => {
    let flag = true
    list.forEach((item) => {
      if (access?.[item.code]) {
        if (item?.path && flag) {
          flag = false
          history.replace(item.path)
        }
      }
    })
  }, [])

  return (
    <div>
      <div style={{
        marginTop: '15vh',
        textAlign:'center'
      }}>
        <NoData imgUrl={noAuthImg}/>
        <p style={{
          lineHeight: '50px'
        }}>暂无权限，禁止访问</p>
      </div>
    </div>
  );
}

export default Index
