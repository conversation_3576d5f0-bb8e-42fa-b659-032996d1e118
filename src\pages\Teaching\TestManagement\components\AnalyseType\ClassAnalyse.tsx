import React, { useEffect, useRef, useState } from 'react'
import styles from './index.less'
import { <PERSON><PERSON>, Card, Popconfirm, Segmented, Spin, message } from 'antd'
import { ExportOutlined } from '@ant-design/icons'
import type { EditableFormInstance, FormInstance, ProColumns } from '@ant-design/pro-components'
import {
  EditableProTable,
  ProCard,
  ProForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components'
import { getNjName } from '@/utils'
import { useModel } from 'umi'
import AbsentStudent from './AbsentStudent'
import { getClassQualityScore } from '@/services/score-analysis/statistics'
import NoData from '@/components/NoData'
import { submitFormRealTime } from './util'

const ClassAnalyse = ({
  form,
  examId,
  grade,
  setEditStatus,
  type,
  infoData,
}: {
  form: FormInstance<any>
  examId: any
  setEditStatus?: (status?: string) => void
  grade?: any
  type?: string
  infoData?: any
}) => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const [gradeActived, setGradeActived] = useState<string>()
  const [gradeCodeActived, setGradeCodeActived] = useState<string>()
  const [subjectActived, setSubjectActived] = useState<string>()
  const [classActived, setClassActived] = useState<any>()
  const [gradeData, setGradeData] = useState<any[]>([])
  const [classData, setClassData] = useState<any[]>([])
  const [statisticData, setStatisticData] = useState<any>()
  const [subjectSource, setSubjectSource] = useState<any>()
  const [downLoading, setDownLoading] = useState<boolean>(false)
  const [isEditor, setIsEditor] = useState<boolean>(false)
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])
  const [curType, setCurType] = useState<string | undefined>(type)
  const [tableData, setTableData] = useState<any[]>([])
  const getClsData = (curSub: any, index?: number) => {
    const clsData = curSub?.classData?.map((v: any) => {
      return {
        label: v.className,
        value: v.classCode,
      }
    })

    const curClsData = clsData?.find((v: any, i: number) => {
      if (index !== undefined) {
        return i === index
      } else if (classActived) {
        return v.value === classActived
      } else {
        return i === 0
      }
    })
    setClassActived(curClsData?.value)
    setClassData(clsData)
    form.setFieldsValue({
      classCode: curClsData?.value,
      className: curClsData?.label,
    })
  }
  const getSubData = (curGrade: any, index?: number) => {
    const subData = curGrade?.subjectData?.map((v: any) => {
      return {
        label: v.subjectName,
        value: v.subjectCode,
        classData: v.teachers?.[0]?.classes,
      }
    })
    const curSubData = subData?.find((v: any, i: number) => {
      if (index !== undefined) {
        return i === index
      } else if (subjectActived) {
        return v.value === subjectActived
      } else {
        return i === 0
      }
    })
    getClsData(curSubData, index)
    setSubjectActived(curSubData?.value)
    setSubjectSource(subData)
    form.setFieldsValue({
      subjectCode: curSubData?.value,
      subjectName: curSubData?.label,
    })
  }
  const resetForm = () => {
    if (grade?.length) {
      const nowData = grade.filter((v: any) => v.subjects?.length > 0)
      const newData: any = [].map.call(nowData, (v: any) => {
        return {
          label: `${getNjName(v.gradeName, schoolInfo?.section_code)}`,
          value: v.id,
          code: v.gradeCode,
          subjectData: v.subjects,
        }
      })
      const curGradeInfo = newData?.find((v: any, i: number) => {
        if (gradeActived) {
          return v.value === gradeActived
        } else {
          return i === 0
        }
      })
      form.setFieldsValue({
        gradeId: curGradeInfo?.value,
        gradeCode: curGradeInfo?.code,
      })
      getSubData(curGradeInfo)
      setGradeActived(curGradeInfo?.value)
      setGradeCodeActived(curGradeInfo?.code)
      setGradeData(newData)
    }
  }
  const columns: ProColumns<any>[] = [
    {
      title: '试题内容',
      key: 'question',
      dataIndex: 'question',
      valueType: 'textarea',
      width: 200,
      align: 'left',
    },
    {
      title: '出错数',
      dataIndex: 'errNum',
      key: 'errNum',
      valueType: 'text',
      width: 70,
      align: 'center',
    },
    {
      title: '错误典型举例',
      dataIndex: 'example',
      key: 'example',
      valueType: 'textarea',
      fieldProps: { allowClear: false },
      width: 250,
      align: 'left',
      formItemProps: () => {
        return {
          rules: [{ type: 'string', max: 255, message: '内容不能超过255个字符' }],
        }
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
      align: 'center',
      hideInTable: curType === 'check',
      render: (text: any, record: any, _: any, action: any) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id)
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="deleteTable"
          title="删除之后，数据不可恢复，确定要删除吗?"
          onConfirm={async () => {
            if (record.id) {
              const newData = tableData.filter((item) => item?.id !== record?.id)
              form.setFieldValue('classTable', newData)
              setTimeout(async () => {
                const result = await submitFormRealTime(form, currentUser, examId)
                if (result) {
                  setTableData(newData)
                }else{
                  form.setFieldValue('classTable', tableData)
                }
              }, 0)
            }
          }}
          okText="确定"
          cancelText="取消"
          placement="topRight"
        >
          <Button type="link" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ]
  useEffect(() => {
    console.log('infoData', infoData);

    if (infoData) {
      setClassActived(infoData?.classActived)
      setSubjectActived(infoData?.subjectActived)
      setGradeCodeActived(infoData?.gradeCodeActived)
    } else {
      resetForm()
    }
  }, [infoData])
  useEffect(() => {
    if (examId && gradeCodeActived && classActived && subjectActived) {
      form.setFieldsValue({
        id: '',
        classTable: [],
        opinion: '',
        measure: '',
      })
      setTableData([])
      setIsEditor(true)
      setDownLoading(true)
      setEditableRowKeys([])
      ;(async () => {
        const res = await getClassQualityScore({
          examinationId: examId,
          gradeCode: gradeCodeActived,
          classCode: classActived,
          subjectCode: subjectActived,
        })
        if (res?.errCode) {
          message.warning(res?.message || res?.msg || '数据获取失败，请联系管理员或稍后再试！')
          setDownLoading(false)
        } else {
          const { reportStatistic, ...info } = res
          if (reportStatistic) {
            const { reportStatisticsDetails, status, subjectCode, classCode, gradeId, ...rest } =
              reportStatistic
            const data = reportStatisticsDetails
              ?.filter((v: any) => v.content)
              .map((item: any) => {
                return item.content
              })
            const sortData = data?.sort((a: any, b: any) => parseInt(a.index) - parseInt(b.index))
            setClassActived(classCode)
            setGradeActived(gradeId)
            setSubjectActived(subjectCode)
            if (status === '已发布') {
              setCurType('check')
              setEditStatus?.('check')
            } else {
              setEditStatus?.('edit')
              setCurType('edit')
            }
            setTableData(sortData || [])
            form.setFieldsValue({
              ...rest,
              subjectCode,
              classCode,
              gradeId,
              classTable: sortData,
              type: '班级',
            })
          } else {
            setTableData([])
            form.setFieldsValue({
              type: '班级',
              edit_teacherCode: currentUser?.userCode,
              edit_teacherName: currentUser?.realName,
              classTable: [],
              examinationId: examId,
            })
            setEditStatus?.('edit')
            setCurType('edit')
          }
          setStatisticData({
            ...info,
          })
          setDownLoading(false)
        }
      })()
    }
  }, [
    gradeCodeActived,
    currentUser,
    classActived,
    subjectActived,
    examId,
    form,
    setEditStatus,
    type,
  ])
  return (
    <Spin tip="数据获取中，请稍候..." spinning={downLoading}>
      {isEditor ? (
        <>
          {!infoData && (
            <div className={styles.topBar}>
              {curType === 'check' && (
                <a
                  className={styles.exportBtn}
                  href={`/api_score/download/statistic_by_class?examinationId=${examId}&gradeCode=${gradeCodeActived}&classCode=${classActived}&subjectCode=${subjectActived}`}
                >
                  <ExportOutlined />
                  导出
                </a>
              )}
              <ProFormText name="type" hidden />
              <ProFormText name="gradeCode" hidden />
              <ProFormText name="classTable" hidden />
              <ProForm.Item label="年级：" name="gradeId">
                {gradeData?.length ? (
                  <Segmented
                    className={styles.gradeSegmented}
                    value={gradeActived}
                    options={gradeData}
                    onChange={(value) => {
                      setGradeActived(value as string)
                      const curData = gradeData?.find((v: any) => v.value === value)
                      if (curData) {
                        setGradeCodeActived(curData?.code)
                        form.setFieldsValue({
                          gradeCode: curData?.code,
                        })
                        getSubData(curData, 0)
                      }
                    }}
                  />
                ) : (
                  '暂无年级'
                )}
              </ProForm.Item>
              <ProForm.Item label="学科：" name="subjectCode">
                {subjectSource?.length ? (
                  <Segmented
                    className={styles.gradeSegmented}
                    value={subjectActived}
                    options={subjectSource}
                    onChange={(value) => {
                      setSubjectActived(value as string)
                      const curData = subjectSource?.find((v: any) => v.value === value)
                      if (curData) {
                        form.setFieldValue('subjectName', curData?.label)
                        getClsData(curData)
                      }
                    }}
                  />
                ) : (
                  '暂无学科'
                )}
              </ProForm.Item>
              <ProForm.Item label="班级：" name="classCode">
                {classData?.length ? (
                  <Segmented
                    className={styles.gradeSegmented}
                    value={classActived}
                    options={classData}
                    onChange={(value) => {
                      setClassActived(value as string)
                      const curData = classData?.find((v: any) => v.value === value)
                      if (curData) {
                        form.setFieldValue('className', curData?.label)
                      }
                    }}
                  />
                ) : (
                  '暂无班级'
                )}
              </ProForm.Item>
            </div>
          )}
          <div
            className={styles.middleBar}
            style={
              curType === 'edit'
                ? {}
                : {
                    marginTop: 50,
                  }
            }
          >
            {statisticData?.qks?.length > 0 && <AbsentStudent qks={statisticData?.qks} />}
            <table className={styles.statisticTable}>
              <thead>
                <tr>
                  <th rowSpan={2}>班级人数</th>
                  <th rowSpan={2}>考试人数</th>
                  <th rowSpan={2}>总分</th>
                  <th rowSpan={2}>平均分</th>
                  <th rowSpan={2}>最高分数</th>
                  <th rowSpan={2}>最低分数</th>
                  <th colSpan={2}>100分</th>
                  <th colSpan={2}>85-99分</th>
                  <th colSpan={2}>70-84分</th>
                  <th colSpan={2}>60分以上</th>
                  <th colSpan={2}>不及格</th>
                </tr>
                <tr>
                  <th>人数</th>
                  <th>率（%）</th>
                  <th>人数</th>
                  <th>率（%）</th>
                  <th>人数</th>
                  <th>率（%）</th>
                  <th>人数</th>
                  <th>率（%）</th>
                  <th>人数</th>
                  <th>率（%）</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{statisticData?.yyrs}</td>
                  <td>{statisticData?.ksrs}</td>
                  <td>{statisticData?.zf}</td>
                  <td>{statisticData?.pjf}</td>
                  <td>{statisticData?.zgf}</td>
                  <td>{statisticData?.zdf}</td>
                  <td>{statisticData?.rs_1}</td>
                  <td>{statisticData?.ratio_1}</td>
                  <td>{statisticData?.rs_2}</td>
                  <td>{statisticData?.ratio_2}</td>
                  <td>{statisticData?.rs_3}</td>
                  <td>{statisticData?.ratio_3}</td>
                  <td>{statisticData?.rs_4}</td>
                  <td>{statisticData?.ratio_4}</td>
                  <td>{statisticData?.rs_5}</td>
                  <td>{statisticData?.ratio_5}</td>
                </tr>
              </tbody>
            </table>
            <Card title="问题分析" bordered={false} style={{ width: '100%' }}>
              <EditableProTable
                rowKey="id"
                // name="classTable"
                recordCreatorProps={
                  curType === 'edit'
                    ? {
                        creatorButtonText: '新增问题分析',
                        type: 'primary',
                        ghost: true,
                        position: 'bottom',
                        record: () => ({
                          id: (Math.random() * 1000000).toFixed(0),
                        }),
                      }
                    : false
                }
                columns={columns}
                value={tableData}
                onChange={setTableData}
                editable={{
                  type: 'single',
                  editableKeys,
                  actionRender: (row, config, dom) => {
                    return [dom.save, dom.cancel]
                  },
                  onChange: setEditableRowKeys,
                  onSave: async (key: any, record: any) => {
                    const newData = [...tableData]
                    const isExist = newData?.findIndex((v) => v.id === key)
                    if (isExist === -1) {
                      newData.push(record)
                    } else {
                      newData[isExist] = record
                    }
                    form.setFieldValue('classTable', newData)
                    setTimeout(async () => {
                      const result = await submitFormRealTime(form, currentUser, examId)
                      if (!result) {
                        setEditableRowKeys([key])
                      }
                    }, 0)
                  },
                }}
              />
            </Card>
            <ProCard split="vertical">
              <ProCard title="对试题的具体意见或建议" colSpan="50%">
                <ProFormTextArea
                  name="opinion"
                  placeholder="请输入"
                  readonly={curType === 'check'}
                  fieldProps={{
                    rows: 4,
                    maxLength: 255,
                    showCount: true,
                    onBlur: () => {
                      setTimeout(() => {
                        submitFormRealTime(form, currentUser, examId)
                      }, 0)
                    },
                  }}
                />
              </ProCard>
              <ProCard title="对命题的整体评议">
                <ProFormTextArea
                  name="measure"
                  placeholder="请输入"
                  readonly={curType === 'check'}
                  fieldProps={{
                    rows: 4,
                    maxLength: 255,
                    showCount: true,
                    onBlur: () => {
                      setTimeout(() => {
                        submitFormRealTime(form, currentUser, examId)
                      }, 0)
                    },
                  }}
                />
              </ProCard>
            </ProCard>

            <ProCard title="本次评价结果分析">
              <ProFormTextArea
                name="resultAnlysis"
                placeholder="请输入"
                readonly={curType === 'check'}
                fieldProps={{
                  rows: 4,
                  maxLength: 1000,
                  showCount: true,
                  onBlur: () => {
                    setTimeout(() => {
                      submitFormRealTime(form, currentUser, examId)
                    }, 0)
                  },
                }}
              />
            </ProCard>

            <ProCard title="存在问题及原因分析（不局限于本次评价活动，可结合日常教学进行分析）">
              <ProFormTextArea
                name="causesAnlysis"
                placeholder="请输入"
                readonly={curType === 'check'}
                fieldProps={{
                  rows: 4,
                  maxLength: 1000,
                  showCount: true,
                  onBlur: () => {
                    setTimeout(() => {
                      submitFormRealTime(form, currentUser, examId)
                    }, 0)
                  },
                }}
              />
            </ProCard>

            <ProCard title="改进措施及努力方向">
              <ProFormTextArea
                name="solutionAnlysis"
                placeholder="请输入"
                readonly={curType === 'check'}
                fieldProps={{
                  rows: 4,
                  maxLength: 1000,
                  showCount: true,
                  onBlur: () => {
                    setTimeout(() => {
                      submitFormRealTime(form, currentUser, examId)
                    }, 0)
                  },
                }}
              />
            </ProCard>
          </div>
        </>
      ) : (
        <div
          style={{
            marginBlockStart: '15vh',
          }}
        >
          <NoData desc="本次考试不涉及您教授的课程或班级，无需填写" />
        </div>
      )}
    </Spin>
  )
}

export default ClassAnalyse
