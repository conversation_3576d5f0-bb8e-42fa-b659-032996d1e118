import { envjudge, getQueryObj } from '@/utils'
import { Button, Empty, Space, Tag, Image } from 'antd'
import { LeftOutlined } from '@ant-design/icons'

import moment from 'moment'
import { useEffect, useState } from 'react'
import styles from './index.less'
import { useLocation, useModel } from 'umi'
import { ProDescriptions } from '@ant-design/pro-components'
import { floatBottomStyle } from '@/constant'
import { diseaseStudentShow } from '@/services/edu-platform-web/disease_student'
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const Detail = () => {
  const { id } = getQueryObj()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo } = initialState || {}
  const env_screen = envjudge()
  const { pathname }: any = useLocation()
  const [visible, setVisible] = useState<{
    show: boolean
    img?: string
  }>({
    show: false,
  })

  const [data, setData] = useState<any>()
  const getData = async () => {
    const result = await diseaseStudentShow(id!)
    if (!result?.errCode) {
      setData(result)
    }
  }

  useEffect(() => {
    if (id) {
      getData()
    }
  }, [id])

  if (!id) {
    return <Empty style={{ marginTop: '35vh' }} description="非法访问" />
  }

  return (
    <div className={styles.detailWrapper}>
      <div>
        {!env_screen.includes('mobile') && (
          <Button
            type="primary"
            style={{
              marginBlockEnd: 24,
            }}
            onClick={() => {
              history.go(-1)
            }}
          >
            <LeftOutlined />
            返回上一页
          </Button>
        )}
        <div className={styles.topCon}>
          <div className={styles.title}>{data?.studentName}因病追踪登记</div>
          <div className={styles.desc}>{schoolInfo?.name}</div>
          <Space>
            <Tag color="blue">
              {data?.gradeName}
              {data?.className}
            </Tag>
            <Tag>{data?.studentCode}</Tag>
            <Tag color={data?.gender === '男' ? 'processing' : 'red'}>{data?.gender}</Tag>
            <Tag>{data?.age}岁</Tag>
          </Space>
        </div>

        <div className={styles.middleCon}>
          <div className={styles.desc}>登记信息</div>
          <div className={styles.flowCon}>
            <ProDescriptions column={env_screen.includes('mobile') ? 1 : 2}>
              <ProDescriptions.Item valueType="text" label="家长姓名">
                {data?.parentName}
              </ProDescriptions.Item>
              <ProDescriptions.Item valueType="text" label="联系方式">
                {data?.parentPhone}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="登记人" valueType="text">
                {data?.teacherName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="登记日期" valueType="text">
                {moment(data?.record_date).format('YYYY-MM-DD')}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="发病日期" valueType="text">
                {moment(data?.disease_date).format('YYYY-MM-DD')}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="缺勤天数" valueType="text">
                {data?.absenceNumber}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="是否就诊" valueType="text">
                {data?.is_diagnose ? '是' : '否'}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="是否内宿" valueType="text">
                {data?.is_resident ? '是' : '否'}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="主要症状" valueType="text" span={2}>
                {data?.disease_symptoms?.map((v: any) => (
                  <Tag key={v.name}>{v.name}</Tag>
                ))}
              </ProDescriptions.Item>
              <ProDescriptions.Item span={2} label="排查原因" valueType="text">
                {data?.reason}
              </ProDescriptions.Item>
              <ProDescriptions.Item span={2} label="排查结果" valueType="text">
                {data?.check_result}
              </ProDescriptions.Item>
            </ProDescriptions>
          </div>
          {data?.attachment ? (
            <>
              <div className={styles.desc}>附件</div>
              <Space>
                {data?.attachment.split(',').map((item: string, index: number) => (
                  <a
                    key={item}
                    onClick={async () => {
                      setVisible({
                        show: true,
                        img: item,
                      })
                    }}
                  >
                    附件{index + 1}
                  </a>
                ))}
              </Space>
            </>
          ) : null}
        </div>
      </div>
      <Image
        width={200}
        style={{ display: 'none' }}
        src={visible?.img}
        preview={{
          visible: visible.show,
          src: visible.img,
          onVisibleChange: (value) => {
            setVisible({
              show: value,
              img: undefined,
            })
          },
        }}
      />
      {env_screen.includes('mobile') && (
        <div style={floatBottomStyle}>
          <Button
            type={
              pathname.includes('management') && data?.status === '待审批' ? 'default' : 'primary'
            }
            shape="round"
            style={{
              width:
                pathname.includes('management') && data?.status === '待审批' ? '100px' : '100%',
            }}
            onClick={() => {
              history.go(-1)
            }}
          >
            返回上一页
          </Button>
        </div>
      )}
    </div>
  )
}

export default Detail
