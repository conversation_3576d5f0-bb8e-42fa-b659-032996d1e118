declare module '*.css'
declare module '*.less'
declare module '*.png'
declare module '*.jpg'
declare module '*.gif'
declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement
  const url: string
  export default url
}
declare module '*.mp3'
declare module '*.mp4'
declare module 'compression-webpack-plugin'
declare module 'intro.js'

/** 系统标题 */
declare const ENV_title: string
/** 系统副标题 */
declare const ENV_subTitle: string
/** 基本信息管理code */
declare const ENV_JCXXGL: '1100000'
/**学校基本信息 */
declare const ENV_XXJB: '11010000'
/**组织架构管理 */
declare const ENV_ZZJG: '11020000'
/**职务管理 */
declare const ENV_ZWGL: '11030000'
/**学年学期管理 */
declare const ENV_XNXQ: '11050000'
/**作息方案管理 */
declare const ENV_ZXFA: '11060000'
/**校历管理 */
declare const ENV_XLGL: '11070000'
/**节假日管理 */
declare const ENV_JJR: '11080000'
/** 教务管理code */
declare const ENV_JWGL: '12000000'
/** 场室管理 */
declare const ENV_CSGL: '12010000'
/** 年级班级管理 */
declare const ENV_NJBJGL: '12020000'
/** 学科管理 */
declare const ENV_XKGL: '12030000'
/** 课程管理 */
declare const ENV_KCGL: '12040000'
/** 任课管理 */
declare const ENV_RKGL: '12050000'
/** 课表管理 */
declare const ENV_KBGL: '12060000'
/** 课时管理 */
declare const ENV_KSGL: '12070000'
/**智慧排课 */
declare const ENV_ZHPK: '12080000'
/**课时统计 */
declare const ENV_KSTJ: '12090000'
/**调代课管理 */
declare const ENV_TDKGL: '12100000'

/**请假管理 */
declare const ENV_QJGL: '12130000'

/**教师请销假 */
declare const ENV_JSQXJ: '12130100'
/**学生请假审批 */
declare const ENV_XSQJSP: '12130200'
/**教师请假审批 */
declare const ENV_JSQJSP: '12130300'
/**教师销假审批 */
declare const ENV_JSXJSP: '12130400'
/**学生请假查询 */
declare const ENV_XSQJCX: '12130500'
/**教师请假查询 */
declare const ENV_JSQJCX: '12130600'
/**请假类别设置 */
declare const ENV_QJLBSZ: '12130700'
/**晨午检管理 */
declare const ENV_CWJGL: '12200000'
/**晨午检填报 */
declare const ENV_CWJTB: '12200100'
/**因病追踪登记 */
declare const ENV_XSJKDJ: '12200200'
/**晨午检记录汇总 */
declare const ENV_CWJJLHZ: '12200300'
/**传染病审核 */
declare const ENV_CRBSH: '12170100'
/**传染病类别设置 */
declare const ENV_CRBLBSZ: '12170200'
/**传染病登记 */
declare const ENV_CRBDJ: '12170300'
/**门禁管理 */
declare const ENV_MJGL: '12160000'
/**外出管理 */
declare const ENV_WCGL: '12150000'
/**门禁学生入校管理 */
declare const ENV_MJXSRXGL: '12160100'
/**门禁学生离校管理 */
declare const ENV_MJXSLXGL: '12160200'
/**门禁教师离校管理 */
declare const ENV_MJJSLXGL: '12160300'
/**考勤打卡 */
declare const ENV_KQDK: '12120400'
declare const ENV_KQTJ: '12120300'
declare const ENV_KQBC: '12120100'
declare const ENV_KQGZSZ: '12120200'
/** 课后服务统计 */
declare const ENV_KHFWTJ: '12140000'
/** 教师档案code */
declare const ENV_JSDA: '13010000'
/** 学生档案code */
declare const ENV_XSDA: '13020000'
/** 异动管理code */
declare const ENV_EDGL: '13060000'
/** 转入管理 */
declare const ENV_ZRGL: '13060100'
/** 转出管理 */
declare const ENV_ZCGL: '13060200'

/** 智慧教学code */
declare const ENV_ZHJX: '15000000'
declare const ENV_XBZY: '15010000'
/** 考试成绩管理 */
declare const ENV_KSCJFX: '15020000'
/** 考试成绩录入code */
declare const ENV_CJLR: '15020100'
/** 考试成绩分析code */
declare const ENV_CJFX: '15020200'
/** 成绩分析参数设定*/
declare const ENV_CSSD: '15020300'
/** 成绩查询code */
declare const ENV_CJCX: '15030000'
/** 成绩汇总code */
declare const ENV_CJHZ: '15020500'
/** 考勤管理code */
declare const ENV_KQGL: '12120000'

/** 考勤规则 */
declare const ENV_KQGZ: '16040100'
/** 审批设置 */
declare const ENV_SPSZ: '16040200'
/** 权限设置 */
declare const ENV_QXSZ: '16040300'
/** 通知公告code */
declare const ENV_TZGG: '17000000'
declare const ENV_DXTZGG: '18050000'
/** 智慧校务 */
declare const ENV_ZHXW: '18000000'
/** 问卷管理 */
declare const ENV_WJGL: '18010000'
/** 问卷填写 */
declare const ENV_WJTX: '18010200'
/** 问卷列表 */
declare const ENV_WJLB: '18010100'
/** 问卷统计 */
declare const ENV_WJTJ: '18010300'
/** 问卷模板 */
declare const ENV_WJMB: '18010400'
/** 收集表管理 */
declare const ENV_SJBGL: '18020000'
/** 收集表填写 */
declare const ENV_SJBTX: '18020200'
/** 收集表列表 */
declare const ENV_SJBLB: '18020100'
/** 收集表统计 */
declare const ENV_SJBTJ: '18020300'
/** 收集表模板 */
declare const ENV_SJBMB: '18020400'
/** 报修服务管理管理 */
declare const ENV_BXFW: '18030000'
/** 报修工单 */
declare const ENV_BXGD: '18030100'
/** 报修工单统计 */
declare const ENV_BXTJ: '18030200'
/** 报修服务配置 */
declare const ENV_BXPZ: '18030300'
/** 财务收费管理 */
declare const ENV_SFGL: '18040100'
/** 财务收费项配置 */
declare const ENV_SFPZ: '18040200'
/**巡课管理 */
declare const ENV_PATROL: '18080000'
/**巡课管理-巡课安排 */
declare const ENV_PATROLAP: '18080300'
/**教师月度考核 */
declare const ENV_JSYDKH: '18140000'
/** 校本应用code */
declare const ENV_XBYY: '98000000'

/** 系统管理code */
declare const ENV_XTGL: '99000000'
/** 第三方code */
declare const ENV_DSF: '96000000'
/** 应用管理 */
declare const ENV_YYGL: '99010000'
/** 账号管理 */
declare const ENV_ZHGL: '99020000'
/** 角色管理 */
declare const ENV_JSGL: '99030000'
/** 授权管理 */
declare const ENV_SQGL: '99040000'

/** 常用工具 */
declare const ENV_CYGJ: '99060000'
/** 友情链接 */
declare const ENV_YQLJ: '99070000'
/**管理权限 */
declare const ENV_GL: '01'
/**查看权限 */
declare const ENV_CK: '02'
/**审批、使用权限 */
declare const ENV_SP: '03'
/** 审批 推荐使用 */
declare const ENV_NEW_SP: '32'
/**维修权限 */
declare const ENV_WX: '26'
/**查询权限 */
declare const ENV_CX: '27'
/**班级质量分析-录入 */
declare const ENV_BJZL: '28'
/**年级质量分析-录入 */
declare const ENV_NJZL: '29'
/**全校质量分析-录入 */
declare const ENV_QXZL: '30'
/**全校成绩汇总-查看 */
declare const ENV_QXHZ: '37'
/**年级成绩汇总-查看 */
declare const ENV_NJHZ: '38'
/**班级成绩汇总-查看 */
declare const ENV_BJHZ: '39'
/**成绩分析-分析 */
declare const ENV_CJFX_FX: '31'
/** 班主任审批 */
declare const ENV_BZR_SP: '33'
/** 教导处审批 */
declare const ENV_JDC_SP: '34'
/** 校领导审批 */
declare const ENV_XLD_SP: '35'
/** 提交申请 */
declare const ENV_TJSQ: '36'
/** 应用编号 */
declare const ENV_clientId: '00010'
/** 运营商企业编号 */
declare const ENV_plat_enterprise_code: '01001'
/** 应用密钥 */
declare const ENV_clientSecret: string
/** 是否为本地开发模式 */
declare const ENV_debug: boolean
/** 开发模式连接的环境 */
declare const ENV_debug_env: string
/** 测试用企业Code */
declare const ENV_test_enterCode: string
/** 领航6测试账号 */
declare const test_lh6: {
  老师: {
    初中语文: string
    初中数学: string
    初中英语: string
  }
  学生: string
}
declare const TENCENT_COS: {
  SecretId: string
  SecretKey: string
  baseDir: string
}
/** 中文数字 */
declare const ENV_zhNum: string
/** 中文周 */
declare const ENV_weekdays: string

declare const wx: any
declare const WWOpenData: any

type ResType<T> = {
  errCode: number
  message: string
} & T

/** 运行环境类型 */
type PlatType = 'com-wx-mobile' | 'wx-mobile' | 'mobile' | 'com-wx-pc' | 'wx-pc' | 'pc'
/** 主题风格 */
type themeStyle = 'default' | 'dark' | 'glass' | 'cartoon' | 'ancient' | 'technology'
/** 应用显示模式配置 */
type AppMode = {
  /** alone 独立模式， nesting 嵌套模式 */
  mode: 'alone' | 'nesting'
  /** 菜单顶部的title */
  title?: string
}

/** 通过乾坤框架与子应用互通的信息 */
type QiankunState = {
  /** 主应用ID，用于协助子应用获取主应用的token，注意，认证中心token名称无后缀，其他应用做为主应用要配自己的clientId */
  master_clientId: '00010'
  /**是否管理员登录 */
  isLoginAdmin: boolean
  /**
   * 是否普通用户
   */
  isLoginOrdinary: boolean
  /**
   * funcode 应用code
   * colcode 权限点code
   */
  isAdministration?: (funcode: string, colCode: string) => boolean
  /** 当前用户信息，子应用直接用其封自己的认证信息即可，不再做sso入口鉴权 */
  currentUser?: UserInfo
} & Record<string, any>

type Enterprise = {
  id?: string
  /** 企业编码 */
  code: string
  /** 企业名称 */
  name: string
  /** 企业全称 */
  full_name?: string
  /** 企业logo */
  logo_url?: string
  /** 注册形式 */
  reg_type: 'default' | 'wechat' | 'dingding'
  /** 是否业务应用 */
  isBuiltIn: boolean
  /** 状态 */
  status: 'enabled' | 'disabled' | 'pending'
  /** 备注 */
  remark: string
}

type UserInfo = {
  userId: string
  /** 登录账号 */
  username: string
  /** 真实姓名 */
  realName: string
  /** 学校/企业ID */
  corpId: string
  /** 用户身份 */
  userType: string[]
  /** 普通权限应用列表 */
  auths: { appCode: string }[]
  /** 管理员权限应用列表 */
  adminAuths: string[]
  /** 认证方式 */
  authType: 'sso'
  /** 绑定的用户编码 */
  userCode: string
  /** 绑定的家长编码 */
  parentCode: string
  /** 绑定的学生编码 */
  studentCode: string
  id?: string
  password?: string
  avatar?: null
  type?: string
  mobile?: string
  email?: string
  /** 是否为内置账号 */
  isBuiltIn?: boolean
  /** 注册时间 */
  logonTime?: string
  /** 最后登录时间 */
  lastLoginTime?: string
  status?: 'applying' | 'rejected' | 'enabled' | 'disabled'
  roles?: any[]
  rolesName?: any[]
  enterprise?: any
  /** 所属企业编号 */
  enterpriseCode?: string
  /** 验证码，用于找回密码等场景 */
  verificationCode?: string
  /** 验证码有效期 */
  verificationCodeExpiresAt?: string
  createdAt?: string
  updatedAt?: string
  roleCode?: any[]
  organizationCode?: any[]
  /** 本地师生信息ID */
  memberId?: string
  user_for_3ths?: any[]
  enterpriseType:
    | 'servicer'
    | 'agentor'
    | 'developer'
    | 'bureau_city'
    | 'bureau'
    | 'school'
    | 'organization'
    | 'parent'
    | 'other'
  /** 是否托管 */
  isTrusteeship?: boolean
  globalSessionToken?: string
}

type AuthType = 'wechat' | 'password' | 'authorization_code'

type BuildOptions = {
  /** 部署环境标记，如chanming、9dy等 */
  ENV_type: string
  /** 版权信息 */
  ENV_copyRight: string
  /** 部署地址，sso认证回调需要使用 */
  ENV_host: string
  /** sso认证地址 */
  ssoHost: string
  /** 问卷地址 */
  questionHost: string
  /** 收费管理 */
  financialHost: string
  /** 请假管理 */
  askforLeaveHost: string
  /** 教师月度考核管理 */
  teacherEvaluationHost: string
  /** 认证方式 */
  authType: AuthType
}

/** 全局初始信息 */
type InitialState = {
  fetchCurrentUser: () => Promise<{
    currentUser: UserInfo | null
    userAccessList: any[]
    allAccessList: any[]
    schoolApplication: any[]
    isInit: boolean
  }>
  currentUser?: UserInfo | null
  env?: string
  userAccessList: any[]
  allAccessList: any[]
  schoolApplication: API.appCategory[]
  schoolInfo?: {
    id?: string
    code?: string
    name?: string
    english_name?: string
    type_code?: string
    type_name?: string
    region?: string
    region_code?: string
    liasion?: string
    mobile?: string
    address?: string
    social_credit_code?: string
    official_url?: string
    email?: string
    fax?: string
    postal_code?: string
    service_scope?: string
    legal_person?: string
    legal_person_code?: string
    section_name?: string
    section_code?: sectionCode
    describe?: string
    remark?: string
    logo_url?: string
    business_license_url?: string
    school_license_url?: string
    createdAt?: string
    updatedAt?: string
    theme: themeStyle
    showInitMessage?: boolean
    independent?: boolean
  }
  /** 是否需要初始化*/
  isInit: boolean
}

/** 通过useAccess获取到的应用权限详情信息 */
type AccessInfo = {
  /** 是否登录 */
  isLogin: boolean
  /** 是否学校 */
  isSchool?: boolean
  /** 是否机构 */
  isJG?: boolean
  /** 是否教育局 */
  isJYJ?: boolean
  /** 使用,管理 */
  authType: string
  /** 是否普通员工 */
  isOrdinary: boolean
  /** 是否管理员 */
  isAdmin: boolean
  /** 是否存在多身份 */
  isSwitchIdentity: boolean
  /** 当前登录人的身份是否是管理员 */
  isLoginAdmin: boolean
  /** 当前登录人的身份是否是普通用户 */
  isLoginOrdinary: boolean
  /**
   * funcode 应用code
   * colcode 权限点code
   */
  isAdministration: (funcode: string, colCode: string | string[]) => boolean
  /** 是否为平台管理员 */
  isPlatAdmin: boolean
  [key: string]: any
}

/** oAuth认证token */
type TokenInfo = {
  access_token: string
  expires_in?: number
  refresh_token?: string
  token_type?: string
}
// 微信部门的数据类型
// 学年学期的数据类型
type ChainDataType = {
  /** 联动一级数据 类型 */
  data: { label: string; value: string }[]
  /** 联动二级数据类型 */
  subData: Record<string, { label: string; value: string }[]>
}

/** 部门信息 */
type DepDataType = {
  /** 负责人 */
  FZR?: Record<string, unknown>
  /** 部门ID */
  id: string
  /** 隶属部门ID */
  LSJGH: string
  /** 简称 */
  JGJC: string
  /** 名称 */
  JGMC: string
  /** 教职工列表 */
  JZGJBSJs: Record<string, unknown>[]
}

type contentData = {
  gradeCode?: string
  gradeName?: string
  children?: weekeRenderType[]
}
type weekeRenderType = { wkd?: number; jcInd?: number; type: 0 | 1; typeName?: string }

/**
 *  01幼儿园 02小学 03初中 04 高中 05九年一贯制 06完全中学 07 12年一贯制
 */
type sectionCode = '01' | '02' | '03' | '04' | '05' | '06' | '07'
