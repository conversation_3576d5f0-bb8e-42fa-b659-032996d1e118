import { getAskForLeave } from '@/services/edu-platform-web/infection_list'
import { getFileById } from '@/services/edu-platform-web/upload'
import { ProDescriptions } from '@ant-design/pro-components'
import { Button, Modal, Space, Tag, Timeline, message } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useModel, useParams, history } from 'umi'
import styles from './index.less'
import { envjudge } from '@/utils'
import { floatBottomStyle } from '@/constant'

const Detail = () => {
  const { id } = useParams() as any
  const { initialState } = useModel('@@initialState')
  const { schoolInfo } = initialState || {}
  const env_screen = envjudge()
  const [data, setData] = useState<API.AskForLeaveStudent>()
  const [previewImage, setPreviewImage] = useState<any>()

  const getData = async (cid: string) => {
    const result = (await getAskForLeave(cid)) as any
    if (!result?.errCode) {
      setData(result)
    }
  }
  useEffect(() => {
    if (id) {
      getData(id)
    }
  }, [id])

  console.log(data)

  return (
    <div className={styles.detailWraper}>
      {!env_screen.includes('mobile') && (
        <Button
          type="primary"
          style={{
            marginBlockEnd: 24,
          }}
          onClick={() => {
            history.push('/educationaManagement/crbgl/auditList')
          }}
        >
          <LeftOutlined />
          返回上一页
        </Button>
      )}
      <div>
        <div className={styles.topCon}>
          <div className={styles.title}>{data?.userName}提交的请假申请</div>
          <div className={styles.desc}>{schoolInfo?.name}</div>
          <Space>
            <Tag
              color={
                data?.status === '已同意'
                  ? '#87d068'
                  : data?.status === '已拒绝'
                  ? '#f50'
                  : '#108ee9'
              }
            >
              {data?.status}
            </Tag>
            {data?.status === '已同意' && (
              <Tag
                color={
                  data?.finshedStatus === '2'
                    ? 'success'
                    : data?.finshedStatus === '1'
                    ? 'processing'
                    : 'error'
                }
              >
                {data?.finshedStatus === '0'
                  ? '待销假'
                  : data?.finshedStatus === '1'
                  ? '销假待审'
                  : data?.finshedStatus === '2'
                  ? '已销假'
                  : data?.finshedStatus === '3'
                  ? '销假被拒绝'
                  : ''}
              </Tag>
            )}
          </Space>
        </div>

        <div className={styles.middleCon}>
          <div className={styles.desc}>申请编号</div>
          <div>{data?.id}</div>
          <div className={styles.flowCon}>
            <div style={{ fontWeight: 'bold' }}>
              {data?.type}
              {data?.type === '传染性病假' && `（${data?.infection_type?.jb}）`}
            </div>
            {data?.type === '传染性病假' && (
              <ProDescriptions
                column={1}
                dataSource={{
                  isolationDays: data?.isolationDays,
                  ...(data?.infection_type || {}),
                }}
              >
                <ProDescriptions.Item valueType="text" dataIndex="qfq" label="潜伏期" />
                {data?.infection_type?.glsx && (
                  <ProDescriptions.Item valueType="text" dataIndex="glsx" label="隔离时限描述" />
                )}
                <ProDescriptions.Item valueType="text" dataIndex="isolationDays" label="隔离天数" />
                <ProDescriptions.Item valueType="text" dataIndex="zcwj" label="政策文件" />
              </ProDescriptions>
            )}
            <Timeline
              style={{
                marginTop: '10px',
              }}
            >
              <Timeline.Item>{moment(data?.startTime).format('YYYY/MM/DD HH:mm')}</Timeline.Item>

              <Timeline.Item>{moment(data?.endTime).format('YYYY/MM/DD HH:mm')}</Timeline.Item>
            </Timeline>
          </div>
          <div className={styles.desc}>学生情况</div>
          <p className={styles.desc}>发病日期: {moment(data?.disease_date).format('YYYY.MM.DD')}</p>
          <p className={styles.desc}>确诊日期: {moment(data?.startTime).format('YYYY.MM.DD')}</p>
          <p className={styles.desc}>诊断单位 : {data?.diagnose_name}</p>
          <p className={styles.desc}>处理措施: {data?.dispose}</p>
          <div className={styles.desc}>请假说明</div>
          <div style={{ marginBottom: '10px', fontSize: 'larger' }}>{data?.reason}</div>
          {data?.annex ? (
            <>
              <div className={styles.desc}>{'诊断证明及病历'}</div>
              <Space>
                {data?.annex?.split(',')?.map((val: string, index) => (
                  <a
                    key={val}
                    href="#"
                    onClick={async () => {
                      const result = await getFileById(val)
                      if (!result?.errCode) {
                        setPreviewImage({
                          imgUrl: result?.url,
                          show: true,
                          title: '图片预览',
                        })
                      } else {
                        message.warning('文件已过期或已损坏')
                      }
                    }}
                  >
                    附件{index + 1}
                  </a>
                ))}
              </Space>
            </>
          ) : null}

          {data?.approvalOpinion && (
            <>
              <div
                className={styles.desc}
                style={{
                  marginTop: '10px',
                }}
              >
                审批意见
              </div>
              <div style={{ marginBottom: '10px', fontSize: 'larger' }}>
                {data?.approvalOpinion}
              </div>
            </>
          )}
        </div>
        {data?.status === '已同意' && data?.finshedStatus !== '0' && (
          <div className={styles.bottomCon}>
            <div className={styles.title}>
              销假{' '}
              {data?.status === '已同意' && (
                <Tag
                  color={
                    data?.finshedStatus === '2'
                      ? 'success'
                      : data?.finshedStatus === '1'
                      ? 'processing'
                      : 'error'
                  }
                >
                  {data?.finshedStatus === '0'
                    ? '待销假'
                    : data?.finshedStatus === '1'
                    ? '销假待审'
                    : data?.finshedStatus === '2'
                    ? '已销假'
                    : data?.finshedStatus === '3'
                    ? '销假被拒绝'
                    : ''}
                </Tag>
              )}
            </div>
            <div className={styles.desc}>销假说明</div>
            <div style={{ marginBottom: '10px', fontSize: 'larger' }}>{data?.finshedReason}</div>
            {data?.finshedAnnex ? (
              <>
                <div className={styles.desc}>{'康复证明'}</div>
                <Space>
                  {data?.finshedAnnex?.split(',')?.map((val: string, index) => (
                    <a
                      key={val}
                      href="#"
                      onClick={async () => {
                        const result = await getFileById(val)
                        if (!result?.errCode) {
                          setPreviewImage({
                            imgUrl: result.url,
                            show: true,
                            title: '图片预览',
                          })
                        } else {
                          message.warning('文件已过期或已损坏')
                        }
                      }}
                    >
                      附件{index + 1}
                    </a>
                  ))}
                </Space>
              </>
            ) : null}
            {data?.finshedOpinion && (
              <>
                <div
                  className={styles.desc}
                  style={{
                    marginTop: '10px',
                  }}
                >
                  审批意见
                </div>
                <div style={{ marginBottom: '10px', fontSize: 'larger' }}>
                  {data?.finshedOpinion}
                </div>
              </>
            )}
          </div>
        )}
      </div>
      {env_screen.includes('mobile') && (
        <div style={floatBottomStyle}>
          <Button
            type="primary"
            shape="round"
            style={{
              width: '100%',
            }}
            onClick={() => {
              history.push('/educationaManagement/crbgl/auditList')
            }}
          >
            返回上一页
          </Button>
        </div>
      )}

      <Modal
        open={previewImage?.show}
        title={previewImage?.title}
        footer={null}
        onCancel={() => setPreviewImage(undefined)}
      >
        <img alt="example" style={{ width: '100%' }} src={previewImage?.imgUrl} />
      </Modal>
    </div>
  )
}

export default Detail
