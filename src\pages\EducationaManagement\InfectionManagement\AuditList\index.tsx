import React, { useEffect, useRef, useState } from 'react'
import { getOutColums, getOutMetas } from './getColumns'
import { useAccess, useModel } from 'umi'
import CommonList from '@/components/CommonList'
import {
  bulkAudit,
  getAllList,
  getApprovedList,
  getAuditList,
} from '@/services/edu-platform-web/infection_list'
import { Button, Input, message, Modal, Space } from 'antd'
import SemesterSelect from '@/components/SemesterSelect'
import SearchLayout from '@/components/Search/Layout'
import Audit from './Audit'
import styles from './index.less'
import { envjudge } from '@/utils'
import type { ActionType } from '@ant-design/pro-components'
import {
  ModalForm,
  ProFormDependency,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components'
import dayjs from 'dayjs'

const { Search } = Input
const AuditList = () => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const { isAdministration } = useAccess()
  const env_screen = envjudge()
  const [title, setTitle] = useState<string>()
  const [termCode, setTermCode] = useState<string>()
  const [previewImage, setPreviewImage] = useState<any>()
  const [modalVisible, setModalVisible] = useState<boolean>(false)
  const [current, setCurrent] = useState<any>()
  const [activeStatus, setActiveStatus] = useState<string>('待审批')
  const [batchModal, setBatchModal] = useState(false)
  const [seelectedRows, setSelectedRows] = useState<string[]>([])
  const onCleanSelectedRef = useRef<() => void>()

  const childRef = useRef<ActionType>()
  useEffect(() => {
    if (current) {
      setModalVisible(true)
    }
  }, [current])

  /** 批量审批 */
  const handleSubmit = async (values: any) => {
    const data: any = seelectedRows.map((item: any) => {
      return {
        id: item?.id,
        status: values.auditStatus ? '已同意' : '已拒绝',
        isolationDays: Number(item.isolationDays),
        approvalOpinion: values?.approvalOpinion,
        endTime: values.auditStatus
          ? dayjs(item?.startTime).add(item.isolationDays, 'day')
          : undefined,
        approvalUser: currentUser?.realName,
        approvalUserCode: currentUser?.username,
        approvalTime: new Date(),
        enterpriseName: currentUser?.enterprise?.name,
      }
    })
    const res = await bulkAudit(data)

    if (res?.errCode) {
      message.error(`批量审批失败：${res?.msg}`)
      return true
    } else {
      message.success('批量审批成功')
      setBatchModal(false)
      ;(childRef?.current as any)?.reload?.()
      setSelectedRows([])
      if (onCleanSelectedRef.current) {
        onCleanSelectedRef.current()
      }
      return true
    }
  }

  return (
    <div className={styles.auditList}>
      {isAdministration(ENV_CRBSH, ENV_SP) && (
        <ul className={styles.tabNav}>
          <li
            className={activeStatus === '待审批' ? styles.active : ''}
            onClick={() => {
              if (activeStatus !== '待审批') {
                setActiveStatus('待审批')
                setTitle(undefined)
              }
            }}
          >
            待审核
          </li>
          <li
            className={activeStatus !== '待审批' ? styles.active : ''}
            onClick={() => {
              if (activeStatus === '待审批') {
                setActiveStatus('已同意,已拒绝')
                setTitle(undefined)
                setSelectedRows([])
                if (onCleanSelectedRef.current) {
                  onCleanSelectedRef.current()
                }
              }
            }}
          >
            已审核
          </li>
        </ul>
      )}
      <CommonList
        ref={childRef}
        params={{
          schoolInfo,
          termCode,
          title,
          activeStatus,
        }}
        rowSelection={activeStatus === '待审批' ? {} : false}
        tableAlertRender={({ selectedRowKeys, selectedRows: value, onCleanSelected }: any) => {
          setSelectedRows(value)
          onCleanSelectedRef.current = onCleanSelected
          return (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
                <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                  取消选择
                </a>
              </span>
            </Space>
          )
        }}
        tableAlertOptionRender={() => {
          return (
            <Button
              type="primary"
              onClick={() => {
                setBatchModal(true)
              }}
            >
              批量审批
            </Button>
          )
        }}
        request={async (
          // 第一个参数 params 查询表单和 params 参数的结合
          // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
          params: any,
        ) => {
          // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
          // 如果需要转化参数可以在这里进行修改
          const { pageSize, current: page } = params
          if (termCode && schoolInfo) {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const { errCode, list, total, msg, message } = (
              isAdministration(ENV_CRBSH, ENV_SP)
                ? activeStatus === '待审批'
                  ? await getAuditList({
                      enterpriseCode: schoolInfo?.code || '',
                      userName: title,
                      semesterCode: termCode,
                      // memberCode: currentUser?.userCode,
                      offset: Number((page || 1) - 1) * Number(pageSize),
                      limit: Number(pageSize),
                    })
                  : await getApprovedList({
                      enterpriseCode: schoolInfo?.code || '',
                      status: undefined,
                      userName: title,
                      semesterCode: termCode,
                      // memberCode: currentUser?.userCode,
                      offset: Number((page || 1) - 1) * Number(pageSize),
                      limit: Number(pageSize),
                    })
                : await getAllList({
                    enterpriseCode: schoolInfo?.code || '',
                    userName: title,
                    type: '传染性病假',
                    semesterCode: termCode,
                    // memberCode: currentUser?.userCode,
                    offset: Number((page || 1) - 1) * Number(pageSize),
                    limit: Number(pageSize),
                  })
            ) as any
            if (errCode) {
              message.error(msg || message || '数据获取失败，请联系管理员或稍后再试！')
              return {
                success: true,
                data: [],
                total: 0,
              }
            } else {
              return {
                success: true,
                data: list || [],
                total: total,
              }
            }
          } else {
            return {
              success: true,
              data: [],
              total: 0,
            }
          }
        }}
        headerTitle={
          <SearchLayout>
            <SemesterSelect
              hidden={env_screen.includes('mobile')}
              onChange={(val) => {
                setTermCode(val)
              }}
            />
            <div>
              <label htmlFor="title">学生姓名：</label>
              <Search
                allowClear
                defaultValue={title}
                onSearch={(val: string) => {
                  setTitle(val === '' ? undefined : val)
                }}
              />
            </div>
          </SearchLayout>
        }
        columns={getOutColums({
          isAdmin: isAdministration(ENV_CRBSH, ENV_SP),
          setPreviewImage,
          setCurrent,
          refreshTable: () => {
            ;(childRef?.current as any)?.reload?.()
          },
          currentUser,
        })}
        metas={getOutMetas({
          isAdmin: isAdministration(ENV_CRBSH, ENV_SP),
          setPreviewImage,
          setCurrent,
          refreshTable: () => {
            ;(childRef?.current as any)?.reload?.()
          },
          currentUser,
        })}
      />
      <Modal
        open={previewImage?.show}
        title={previewImage?.title}
        footer={null}
        onCancel={() => setPreviewImage(undefined)}
      >
        <img alt="example" style={{ width: '100%' }} src={previewImage?.imgUrl} />
      </Modal>
      <Audit
        current={current}
        modalVisible={modalVisible}
        setPreviewImage={setPreviewImage}
        onClose={() => {
          setCurrent(undefined)
          setModalVisible(false)
        }}
        reload={() => {
          ;(childRef?.current as any)?.reload?.()
        }}
      />
      <ModalForm
        layout="horizontal"
        title="批量审批"
        width={600}
        open={batchModal}
        onFinish={handleSubmit}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setBatchModal(false)
          },
        }}
      >
        <div style={{ marginBottom: 16 }}>
          注意：批量审批时，系统将自动启用建议的隔离天数，并且此设置无法修改。是否继续操作？
        </div>
        <ProFormRadio.Group
          name="auditStatus"
          label="审批意见"
          initialValue={true}
          options={[
            {
              label: '同意',
              value: true,
            },
            {
              label: '拒绝',
              value: false,
            },
          ]}
        />

        <ProFormDependency name={['auditStatus']}>
          {({ auditStatus }) => {
            return (
              <ProFormTextArea
                name="approvalOpinion"
                label="审批说明"
                placeholder="请输入名称"
                rules={[
                  {
                    required: !auditStatus,
                    message: '请输入审批说明',
                  },
                ]}
              />
            )
          }}
        </ProFormDependency>
      </ModalForm>
    </div>
  )
}

export default AuditList
