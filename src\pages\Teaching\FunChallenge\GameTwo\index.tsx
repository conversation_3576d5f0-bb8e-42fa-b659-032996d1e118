/** 曲江流饮 选人页面  */

import React, { useCallback, useEffect, useState } from 'react'
import styles from './index.less'
import { Row, Col, Select, message, Empty, List, Drawer, Modal } from 'antd'
import { getL<PERSON><PERSON> as getAncientPoetryList } from '@/services/edu-platform-web/ancient_poetry_answers'
import {
  controllerRepeatGetClasses,
  controllerRepeatGetGrades,
  controllerRepeatGetStudents,
  controllerRepeatGetStudentsNoAuth,
} from '@/services/edu-platform-web/ssoRepeat'
import { getActivedTerm } from '@/utils'
import { useLocation, useModel } from 'umi'
import moment from 'moment'
import StudentCard from './components/StudentCard'
import SwimFlowers from './components/SwimFlowers'

type ScreenType = {
  label: string
  value: string
}

const GameTwo: React.FC = () => {
  const { search, pathname } = useLocation()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo } = initialState || {}
  const [grades, setGrades] = useState<ScreenType[]>([])
  const [semesterCode, setSemesterCode] = useState<string>()
  const [currentGrade, setCurrentGrade] = useState<ScreenType>()
  const [currentClass, setCurrentClass] = useState<ScreenType>()
  const [students, setStudents] = useState<(API.StudentInfo & { done: boolean })[]>([])
  const [classes, setClasses] = useState<ScreenType[]>([])
  const [currentStudent, setCurrentStudent] = useState<API.StudentInfo>()
  /** 是否直接进入游戏 */
  const [isDirect, setIsDirect] = useState<boolean>(false)

  useEffect(() => {
    const checkOrientation = () => {
      if (window.matchMedia('(orientation: portrait)').matches) {
        Modal.info({
          title: '闯关暂不支持竖屏游玩，请切换到横向模式',
          content: <div>设备处于竖屏模式。请切换到横向模式以获得最佳体验。</div>,
          onOk() {},
        })
      }
    }
    window.addEventListener('orientationchange', checkOrientation)
    window.addEventListener('resize', checkOrientation)
    checkOrientation()
    return () => {
      window.removeEventListener('orientationchange', checkOrientation)
      window.removeEventListener('resize', checkOrientation)
    }
  }, [])

  const getAncientPoetrys = useCallback(
    async (gradeName: string, className: string): Promise<API.AncientPoetryAnswer[]> => {
      const result = await getAncientPoetryList({
        schoolCode: schoolInfo?.code || '',
        gradeName,
        className,
        gameName: '乐游长安',
        answerTime: moment(new Date()).format('YYYY-MM-DD'),
      })
      if (result?.errCode) {
        message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
        return []
      }
      return result?.list || []
    },
    [schoolInfo?.code],
  )

  const getStudents = useCallback(
    async (gradeCode: string, classCode: string) => {
      const result = await controllerRepeatGetStudents({
        enterpriseId: schoolInfo?.id || '',
        semesterCode: semesterCode,
        grade: gradeCode,
        classCode,
      })
      if (result?.errCode) {
        message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
        return []
      }
      return result?.list || []
    },
    [schoolInfo?.id, semesterCode],
  )

  const getClassData = useCallback(
    async (gradeCode: string) => {
      const BjArr: ScreenType[] = []
      const result = (await controllerRepeatGetClasses({
        grade: gradeCode,
        enterpriseId: schoolInfo?.id || '',
        semesterCode: semesterCode,
      })) as ResType<any>
      if (result?.errCode) {
        message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
        return []
      }
      result?.list?.map((item: any) => {
        return BjArr.push({
          label: item?.name,
          value: item?.code,
        })
      })
      return BjArr
    },
    [schoolInfo?.id, semesterCode],
  )

  useEffect(() => {
    if (pathname !== '/entertainment/gameTwo') {
      if (currentGrade) {
        ;(async () => {
          const list = await getClassData(currentGrade.value)
          setClasses(list)
          const code = sessionStorage.getItem('gradeCode')
          if (code) {
            const Code = code.split(';').pop()
            const selectedClass = list.find((v) => v.value === Code)
            if (selectedClass) {
              setCurrentClass(selectedClass)
            }
          }
        })()
      } else {
        setClasses([])
        setCurrentClass(undefined)
      }
    }
  }, [currentGrade, getClassData, pathname])

  useEffect(() => {
    if (pathname !== '/entertainment/gameTwo') {
      ;(async () => {
        const res = (await getActivedTerm(schoolInfo?.code || '')) || {}
        setSemesterCode(res?.semester_code)
      })()
      const XDNameList = schoolInfo?.section_name?.split(',') || []
      ;(async () => {
        const NjArr: ScreenType[] = []
        const result = (await controllerRepeatGetGrades({ section: XDNameList })) as ResType<any>
        if (result?.errCode) {
          message.error(result.message || '数据获取失败，请联系管理员或稍后再试')
        } else {
          result?.list?.forEach((item: any) => {
            if (['0201', '0202'].includes(item?.code)) {
              NjArr.push({
                label: item?.name,
                value: item?.code,
              })
            }
          })
          setGrades(NjArr)
        }
      })()
    }
  }, [pathname, schoolInfo?.code, schoolInfo?.section_name])

  useEffect(() => {
    if (pathname !== '/entertainment/gameTwo') {
      if (currentGrade && currentClass) {
        ;(async () => {
          const list = (await getStudents(
            currentGrade.value,
            currentClass.value,
          )) as API.StudentInfo[]
          const answers = await getAncientPoetrys(currentGrade.label, currentClass.label)
          setStudents(
            list.map((item) => ({
              ...item,
              done: !!answers.find((answer) => answer.studentCode === item.code),
            })),
          )
        })()
      } else {
        setStudents([])
      }
    }
  }, [currentClass, currentGrade, getAncientPoetrys, getStudents, pathname])

  useEffect(() => {
    if (pathname !== '/entertainment/gameTwo') {
      const code = sessionStorage.getItem('gradeCode')
      if (!code) return

      const gradesCode = code.split(';').shift()
      const Code = code.split(';').pop()

      if (grades.length > 0) {
        const selectedGrade = grades.find((v) => v.value === gradesCode)
        if (selectedGrade) {
          setCurrentGrade(selectedGrade)
        }
      }

      if (classes.length > 0) {
        const selectedClass = classes.find((v) => v.value === Code)
        if (selectedClass) {
          setCurrentClass(selectedClass)
        }
      }
    }
  }, [grades, classes, pathname])

  const studentInfo = useCallback(async () => {
    const queryString = search.split('?')[1]
    const params = new URLSearchParams(queryString)
    const code = params.get('code')
    const enterpriseCode = params.get('enterpriseCode')
    if (!code && !enterpriseCode) {
      message.warning('缺少参数，请联系管理员')
      return
    }
    const res = await controllerRepeatGetStudentsNoAuth({
      enterpriseCode,
      code,
    })
    if (res) {
      if (res.count == 1) {
        const gradeName = res?.list?.[0]?.classes?.[0]?.grade_name
        if (gradeName !== '一年级' && gradeName !== '二年级') {
          return
        }
        setCurrentStudent(res?.list?.[0])
        sessionStorage.setItem('student', JSON.stringify(res?.list[0]))
        setIsDirect(true)
      } else {
        message.error('沒有找到该学生！')
      }
    } else {
      message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
    }
  }, [search])

  useEffect(() => {
    if (pathname === '/entertainment/gameTwo') {
      studentInfo()
    }
  }, [pathname, studentInfo])

  return (
    <>
      {(pathname === '/entertainment/gameTwo' ? false : true) ? (
        <>
          {isDirect ? (
            <SwimFlowers
              data={!!currentStudent}
              setCurrentStudent={setCurrentStudent}
              currentStudent={currentStudent}
            />
          ) : (
            <div
              className={styles.selectPerson}
              style={{
                minHeight: pathname !== '/entertainment/gameTwo' ? '' : '100vh',
              }}
            >
              <div className={styles.titleBox}>
                <h1 className={styles.title}>欢迎来到长安八景“曲江流饮”</h1>
                <div className={styles.described}>——夏之乐</div>
              </div>
              {pathname !== '/entertainment/gameTwo' ? (
                <>
                  <Row gutter={16} className={styles.selectBox}>
                    <Col className={styles.searchCon}>
                      <label htmlFor="xm" className={styles.label}>
                        年级：
                      </label>
                      <Select
                        options={grades}
                        allowClear={true}
                        value={currentGrade?.value}
                        onChange={(value) => {
                          setCurrentGrade(grades.find((v) => v.value === value))
                          setCurrentClass(undefined)
                          sessionStorage.removeItem('gradeCode')
                        }}
                        className={styles.option}
                      />
                    </Col>
                    <Col className={styles.searchCon}>
                      <label htmlFor="xm" className={styles.label}>
                        班级：
                      </label>
                      <Select
                        options={classes}
                        allowClear={true}
                        value={currentClass?.value}
                        onChange={(value) => {
                          setCurrentClass(classes.find((v) => v.value === value))
                          sessionStorage.removeItem('gradeCode')
                        }}
                        className={styles.option}
                      />
                    </Col>
                  </Row>
                  <List
                    className={styles.studentList}
                    grid={{ gutter: 16, xs: 3, sm: 4, md: 4, lg: 4, xl: 6, xxl: 8 }}
                    dataSource={students}
                    renderItem={(item) => (
                      <StudentCard
                        onClick={() => {
                          setCurrentStudent(students.find((v) => v.code === item.code))
                        }}
                        done={item.done}
                      >
                        {item.name}
                      </StudentCard>
                    )}
                    locale={{
                      emptyText: (
                        <Empty
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                          description="请选择年级和班级"
                          style={{ marginTop: '20vh' }}
                        />
                      ),
                    }}
                  />
                </>
              ) : (
                <div className={styles.warning}>
                  遨游太空趣味闯关活动仅面向一、二年级的同学开放。
                </div>
              )}
              <Drawer
                width={'100vw'}
                forceRender
                open={!!currentStudent}
                headerStyle={{ display: 'none' }}
                bodyStyle={{ padding: 0 }}
                destroyOnClose
              >
                {!!currentStudent && (
                  <SwimFlowers
                    data={!!currentStudent}
                    setCurrentStudent={setCurrentStudent}
                    currentStudent={currentStudent}
                  />
                )}
              </Drawer>
            </div>
          )}
        </>
      ) : (
        <div
          className={styles.selectPerson}
          style={{
            minHeight: '100vh',
          }}
        >
          <div className={styles.unopen}>暂未开放使用</div>
        </div>
      )}
    </>
  )
}

export default GameTwo
