import SearchLayout from '@/components/Search/Layout'
import SemesterSelect from '@/components/SemesterSelect'
import { Button, Col, DatePicker, Empty, Modal, Row, Select, Space, Tag, message } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import { ToTopOutlined } from '@ant-design/icons'
import { envjudge, getNjName } from '@/utils'
import CommonList from '@/components/CommonList'
import { getRecordColums, getRecordMetas } from './getColumns'
import {
  getAbsentList,
  getNoonStatisticList,
  getUnsubmittedClass,
} from '@/services/edu-platform-web/noonInspection'
import { controllerRepeatGetGrades } from '@/services/edu-platform-web/ssoRepeat'
import AbsenceModal from './AbsenceModal'
import moment from 'moment'
import { ModalForm, ProFormDateRangePicker, ProFormSelect } from '@ant-design/pro-components'
import styles from './index.less'

const { Option } = Select
const grades = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
const RecordStastic = () => {
  const childRef = useRef()
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const env_screen = envjudge()
  const isMobile = env_screen?.includes('mobile')
  // 选择学年学期
  const [nowTermCode, setNowTermCode] = useState<string>()
  const [NJData, setNJData] = useState<any>()
  const [gradeCode, setGradeCode] = useState<string>()
  const [checkTime, setCheckTime] = useState<string>(moment().format('YYYY-MM-DD'))
  const [modalProps, setModalProps] = useState<{
    visible: boolean
    data?: any
  }>({
    visible: false,
  })
  const [period, setPeriod] = useState<string>()
  const [exportModal, setExportModal] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)

  useEffect(() => {
    ;(async () => {
      // 学段名称
      const XDNameList = schoolInfo?.section_name?.split(',') || []
      // 获取年级数据
      const resNJ = (await controllerRepeatGetGrades({
        section: [XDNameList?.[0]],
      })) as ResType<any>
      if (resNJ?.errCode) {
        message.error(resNJ.message || '数据获取失败，请联系管理员或稍后再试')
      } else {
        setNJData(resNJ?.list)
      }
    })()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [schoolInfo?.section_name])
  const handleDownload = async () => {
    setExportModal(true)
    setExportModal(true)
  }
  const queryDetail = async (data: any) => {
    const result = await getAbsentList({
      enterpriseCode: (schoolInfo && schoolInfo.code)!,
      semesterCode: nowTermCode,
      gradeCode: data.gradeCode,
      classCode: data.classCode,
      check_time: data.check_time,
      id: data.id,
    })
    if (result.errCode) {
      message.error(result.message || result.msg || '数据获取失败，请联系管理员或稍后再试')
    } else {
      setModalProps({
        visible: true,
        data: {
          ...data,
          absentList: result || [],
        },
      })
    }
  }
  // useEffect(() => {
  //   ;(async () => {
  //     const result = await getDiseases({})
  //     console.log(result)
  //   })()
  // }, [])

  /** 未填报班级 */
  const notFilled = async () => {
    let unsubmittedData = {}
    if (!nowTermCode) {
      return message.error('请选择学期')
    }
    if (!checkTime) {
      return message.error('请选择查询日期')
    }
    if (!period) {
      return message.error('请选择填报时段')
    }
    try {
      setLoading(true)
      const res = await getUnsubmittedClass({
        enterpriseCode: currentUser?.enterpriseCode,
        semesterCode: nowTermCode,
        check_time: checkTime,
        period,
        gradeCode: gradeCode === 'allgrade' ? undefined : gradeCode,
      })
      if (res?.errCode) {
        return message.error(`获取未填报数据失败，请联系管理员或稍后再试 ${res?.msg}`)
      }
      const gradeClasses: Record<string, string[]> = {}
      res?.split('、').forEach((item: string) => {
        const matchedGrade = grades.find((grade) => item.includes(grade))
        if (matchedGrade) {
          if (!gradeClasses[matchedGrade]) {
            gradeClasses[matchedGrade] = []
          }
          gradeClasses[matchedGrade].push(item)
        }
      })
      unsubmittedData = gradeClasses
    } catch (error) {
      return message.error(`获取未填报数据失败，请联系管理员或稍后再试 ${error}`)
    } finally {
      setLoading(false)
      Modal.info({
        title: '未填报班级',
        width: 800,
        content:
          Object.keys(unsubmittedData).length > 0 ? (
            <div className={styles.unsubmitted}>
              {Object.entries(unsubmittedData ?? {})?.map(([key, value]) => (
                <div key={key}>
                  <div className={styles.title}>{key}</div>
                  <Row gutter={12} className={styles.content}>
                    {value?.map((item) => (
                      <Col className={styles.tag} key={item}>
                        {item}
                      </Col>
                    ))}
                  </Row>
                </div>
              ))}
            </div>
          ) : (
            <Empty description="暂无未填报班级" />
          ),
      })
    }
  }

  return (
    <div>
      <CommonList
        ref={childRef}
        columns={getRecordColums({
          refreshTable: () => {
            ;(childRef?.current as any)?.reload?.()
          },
          queryDetail,
          currentUser,
        })}
        metas={getRecordMetas({
          refreshTable: () => {
            ;(childRef?.current as any)?.reload?.()
          },
          queryDetail,
          currentUser,
        })}
        params={{
          schoolInfo,
          nowTermCode,
          gradeCode,
          checkTime,
          period,
        }}
        request={async (
          // 第一个参数 params 查询表单和 params 参数的结合
          // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
          params: any,
          sort,
          filter,
        ) => {
          // 这里需要返回一个 Promise,在返回之前你可以进行数据转化
          // 如果需要转化参数可以在这里进行修改
          if (nowTermCode) {
            const { pageSize, current: page } = params
            const {
              errCode,
              list = [],
              count = 0,
              total = 0,
            } = (await getNoonStatisticList({
              enterpriseCode: (schoolInfo && schoolInfo.code)!,
              semesterCode: nowTermCode,
              gradeCode: gradeCode === 'allgrade' ? undefined : gradeCode,
              check_time: checkTime || undefined,
              period,
              offset: Number((page || 1) - 1) * Number(pageSize),
              limit: Number(pageSize),
            })) as any
            return {
              success: errCode ? false : true,
              data: list || [],
              total: count || total || 0,
            }
          } else {
            return {
              success: false,
              data: [],
              total: 0,
            }
          }
        }}
        headerTitle={
          <SearchLayout>
            <SemesterSelect
              onChange={(val) => {
                setNowTermCode(val)
              }}
            />
            <div>
              <label htmlFor="nj">查询日期：</label>
              <DatePicker
                defaultValue={moment(new Date())}
                onChange={(date, dateString) => {
                  setCheckTime(dateString)
                }}
                allowClear
              />
            </div>
            <div>
              <label htmlFor="nj">所属年级：</label>
              <Select
                style={{ width: 160 }}
                defaultValue="allgrade"
                placeholder="请选择"
                onChange={(value, key: any) => {
                  setGradeCode?.(value)
                }}
              >
                <Option value="allgrade" key="allgrade">
                  全年级
                </Option>
                {NJData?.map((item: any) => {
                  return (
                    <Option value={item.code} key={item.code}>
                      {getNjName(item?.name, schoolInfo?.section_code)}
                    </Option>
                  )
                })}
              </Select>
            </div>
            <div>
              <label htmlFor="period">填报时段：</label>
              <Select
                allowClear
                placeholder="请选择时段"
                style={{ width: 180 }}
                onChange={(value: string) => {
                  setPeriod(value)
                }}
                options={[
                  { value: '上午', label: '上午' },
                  { value: '下午', label: '下午' },
                ]}
              />
            </div>
          </SearchLayout>
        }
        toolBarRender={
          isMobile
            ? () => {
                return (
                  <Button type={'primary'} onClick={notFilled} loading={loading}>
                    未填报班级
                  </Button>
                )
              }
            : () => {
                return (
                  <Space>
                    <Button type={'primary'} onClick={notFilled} loading={loading}>
                      未填报班级
                    </Button>
                    <Button type={'primary'} onClick={handleDownload}>
                      <ToTopOutlined />
                      导出
                    </Button>
                  </Space>
                )
              }
        }
      />
      <AbsenceModal setModalProps={setModalProps} modalProps={modalProps} />
      <ModalForm
        title="导出"
        open={exportModal}
        onFinish={async (values) => {
          const { dateRange, grade, period: exportPeriod } = values
          try {
            const baseUrl = '/edu_api/download/morning_check/statistic'
            const params = new URLSearchParams()
            params.append('enterpriseCode', schoolInfo?.code || '')
            params.append('enterpriseName', schoolInfo?.name || '')
            params.append('semesterCode', nowTermCode || '')
            params.append('teacherName', currentUser?.realName || '')
            if (grade !== 'allgrade') {
              params.append('gradeCode', grade)
            }
            if (dateRange) {
              params.append('startDate', dateRange?.[0])
              params.append('endDate', dateRange?.[1])
            }
            if (exportPeriod !== '全时段') {
              params.append('period', exportPeriod)
            }

            const url = `${baseUrl}?${params.toString()}`
            const a = document.createElement('a')
            a.href = url
            a.download = '晨午检记录汇总.xlsx'
            document.body.appendChild(a)
            a.click()
            a.remove()
            message.success('导出成功')
            setExportModal(false)
            return true
          } catch (error) {
            message.error('导出失败，请联系管理员或稍后再试！' + error)
            return false
          }
        }}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => {
            setExportModal(false)
          },
        }}
      >
        <ProFormDateRangePicker
          name="dateRange"
          label="起止日期"
          rules={[{ required: true, message: '起止日期不能为空！' }]}
        />
        <ProFormSelect
          name="grade"
          label="年级"
          placeholder="请选择"
          options={[
            { label: '全年级', value: 'allgrade' },
            ...(NJData?.map((item: any) => ({
              label: getNjName(item.name, schoolInfo?.section_code),
              value: item.code,
            })) || []),
          ]}
          rules={[{ required: true, message: '年级不能为空！' }]}
        />
        <ProFormSelect
          name="period"
          label="时段"
          options={[
            {
              label: '全时段',
              value: '全时段',
            },
            {
              label: '上午',
              value: '上午',
            },
            {
              label: '下午',
              value: '下午',
            },
          ]}
          placeholder="请选择"
          rules={[{ required: true, message: '时段不能为空！' }]}
        />
      </ModalForm>
    </div>
  )
}

export default RecordStastic
