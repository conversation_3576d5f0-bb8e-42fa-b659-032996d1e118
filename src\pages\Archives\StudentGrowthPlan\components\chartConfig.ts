export const columnConfig: any = {
  data: [],
  appendPadding: 10,
  xField: 'name',
  yField: 'value',
  seriesField: 'type',
  color: ({ type }: any) => {
    if (type === '良好人数' ||type === '正常人数' || type === '正常率'|| type === '及格率') {
      return 'rgba(35, 137, 255, 1)'
    }
    if (type === '及格人数' ||type === '男生不良人数' || type === '不良率' || type === '不及格率') {
      return 'rgba(13, 204, 204, 1)'
    }
    if(type === '优秀人数'){
      return 'rgba(255, 156, 126, 1)'
    }
    return 'rgba(241, 142, 86, 1)'
  },
  isGroup: true,
  maxColumnWidth: 20,
  columnStyle: {
    radius: [20, 20, 0, 0],
  },
  legend: false,
}
export const lineConfig: any = {
  appendPadding: 10,
  data: [],
  xField: 'date',
  yField: 'value',
  seriesField: 'eye',
  tooltip: false,
  legend: {
    position: 'bottom',
  },
  yAxis: {
    label: {
      // 数值格式化为千分位
      formatter: (v: any) => `${Number(v) === 0 ? 0 : Number(v).toFixed(1)}`,
    },
    // show: false
  },
}
export const pieConfig: any = {
  data: [],
  angleField: 'value',
  colorField: 'type',
  color: ['#2e8fff', '#19cfcf', '#f2945f'],
  radius: 0.8,
  innerRadius: 0.6,
  // legend: {
  //   position: 'bottom',
  // },
  label: {
    type: 'inner',
    offset: '-50%',
    content: '{value}',
    style: {
      textAlign: 'center',
      fontSize: 12,
    },
  },
  interactions: [
    {
      type: 'element-selected',
    },
    {
      type: 'element-active',
    },
  ],
}
