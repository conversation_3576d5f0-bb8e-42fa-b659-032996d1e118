import {
  ModalForm,
  ProDescriptions,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components'
import { Divider, Form, Space, message } from 'antd'
import type { Dispatch } from 'react'
import React, { useEffect } from 'react'
import { useModel } from 'umi'
import { auditFinish, auditLeave } from '@/services/edu-platform-web/infection_list'

import styles from './index.less'
import { getFileById } from '@/services/edu-platform-web/upload'
import moment from 'moment'

const formLayout = {
  labelCol: { flex: '8em' },
}
const Audit = (props: {
  current: any
  modalVisible: boolean
  setPreviewImage: Dispatch<any>
  onClose: () => void
  reload?: () => void
}) => {
  const { initialState } = useModel('@@initialState')
  const { currentUser } = initialState || {}
  const [form] = Form.useForm()
  const { current, reload, modalVisible, setPreviewImage, onClose } = props
  useEffect(() => {
    if (modalVisible && form) {
      form.setFieldsValue({
        status: '已同意',
        finshedStatus: '2',
        isolationDays: current?.isolationDays,
      })
    }
  }, [modalVisible, current, form])
  const handleSubmit = async (values: any) => {
    const endTime = moment(current?.startTime)
      .add({ days: values?.isolationDays || 0 })
      .format('YYYY/MM/DD 23:59')
    const res =
      current?.status === '待审批'
        ? await auditLeave(
            { id: current.id },
            {
              ...values,
              isolationDays: values?.isolationDays || 0,
              approvalOpinion: values?.approvalOpinion || null,
              endTime: new Date(endTime).toString(),
              approvalUser: currentUser?.realName,
              approvalUserCode: currentUser?.userCode,
              approvalTime: new Date(),
              enterpriseName:
                values?.status === '已同意' ? currentUser?.enterprise?.name : undefined,
            },
          )
        : ((await auditFinish(
            { id: current.id },
            {
              ...values,
              finshedOpinion: values?.finshedOpinion || null,
              approvalUser: currentUser?.realName,
              approvalUserCode: currentUser?.userCode,
              approvalTime: new Date(),
            },
          )) as any)
    if (res?.errCode) {
      message.error(res?.message || '审核失败，请联系管理员或稍后再试')
      return false
    } else {
      message.success('审核成功')
      onClose?.()
      reload?.()
      return true
    }
  }
  return (
    <>
      <ModalForm
        title={current?.finshedStatus === '1' ? `销假流程审批` : `请假流程审批`}
        form={form}
        modalProps={{
          centered: true,
          destroyOnClose: true,
          className: styles.auditModal,
          getContainer: false,
          onCancel: () => {
            onClose?.()
          },
        }}
        visible={modalVisible}
        onVisibleChange={(visible: boolean) => {
          if (!visible) {
            form.resetFields()
          }
        }}
        width="650px"
        onFinish={async (values) => {
          const flag = handleSubmit(values)
          return flag
        }}
        layout="horizontal"
        {...formLayout}
      >
        {current?.finshedStatus === '1' ? (
          <ProDescriptions column={1} title={current?.infection_type?.jb} dataSource={current}>
            <ProDescriptions.Item valueType="date" dataIndex="startTime" label="学生确诊日期" />
            <ProDescriptions.Item valueType="text" dataIndex="isolationDays" label="要求隔离天数" />
            <ProDescriptions.Item valueType="dateTime" dataIndex="updatedAt" label="申请销假时间" />
            <ProDescriptions.Item valueType="text" dataIndex="finshedReason" label="销假说明" />
            <ProDescriptions.Item label="康复证明">
              <Space>
                {current.finshedAnnex?.split(',').map((val: string, index: number) => (
                  <a
                    key={val}
                    href="#"
                    onClick={async () => {
                      const result = await getFileById(val)
                      if (!result?.errCode) {
                        setPreviewImage({
                          imgUrl: result.url,
                          show: true,
                          title: '图片预览',
                        })
                      } else {
                        message.warning('文件已过期或已损坏')
                      }
                    }}
                  >
                    附件{index + 1}
                  </a>
                ))}
              </Space>
            </ProDescriptions.Item>
          </ProDescriptions>
        ) : (
          <ProDescriptions
            column={1}
            title={current?.infection_type?.jb}
            dataSource={
              current && {
                startTime: current.startTime,
                ...current.infection_type,
              }
            }
          >
            <ProDescriptions.Item valueType="date" dataIndex="startTime" label="学生确诊日期" />
            {current?.infection_type?.glsx && (
              <ProDescriptions.Item valueType="text" dataIndex="glsx" label="隔离时限描述" />
            )}
            <ProDescriptions.Item valueType="text" dataIndex="glts" label="建议隔离天数" />
            <ProDescriptions.Item valueType="text" dataIndex="zcwj" label="政策文件" />
          </ProDescriptions>
        )}
        <Divider />
        <ProFormRadio.Group
          name={current?.status === '待审批' ? 'status' : 'finshedStatus'}
          label="审批意见"
          options={[
            {
              label: '同意',
              value: current?.status === '待审批' ? '已同意' : '2',
            },
            {
              label: '不同意',
              value: current?.status === '待审批' ? '已拒绝' : '3',
            },
          ]}
        />
        {current?.status === '待审批' && (
          <ProFormText
            name="isolationDays"
            label="隔离天数"
            proFieldProps={{ mode: 'edit' }}
            fieldProps={{ type: 'number', addonAfter: '天', min: 0, max: 365, allowClear: false }}
          />
        )}
        <ProFormDependency name={[current?.status === '待审批' ? 'status' : 'finshedStatus']}>
          {({ status, finshedStatus }) => {
            return (
              <ProFormTextArea
                name={current?.status === '待审批' ? 'approvalOpinion' : 'finshedOpinion'}
                label="审批说明"
                placeholder={'字符最长不应超过255'}
                fieldProps={{
                  maxLength: 255,
                  showCount: true,
                  autoSize: { minRows: 3, maxRows: 6 },
                }}
                rules={
                  status === '已拒绝' || finshedStatus === '3'
                    ? [{ required: true, message: '请输入审批说明' }]
                    : []
                }
              />
            )
          }}
        </ProFormDependency>
      </ModalForm>
    </>
  )
}

export default Audit
