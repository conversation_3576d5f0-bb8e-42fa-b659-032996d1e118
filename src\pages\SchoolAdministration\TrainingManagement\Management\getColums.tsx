import { teacherTrainUpdate } from '@/services/edu-platform-web/teacherTrain'
import type { ProColumns } from '@ant-design/pro-components'
import { Button, Input, Modal, Popconfirm, Space, Tag, Typography, message } from 'antd'
import moment from 'moment'
import { history } from 'umi'
const styleP = {
  marginBottom: 0,
  lineHeight: '28px',
}
const { Paragraph } = Typography
const { TextArea } = Input
const getColums = ({
  type,
  refreshTable,
  currentUser,
}: {
  type?: string
  refreshTable: () => void
  currentUser?: UserInfo | null
}): ProColumns<any>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    width: 58,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '教师姓名',
    dataIndex: 'realName',
    key: 'realName',
    align: 'center',
    width: 80,
    fixed: 'left',
  },
  {
    title: '培训起止时间',
    dataIndex: 'work_record',
    key: 'work_record',
    width: 160,
    align: 'center',
    render: (_, record: any) => {
      return (
        moment(record?.start_time).format('MM/DD HH:mm') +
        '至' +
        moment(record?.end_time).format('MM/DD HH:mm')
      )
    },
  },
  {
    title: '学时',
    dataIndex: 'class_hour',
    key: 'class_hour',
    width: 60,
    align: 'center',
  },
  {
    title: '培训地点',
    dataIndex: 'address',
    key: 'address',
    ellipsis: true,
    width: 120,
    align: 'center',
  },
  {
    title: '培训主题或内容',
    dataIndex: 'train_theme',
    key: 'train_theme',
    align: 'center',
    width: 150,
    render: (_, record) => {
      return (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: true,
            onEllipsis: (ellipsis) => {
              console.log('Ellipsis changed:', ellipsis)
            },
          }}
          style={{
            marginBottom: 0,
          }}
          title={`${record?.train_theme}`}
        >
          {record?.train_theme}
        </Paragraph>
      )
    },
  },
  {
    title: '申请时间',
    dataIndex: 'sq_time',
    key: 'sq_time',
    align: 'center',
    valueType: 'dateTime',
    width: 130,
  },
  {
    title: '审批人',
    dataIndex: 'sp_realName',
    key: 'sp_realName',
    align: 'center',
    hideInTable: type === 'audit',
    width: 90,
  },
  {
    title: '审批时间',
    dataIndex: 'sp_time',
    key: 'sp_time',
    align: 'center',
    hideInTable: type === 'audit',
    valueType: 'dateTime',
    width: 130,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    hideInTable: type === 'audit',
    width: 80,
    fixed: 'right',
    render: (_, record: any) => (
      <Tag
        color={
          record?.status === '已同意'
            ? '#87d068'
            : record?.status === '已拒绝'
            ? '#f50'
            : record?.status === '待审批'
            ? '#108ee9'
            : record?.status === '已结束'
            ? '#999999'
            : 'default'
        }
        style={{
          marginInlineEnd: 0,
        }}
      >
        {record.status}
      </Tag>
    ),
  },
  {
    title: '操作',
    key: 'option',
    width: type === 'audit' ? 150 : 80,
    fixed: 'right',
    render: (_: any, record: any) => (
      <>
        {record.status === '待审批' && (
          <>
            <Popconfirm
              key="agree"
              title={'确定同意' + record?.realName + '的培训登记吗？'}
              onConfirm={async () => {
                if (record.id) {
                  const res = (await teacherTrainUpdate(record.id, {
                    status: '已同意',
                    sp_time: new Date(),
                    sp_userCode: currentUser?.userCode,
                    sp_realName: currentUser?.realName,
                    sp_username: currentUser?.username,
                  })) as ResType<any>
                  if (res?.errCode) {
                    message.error(res.message || '操作失败，请联系管理员或稍后再试')
                  } else {
                    message.success('操作成功')
                    refreshTable?.()
                  }
                }
              }}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Button
                type="link"
                style={{
                  cursor: 'pointer',
                }}
              >
                同意
              </Button>
            </Popconfirm>
            <Button
              type="link"
              danger
              onClick={async () => {
                if (record.id) {
                  // 审核意见
                  let approvalOpinion: string
                  Modal.confirm({
                    title: `确定拒绝${record.realName}的培训登记吗？`,
                    content: (
                      <TextArea
                        placeholder="审批意见"
                        autoSize={{ minRows: 3, maxRows: 6 }}
                        maxLength={100}
                        showCount
                        onChange={(e) => {
                          approvalOpinion = e.target.value
                        }}
                      />
                    ),
                    onOk: async () => {
                      const res = await teacherTrainUpdate(record.id, {
                        status: '已拒绝',
                        sp_time: new Date(),
                        sp_result: approvalOpinion,
                        sp_userCode: currentUser?.userCode,
                        sp_realName: currentUser?.realName,
                        sp_username: currentUser?.username,
                      })
                      if (res?.errCode) {
                        message.error('操作失败，请联系管理员或稍后再试')
                      } else {
                        message.success('操作成功')
                        refreshTable?.()
                      }
                    },
                  })
                }
              }}
              style={{
                cursor: 'pointer',
              }}
            >
              拒绝
            </Button>
          </>
        )}
        <Button
          type="text"
          onClick={() => {
            sessionStorage.setItem('teacherTrainDetail', 'true')
            history.push({
              pathname: '/schoolAdministration/pxgl/management/detail',
              query: {
                id: record.id,
              },
            })
          }}
        >
          查看
        </Button>
      </>
    ),
    align: 'center',
  },
]

const getMetas = ({
  type,
  refreshTable,
  currentUser,
}: {
  type?: string
  refreshTable: () => void
  currentUser?: UserInfo | null
}): any => {
  return {
    title: {
      dataIndex: 'teacherName',
      render: (dom: React.ReactNode, entity: any) => {
        return `${entity?.realName}提交的培训登记`
      },
    },
    subTitle: {
      render: (_dom: React.ReactNode, entity: any) => {
        return (
          <Tag
            color={
              entity?.status === '已同意'
                ? '#87d068'
                : entity?.status === '已拒绝'
                ? '#f50'
                : entity?.status === '待审批'
                ? '#108ee9'
                : entity?.status === '已结束'
                ? '#999999'
                : 'default'
            }
          >
            {entity.status}
          </Tag>
        )
      },
    },
    description: {
      render: (dom: React.ReactNode, entity: any) => {
        return (
          <div>
            <p style={styleP}>培训主题：{entity?.train_theme}</p>
            <p style={styleP}>
              申请时间: {entity?.sq_time ? moment(entity?.sq_time).format('YYYY.MM.DD HH:mm') : '-'}
            </p>
            <p style={styleP}>
              培训起止时间: {moment(entity?.start_time).format('MM/DD HH:mm')}至
              {moment(entity?.end_time).format('MM/DD HH:mm')}
            </p>
          </div>
        )
      },
    },
    actions: {
      render: (
        _: any,
        row: {
          status: string
          realName: string
          id: any
        },
      ) => {
        return (
          <Space>
            {row.status === '待审批' && (
              <>
                <Popconfirm
                  key="agree"
                  title={'确定同意' + row?.realName + '的培训登记吗？'}
                  onConfirm={async () => {
                    if (row.id) {
                      const res = (await teacherTrainUpdate(row.id, {
                        status: '已同意',
                        sp_time: new Date(),
                        sp_userCode: currentUser?.userCode,
                        sp_realName: currentUser?.realName,
                        sp_username: currentUser?.username,
                      })) as ResType<any>
                      if (res?.errCode) {
                        message.error(res.message || '操作失败，请联系管理员或稍后再试')
                      } else {
                        message.success('操作成功')
                        refreshTable?.()
                      }
                    }
                  }}
                  okText="确定"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Button
                    type="link"
                    style={{
                      cursor: 'pointer',
                    }}
                  >
                    同意
                  </Button>
                </Popconfirm>
                <Button
                  type="link"
                  danger
                  onClick={async () => {
                    if (row.id) {
                      // 审核意见
                      let approvalOpinion: string
                      Modal.confirm({
                        title: `确定拒绝${row.realName}的培训登记吗？`,
                        content: (
                          <TextArea
                            placeholder="审批意见"
                            autoSize={{ minRows: 3, maxRows: 6 }}
                            maxLength={100}
                            showCount
                            onChange={(e) => {
                              approvalOpinion = e.target.value
                            }}
                          />
                        ),
                        onOk: async () => {
                          const res = await teacherTrainUpdate(row.id, {
                            status: '已拒绝',
                            sp_time: new Date(),
                            sp_result: approvalOpinion,
                            sp_userCode: currentUser?.userCode,
                            sp_realName: currentUser?.realName,
                            sp_username: currentUser?.username,
                          })
                          if (res?.errCode) {
                            message.error('操作失败，请联系管理员或稍后再试')
                          } else {
                            message.success('操作成功')
                            refreshTable?.()
                          }
                        },
                      })
                    }
                  }}
                  style={{
                    cursor: 'pointer',
                  }}
                >
                  拒绝
                </Button>
              </>
            )}
            <Button
              type="text"
              onClick={() => {
                history.push({
                  pathname: '/schoolAdministration/pxgl/management/detail',
                  query: {
                    id: row.id,
                  },
                })
              }}
            >
              查看
            </Button>
          </Space>
        )
      },
    },
  }
}

export { getColums, getMetas }
