/*
 * @description:
 * @author: wuzhan
 * @Date: 2022-09-26 11:54:11
 * @LastEditTime: 2024-02-01 17:42:37
 * @LastEditors: zpl
 */
import type { ProColumns } from '@ant-design/pro-components'
import { ProTable } from '@ant-design/pro-components'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { paginationConfig } from '@/constant'
import { getNjName, getTableWidth } from '@/utils'
import { Button, Drawer, Input, message, Modal, Popconfirm, Space, Tag, Tooltip } from 'antd'
import SearchLayout from '@/components/Search/Layout'
import SemesterSelect from '@/components/SemesterSelect'
import Analyse from './components/Analyse'
import { useModel, useAccess } from 'umi'
import { getList } from '@/services/score-analysis/examination'
import EllipsisHint from '@/components/EllipsisHint'
import QualityAnalyse from './components/QualityAnalyse'
const { Search } = Input
import styles from './index.less'
import SummaryGrades from './components/SummaryGrades'
const analyseAuth = [
  ENV_CJFX_FX,
  ENV_GL,
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
]
const GradeAnalyse = () => {
  const { initialState } = useModel('@@initialState')
  const { schoolInfo, currentUser } = initialState || {}
  const childRef = useRef()
  const { isAdministration } = useAccess()
  const [dataSource, setDataSource] = useState<any[]>()
  const [termCode, setTermCode] = useState<string>()
  const [termLock, setTermLock] = useState<number>()
  const [title, setTitle] = useState<string>()
  const [editStatus, setEditStatus] = useState<string>()
  const [drawProps, setDrawProps] = useState<{
    open: boolean
    type: string
    title: string
    subType?: string
    current: any
  }>()
  const [summaryGradesProps, setSummaryGradesProps] = useState<{
    open: boolean
    type: 'school' | 'grade' | 'class' | undefined
    title: string
    data: any
  }>()

  const getData = useCallback(async () => {
    if (schoolInfo?.code && termCode) {
      const res = (await getList({
        /** 标题  */
        name: title,
        teacherCode: isAdministration(ENV_CJFX, ENV_BJZL) ? currentUser?.userCode : undefined,
        /** 日期  */
        // date: string;
        /** 学年学期编码  */
        semesterCode: termCode,
        /** 学校编码  */
        enterpriseCode: schoolInfo?.code,
      })) as ResType<any>
      if (res.errCode) {
        message.error(res.message || '数据获取失败，请联系管理员或稍后再试')
      } else {
        setDataSource(res.list)
      }
    }
  }, [schoolInfo?.code, termCode, title])

  useEffect(() => {
    getData()
  }, [getData])

  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      width: 58,
      align: 'center',
    },
    {
      title: '考试名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      align: 'center',
      width: 180,
    },
    {
      title: '考试时间',
      dataIndex: 'date',
      key: 'date',
      ellipsis: true,
      align: 'center',
      width: 80,
    },
    {
      title: '参考年级',
      dataIndex: 'gxamGrads',
      key: 'gxamGrads',
      ellipsis: true,
      align: 'center',
      width: 180,
      render: (_, record) => {
        const arr = record?.examGrads?.sort(
          (a: { gradeCode: string }, b: { gradeCode: string }) =>
            parseInt(a.gradeCode + '') - parseInt(b.gradeCode + ''),
        )
        return (
          <EllipsisHint
            width="100%"
            text={arr?.map((item: any) => {
              return (
                <Tag key={item} style={{ color: '#333', margin: '0 .25rem' }}>
                  {getNjName(item?.gradeName, schoolInfo?.section_code)}
                </Tag>
              )
            })}
          />
        )
      },
    },
    {
      title: '数据更新时间',
      key: 'editTime',
      dataIndex: 'editTime',
      valueType: 'dateTime',
      align: 'center',
      width: 100,
      render: (dom, record) => {
        const editTime = new Date(record.editTime || 0).getTime()
        const analysisTime = new Date(record.analysisTime || 0).getTime()
        const time = editTime - analysisTime
        if (time > 0) {
          return (
            <Tooltip title="请注意，分析完成后成绩发生了变动，建议重新分析">
              <span style={{ color: 'red' }}>{dom}</span>
            </Tooltip>
          )
        }
        return dom
      },
    },
    {
      title: '分析时间',
      key: 'analysisTime',
      dataIndex: 'analysisTime',
      valueType: 'dateTime',
      align: 'center',
      width: 100,
      render: (dom, record) => {
        const editTime = new Date(record.editTime || 0).getTime()
        const analysisTime = new Date(record.analysisTime || 0).getTime()
        const time = editTime - analysisTime
        if (time > 0) {
          return (
            <Tooltip title="请注意，分析完成后成绩发生了变动，建议重新分析">
              <span style={{ color: 'red' }}>{dom}</span>
            </Tooltip>
          )
        }
        return dom
      },
    },
    // {
    //   title: '学年学期',
    //   dataIndex: 'semesterName',
    //   key: 'semesterName',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 180,
    // },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      width: 160,
      render: (text, record) => {
        const flag = !!analyseAuth.map((qx) => isAdministration(ENV_CJFX, qx)).find((qx) => qx)
        return (
          <>
            {flag && (
              <Button
                type="link"
                key="analyse"
                style={{ margin: '0' }}
                onClick={() => {
                  setDrawProps({
                    title: `${record?.name}成绩分析`,
                    type: 'analyse',
                    open: true,
                    current: { ...record, termLock },
                  })
                }}
              >
                成绩分析
              </Button>
            )}
            {(isAdministration(ENV_CJFX, ENV_BJZL) ||
              isAdministration(ENV_CJFX, ENV_NJZL) ||
              isAdministration(ENV_CJFX, ENV_QXZL) ||
              isAdministration(ENV_CJFX, ENV_GL)) && (
              <Button
                type="link"
                key="quality"
                style={{ margin: '0' }}
                onClick={() => {
                  if (record?.is_Analysis) {
                    const authArr = [
                      {
                        label: '学校',
                        value: isAdministration(ENV_CJFX, ENV_QXZL),
                      },
                      {
                        label: '年级',
                        value: isAdministration(ENV_CJFX, ENV_NJZL),
                      },
                      {
                        label: '班级',
                        value: isAdministration(ENV_CJFX, ENV_BJZL),
                      },
                    ].filter((v) => !!v.value)
                    if (authArr?.length > 1) {
                      Modal.info({
                        title: '请选择进入的质量分析类型',
                        okText: '关闭',
                        width: 500,
                        content: (
                          <div style={{ display: 'flex' }}>
                            {authArr.map((key) => (
                              <div
                                key={key?.label}
                                style={{
                                  padding: '0 1rem',
                                  flex: '1',
                                  textAlign: 'center',
                                }}
                              >
                                <div
                                  style={{
                                    lineHeight: '2.5rem',
                                  }}
                                >
                                  {key?.label}质量分析
                                </div>
                                <Button
                                  onClick={() => {
                                    setDrawProps({
                                      title: `${record?.name}质量分析`,
                                      type: 'quality',
                                      open: true,
                                      subType: key?.label,
                                      current: { ...record, termLock },
                                    })
                                    Modal.destroyAll()
                                  }}
                                >
                                  点击进入
                                </Button>
                              </div>
                            ))}
                          </div>
                        ),
                      })
                    } else {
                      setDrawProps({
                        title: `${record?.name}质量分析`,
                        type: 'quality',
                        open: true,
                        subType: authArr?.[0].label,
                        current: { ...record, termLock },
                      })
                    }
                  } else {
                    Modal.warning({
                      title: '友情提示',
                      content: '请先进行成绩分析后再填写质量分析',
                    })
                  }
                }}
              >
                质量分析
              </Button>
            )}
            {(isAdministration(ENV_CJHZ, ENV_QXHZ) ||
              isAdministration(ENV_CJHZ, ENV_NJHZ) ||
              isAdministration(ENV_CJHZ, ENV_BJHZ)) && (
              <Button
                type="link"
                key="quality"
                style={{ margin: '0' }}
                onClick={() => {
                  if (record?.is_Analysis) {
                    const authArr = [
                      {
                        label: '学校',
                        value: isAdministration(ENV_CJHZ, ENV_QXHZ),
                      },
                      {
                        label: '年级',
                        value: isAdministration(ENV_CJHZ, ENV_NJHZ),
                      },
                      {
                        label: '班级',
                        value: isAdministration(ENV_CJHZ, ENV_BJHZ),
                      },
                    ].filter((v) => !!v.value)

                    if (authArr?.length > 1) {
                      Modal.info({
                        title: '请选择进入的成绩汇总类型',
                        okText: '关闭',
                        width: 500,
                        content: (
                          <div style={{ display: 'flex' }}>
                            {authArr.map((key) => (
                              <div
                                key={key?.label}
                                style={{
                                  padding: '0 1rem',
                                  flex: '1',
                                  textAlign: 'center',
                                }}
                              >
                                <div
                                  style={{
                                    lineHeight: '2.5rem',
                                  }}
                                >
                                  {key?.label}成绩汇总
                                </div>
                                <Button
                                  onClick={() => {
                                    setSummaryGradesProps({
                                      title: `${record?.name}成绩汇总`,
                                      type:
                                        key?.label === '学校'
                                          ? 'school'
                                          : key?.label === '年级'
                                          ? 'grade'
                                          : 'class',
                                      open: true,
                                      data: { ...record, termLock },
                                    })
                                    Modal.destroyAll()
                                  }}
                                >
                                  点击进入
                                </Button>
                              </div>
                            ))}
                          </div>
                        ),
                      })
                    } else {
                      setSummaryGradesProps({
                        title: `${record?.name}成绩汇总`,
                        type: isAdministration(ENV_CJHZ, ENV_QXHZ)
                          ? 'school'
                          : isAdministration(ENV_CJHZ, ENV_NJHZ)
                          ? 'grade'
                          : 'class',
                        open: true,
                        data: { ...record, termLock },
                      })
                    }
                  } else {
                    Modal.warning({
                      title: '友情提示',
                      content: '请先进行成绩分析后再查看成绩汇总',
                    })
                  }
                }}
              >
                成绩汇总
              </Button>
            )}

            {/* <Analyse key="analyse" current={{ ...record, termLock }} /> */}
            {/* <QualityAnalyse key="quality" current={{ ...record, termLock }} /> */}
          </>
        )
      },
    },
  ]

  return (
    <>
      <ProTable<any>
        rowKey="id"
        pagination={paginationConfig}
        scroll={{ x: getTableWidth(columns) }}
        dataSource={dataSource}
        columns={columns}
        search={false}
        options={{
          setting: false,
          fullScreen: false,
          density: false,
          reload: false,
        }}
        headerTitle={
          <SearchLayout>
            <SemesterSelect
              onChange={(val, _title, lock) => {
                setTermCode(val)
                setTermLock(lock)
              }}
            />
            <div>
              <label htmlFor="title">标题：</label>
              <Search
                allowClear
                onSearch={(val) => {
                  setTitle(val)
                }}
              />
            </div>
          </SearchLayout>
        }
      />
      <Drawer
        className={styles.analyseModal}
        destroyOnClose
        title={drawProps?.title}
        width="90vw"
        open={drawProps?.open || false}
        footer={
          drawProps?.type === 'quality' ? (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
              }}
            >
              <Space>
                <Button
                  key="save"
                  type="dashed"
                  onClick={() => {
                    ;(childRef?.current as any)?.handleCancel?.()
                    setDrawProps(undefined)
                  }}
                >
                  关闭
                </Button>
                {editStatus === 'check' && (
                  <Popconfirm
                    title="确定撤销当前版本提交，恢复为草稿状态吗？"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={() => {
                      ;(childRef?.current as any)?.handleRevert?.()
                    }}
                  >
                    <Button danger>撤销</Button>
                  </Popconfirm>
                )}
                {editStatus !== 'check' && (
                  <Button
                    key="save"
                    type="default"
                    onClick={() => {
                      ;(childRef?.current as any)?.handleSave?.()
                    }}
                  >
                    保存
                  </Button>
                )}
                {editStatus !== 'check' && (
                  <Button
                    key="ok"
                    type="primary"
                    onClick={() => {
                      ;(childRef?.current as any)?.handleSubmit?.()
                    }}
                  >
                    提交
                  </Button>
                )}
              </Space>
            </div>
          ) : (
            false
          )
        }
        onClose={() => {
          setDrawProps(undefined)
          setEditStatus(undefined)
        }}
      >
        {drawProps?.type === 'analyse' && (
          <Analyse key="analyse" current={drawProps?.current} refresh={getData} />
        )}
        {drawProps?.type === 'quality' && (
          <QualityAnalyse
            key="quality"
            subType={drawProps?.subType}
            current={drawProps?.current}
            setEditStatus={setEditStatus}
            onClose={() => {
              setDrawProps(undefined)
              setEditStatus(undefined)
            }}
            ref={childRef}
          />
        )}
      </Drawer>
      {/* 成绩汇总 */}
      <Drawer
        className={styles.summaryGrades}
        destroyOnClose
        title={summaryGradesProps?.title}
        width="90vw"
        open={summaryGradesProps?.open || false}
        onClose={() => {
          setSummaryGradesProps({ open: false, type: undefined, title: '', data: undefined })
        }}
      >
        <SummaryGrades data={summaryGradesProps} />
      </Drawer>
    </>
  )
}

export default GradeAnalyse
